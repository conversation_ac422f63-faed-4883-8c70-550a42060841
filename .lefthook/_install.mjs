#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync, mkdirSync, chmodSync } from 'fs';
import process from 'node:process';

// Check if lefthook is disabled
if (process.env.LEFTHOOK === '0') {
  console.log('LEFTHOOK=0 detected - skipping lefthook installation');
  process.exit(0);
}

console.log('Installing lefthook custom hooks...');

try {
  // Set git config to use a custom hooks directory.
  console.log('Setting git hooks path to .lefthook/hooks');
  execSync('git config core.hooksPath .lefthook/hooks', { stdio: 'inherit' });

  // Create the hooks directory if it doesn't exist
  const hooksDir = '.lefthook/hooks';
  if (!existsSync(hooksDir)) {
    console.log(`Creating hooks directory: ${hooksDir}`);
    mkdirSync(hooksDir, { recursive: true });
  }

  // List of hook files for different platforms
  const unixHookFiles = [
    '.lefthook/hooks/pre-commit',
    '.lefthook/hooks/pre-push'
  ];

  const windowsHookFiles = [
    '.lefthook/hooks/pre-commit.cmd',
    '.lefthook/hooks/pre-push.cmd'
  ];

  // Make hook files executable based on the platform.
  if (process.platform === 'win32') {
    // Windows: Check that .cmd files exist (they don't need chmod)
    console.log('Windows detected - checking .cmd files exist');
    windowsHookFiles.forEach(hookFile => {
      if (existsSync(hookFile)) {
        console.log(`✅ Found ${hookFile}`);
      } else {
        console.log(`Warning: ${hookFile} does not exist yet`);
      }
    });
  } else {
    // Unix/Mac: Make extensionless files executable
    console.log('Unix/Mac detected - making shell scripts executable');
    unixHookFiles.forEach(hookFile => {
      if (existsSync(hookFile)) {
        console.log(`Making ${hookFile} executable`);
        chmodSync(hookFile, 0o755);
      } else {
        console.log(`Warning: ${hookFile} does not exist yet`);
      }
    });
  }

  console.log('✅ Lefthook custom hooks installation complete');

} catch (error) {
  console.error('❌ Failed to install lefthook custom hooks');
  console.error('Error:', error.message);
  process.exit(1);
}