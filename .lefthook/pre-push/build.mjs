#!/usr/bin/env node

import { execSync } from 'child_process';
import process from 'node:process';

function prePushRunBuild() {
  try {
    // Check for Docker-related files in staged and unstaged changes
    const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' }).trim();
    const unstagedFiles = execSync('git diff --name-only', { encoding: 'utf8' }).trim();

    const allFiles = `${stagedFiles}\n${unstagedFiles}`;
    const dockerPattern = /^(Dockerfile|docker-compose.*\.yml|docker-compose.*\.yaml)$/m;

    if (dockerPattern.test(allFiles)) {
      console.log('Docker file changes detected, running docker build...');
      execSync('make build', { stdio: 'inherit' });
    } else {
      console.log('No Docker file changes detected, running npm build...');
      execSync('npm run build', { stdio: 'inherit' });
    }
  } catch (error) {
    console.error(`❌ Build Failure: ${error.message}`);
    process.exit(1);
  }
}

prePushRunBuild();
