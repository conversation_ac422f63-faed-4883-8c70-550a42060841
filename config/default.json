{"application": {"port": 3000, "corsWhitelist": [], "rootPath": "/", "cookie": {"prefix": "cms-api-base", "secure": true, "httpOnly": false}}, "logging": {"logNames": {"audit": "cms-audit", "server": "cms-server"}, "loggers": [{"name": "cms-server", "level": "info"}, {"name": "cms-audit", "level": "info"}]}, "roles": {"debugger": ["BDTI", "CZWK", "G72B", "KLFN", "KZ3Y", "MIGS", "R7H1", "VW9K"]}, "secretStrings": ["CEDAR-DEV-LDAP-Connect-System-Account-2", "SparxDatabasesAccounts-DEV", "Sparx_API_Dev_User", "cedar-dev-api-key", "cedar-dev-auth-tokens", "dev/SAPCoreSession", "local/SAPCoreDatabase"], "seedDb": false, "systems": {"apikey": {"secretString": "cedar-dev-api-key"}, "auth": {"secretString": "cedar-dev-auth-tokens"}, "core": {"dataSource": "core", "dbSecretString": "local/SAPCoreDatabase"}, "ldap": {"secretString": "CEDAR-DEV-LDAP-Connect-System-Account-2"}, "session": {"dataSource": "core", "secretString": "dev/SAPCoreSession"}, "sparxea": {"dataSource": "sparxea", "dbSecretString": "SparxDatabasesAccounts-DEV", "apiSecretString": "Sparx_API_Dev_User"}, "cedarSupport": {"dataSource": "cedarSupport", "dbSecretString": "SparxDatabasesAccounts-DEV", "apiSecretString": "Sparx_API_Dev_User"}}}