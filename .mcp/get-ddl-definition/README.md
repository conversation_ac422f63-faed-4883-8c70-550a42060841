# DDL Definition MCP Server

This comprehensive MCP (Model Context Protocol) server can search through DDL files and provide detailed understanding of database objects. The server is designed to work with structured DDL files in the `docs/database` directory.

## Features

- **Search DDL Objects**: Search for database objects by name, type, or database
- **Get DDL Definition**: Get detailed DDL definition and documentation for specific objects
- **List DDL Objects**: List all available database objects with optional filtering
- **Get Database Table Relationships**: Retrieve defined relationships between tables, or get direct relationships for a specific table

## Testing
* `watchexec -i "dist/*" -- yarn build && node ./dist/main.js`
* `npx @modelcontextprotocol/inspector node dist/main.js`

## Note:
Zod 3 is required, but not documented in the modelcontextprotocol docs.

## DDL Indexing System

- **Automatic Discovery**: Scans all DDL files in `docs/database` subdirectories.
- **Smart Parsing**: Extracts object names from various naming conventions:
  - `dbo_[ObjectName].sql` - Standard objects
  - `dbo_SP_[ProcedureName].sql` - Stored procedures
  - `FY22_[ObjectName].sql` - FY22 specific objects
- **Documentation Extraction**: Parses embedded documentation including:
  - Description/Purpose
  - Author and Date
  - Parameters (for procedures)
  - Dependencies
  - Update History

## Relationship System

- **Relationship Discovery**: Automatically loads and indexes relationship definitions from `relationships.ts` files
- **Direct Relationships**: When querying a specific table, returns only tables with direct relationships (one level deep)
- **Relationship Types**: Supports one-to-one, one-to-many, many-to-one, and many-to-many relationships
- **Confidence Levels**: Each relationship includes confidence indicators (high, medium, low)
- **Bidirectional Analysis**: Finds both outgoing relationships (what the table references) and incoming relationships (what references the table)

### Relationship Data Structure
Each relationship includes:
- **To**: Target table name
- **Type**: Relationship type (one-to-one, one-to-many, many-to-one, many-to-many)
- **Source Column**: Column in the source table
- **Target Column**: Column in the target table
- **Confidence**: Confidence level of the relationship definition

## Supported Databases

- `sparxdb`: 100 tables, 7 stored procedures, 2 views
- `sparx_support`: 93 stored procedures, 146 views
- `cedar_support`: 31 views
- `cedar_support.cedar_api`: Additional API objects

## Object Types

- **Views**: Database views (e.g., `Sparx_System`, `FY22_System_View`)
- **Procedures**: Stored procedures (e.g., `SP_Update_SystemExchange`, `SP_Insert_Person`)
- **Tables**: Database tables

## Usage Examples

### Search for a specific object
```
search_ddl_objects with query "Sparx_System"
```
Returns: Object details including description, author, date, and file location.

### Get complete DDL definition
```
get_ddl_definition with name "SP_Update_SystemExchange" and database "sparx_support"
```
Returns: Full DDL definition with complete SQL code, parameter list, dependencies, update history, and metadata.

### List all views in a database
```
list_ddl_objects with type "view" and database "sparx_support"
```
Returns: Organized list of all views with counts.

### Get all table relationships for a database
```
get_database_table_relationships with database "sparxdb"
```
Returns: Complete list of all table relationships defined in the database.

### Get direct relationships for a specific table
```
get_database_table_relationships with database "sparxdb" and tableName "Sparx_System"
```
Returns: Only the specified table and tables that have direct relationships with it (both incoming and outgoing relationships).

## Technical Implementation

### Architecture
```
DDLIndex Class
├── Automatic file discovery
├── Smart object name extraction
├── Documentation parsing
├── Multi-key indexing for flexible lookup
└── Search and retrieval methods
```

### Key Features
- **Lazy Indexing**: Only indexes files when first accessed.
- **Multiple Lookup Keys**: Supports exact name, filename, and database-prefixed lookups.
- **Robust Error Handling**: Gracefully handles missing files and parsing errors.
- **TypeScript**: Fully typed for reliability and IDE support.
- **MCP Protocol**: Standard MCP server implementation using stdio transport.

## Integration

### With MCP Clients
This server can be integrated with any MCP-compatible client using the provided `mcp-config.json`:

```json
{
  "mcpServers": {
    "get-ddl-definition": {
      "command": "node",
      "args": ["main.js"],
      "cwd": ".mcp/get-ddl-definition"
    }
  }
}
```

### With AI Assistants
This server provides natural language access to DDL files, enabling AI assistants to:
- Understand database structure
- Answer questions about specific objects
- Provide context for database-related queries
- Generate documentation or code based on existing DDL

## Benefits

1. **Centralized Knowledge**: All DDL information accessible through a single interface.
2. **Intelligent Search**: Find objects by name, type, or database context.
3. **Rich Documentation**: Automatic extraction of embedded documentation.
4. **Standard Protocol**: Uses MCP for broad compatibility.
5. **Type Safety**: Full TypeScript implementation.
6. **Extensible**: Easy to add new databases or object types.

## Future Enhancements

Potential improvements could include:
- **SQL Parsing**: Parse DDL to extract column definitions and relationships.
- **Dependency Graph**: Visualize object dependencies.
- **Change Tracking**: Track changes across different versions.
- **Validation**: Validate DDL syntax and consistency.
- **Export**: Export documentation in various formats.

## Installation

1. Install dependencies:
  `yarn`
2. Build the server:
  `yarn build`

## Development

For development with hot reload:
```bash
npm run dev
```

## File Naming Convention

The server understands the following file naming patterns:
- `dbo_[ObjectName].sql` - Standard objects
- `dbo_SP_[ProcedureName].sql` - Stored procedures
- `FY22_[ObjectName].sql` - FY22 specific objects

## Documentation Extraction

The server automatically extracts documentation from DDL files including:
- Description/Purpose
- Author and Date
- Parameters (for procedures)
- Dependencies
- Update History

## Conclusion

This MCP server provides a powerful interface to DDL files, making database object information easily accessible to AI assistants and other tools. It automatically indexes and understands database structure, providing rich context for database-related queries and development tasks.

This implementation is production-ready and can be immediately used with any MCP-compatible client to enhance database understanding and development workflows.
