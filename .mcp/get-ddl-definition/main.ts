/* eslint-disable no-await-in-loop, no-console, import/extensions */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
import { fileURLToPath } from 'url';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { readFile, readdir } from 'fs/promises';
import { join, dirname, extname } from 'path';
import { existsSync } from 'fs';
import { z } from 'zod';

interface Relationship {
  to: string;
  type: 'one-to-one' | 'one-to-many' | 'many-to-one' | 'many-to-many';
  sourceColumn: string;
  targetColumn: string;
  confidence: 'high' | 'medium' | 'low';
}

interface Table {
  name: string;
  relationships: Relationship[];
}

interface Schema {
  tables: Table[];
}

const getDirectoryName = () => dirname(fileURLToPath(import.meta.url));

process.on('uncaughtException', (err) => {
  console.error('[uncaughtException]', err);
});
process.on('unhandledRejection', (err) => {
  console.error('[unhandledRejection]', err);
});

const transport = new StdioServerTransport();
const server = new McpServer({
  name: 'get-ddl-definition',
  version: '1.0.0',
});

interface DDLObject {
  name: string;
  type: 'view' | 'procedure' | 'table';
  database: string;
  filePath: string;
  content: string;
  documentation: {
    description?: string;
    author?: string;
    date?: string;
    purpose?: string;
    parameters?: string[];
    dependencies?: string[];
    updateHistory?: string[];
  };
}

class DDLIndex {
  private readonly objects: Map<string, DDLObject> = new Map();
  private readonly relationships: Map<string, Schema> = new Map();
  private readonly databasePaths: Map<string, string> = new Map();
  private readonly projectRoot: string;

  constructor() {
    this.projectRoot = join(getDirectoryName(), '../../..');
    // Initialize database paths
    const docsPath = join(this.projectRoot, 'docs', 'database');
    this.databasePaths.set('sparxdb', join(docsPath, 'sparxdb'));
    this.databasePaths.set('sparx_support', join(docsPath, 'sparx_support'));
    this.databasePaths.set('cedar_support', join(docsPath, 'cedar_support'));
    this.databasePaths.set('cedar_support.cedar_api', join(docsPath, 'cedar_support.cedar_api'));
    this.databasePaths.set('cedar_support.system_census', join(docsPath, 'cedar_support.system_census'));
  }

  async indexAll(): Promise<void> {
    for (const [database, dbpath] of this.databasePaths) {
      await this.indexDatabase(database, dbpath);
    }
    await this.indexRelationshipsFile();
  }

  private async indexRelationshipsFile(): Promise<void> {
    const filePath = join(this.projectRoot, 'docs', 'database', 'sparxdb', 'relationships.ts');
    if (!existsSync(filePath)) {
      console.warn(`Relationships file not found: ${filePath}`);
      return;
    }

    try {
      // Dynamically import the relationships file
      const relationshipsModule = await import(filePath);
      const parsedRelationships: Schema = relationshipsModule.default;
      this.relationships.set('sparxdb', parsedRelationships);
      console.log('SparxDB relationships indexed successfully via direct import.');
    } catch (error) {
      console.error(`Error indexing relationships file via direct import: ${filePath}`, error);
    }
  }

  private async indexDatabase(database: string, basePath: string): Promise<void> {
    if (!existsSync(basePath)) {
      console.warn(`Database path not found: ${basePath}`);
      return;
    }

    try {
      const entries = await readdir(basePath, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory()) {
          const objectType = entry.name as 'views' | 'procedures' | 'tables';
          await this.indexObjectType(database, objectType, join(basePath, entry.name));
        }
      }
    } catch (error) {
      console.error(`Error indexing database ${database}:`, error);
    }
  }

  private async indexObjectType(
    database: string,
    objectType: string,
    dbpath: string,
  ): Promise<void> {
    try {
      const files = await readdir(dbpath);

      for (const file of files) {
        if (extname(file) === '.sql') {
          await this.indexFile(database, objectType, dbpath, file);
        }
      }
    } catch (error) {
      console.error(`Error indexing object type ${objectType} in ${database}:`, error);
    }
  }

  private async indexFile(
    database: string,
    objectType: string,
    filepath: string,
    filename: string,
  ): Promise<void> {
    try {
      const filePath = join(filepath, filename);
      const content = await readFile(filePath, 'utf-8');

      // Extract object name from filename
      const objectName = DDLIndex.extractObjectName(filename);
      if (!objectName) {
        return;
      }

      // Parse documentation from content
      const documentation = DDLIndex.parseDocumentation(content);

      const ddlObject: DDLObject = {
        name: objectName,
        type: objectType.slice(0, -1) as 'view' | 'procedure' | 'table', // Remove 's' from end
        database,
        filePath,
        content,
        documentation,
      };

      // Store with multiple keys for flexible lookup
      this.objects.set(objectName.toLowerCase(), ddlObject);
      this.objects.set(filename.toLowerCase(), ddlObject);

      // Also store with database prefix for disambiguation
      this.objects.set(`${database}.${objectName}`.toLowerCase(), ddlObject);
    } catch (error) {
      console.error(`Error indexing file ${filename}:`, error);
    }
  }

  private static extractObjectName(filename: string): string | null {
    // Remove .sql extension
    const nameWithoutExt = filename.replace(/\.sql$/, '');

    // Handle different naming patterns
    if (nameWithoutExt.startsWith('dbo_')) {
      return nameWithoutExt.replace('dbo_', '');
    }

    // Handle FY22_ prefix
    if (nameWithoutExt.startsWith('FY22_')) {
      return nameWithoutExt.replace('FY22_', '');
    }

    return nameWithoutExt;
  }

  private static parseDocumentation(content: string): DDLObject['documentation'] {
    const documentation: DDLObject['documentation'] = {};

    // Extract description/purpose
    const descriptionMatch = /DESCRIPTION\/PURPOSE:\s*\n\s*(.*?)(?:\n\s*\n|\n\s*\*)/s.exec(content);
    if (descriptionMatch) {
      documentation.description = descriptionMatch[1].trim();
    }

    // Extract purpose specifically
    const purposeMatch = /PURPOSE:\s*\n\s*(.*?)(?:\n\s*\n|\n\s*\*)/s.exec(content);
    if (purposeMatch) {
      documentation.purpose = purposeMatch[1].trim();
    }

    // Extract author
    const authorMatch = /AUTHOR:\s*(.*?)(?:\n|$)/.exec(content);
    if (authorMatch) {
      documentation.author = authorMatch[1].trim();
    }

    // Extract date
    const dateMatch = /DATE:\s*(.*?)(?:\n|$)/.exec(content);
    if (dateMatch) {
      documentation.date = dateMatch[1].trim();
    }

    // Extract parameters (for procedures)
    const parameters: string[] = [];
    const paramMatches = /@(\w+)\s+(\w+)(?:\([^)]+\))?\s*=/g.exec(content);
    for (const match of paramMatches ?? []) {
      parameters.push(`${match[1]} (${match[2]})`);
    }
    if (parameters.length > 0) {
      documentation.parameters = parameters;
    }

    // Extract dependencies
    const dependencies: string[] = [];
    const depMatch = /DEPENDENCY MATRIX:\s*\n(.*?)(?:\n\s*\n|\n\s*\*)/s.exec(content);
    if (depMatch) {
      const depContent = depMatch[1];
      const depLines = depContent.split('\n').filter((line) => line.trim() && !line.includes('---'));
      dependencies.push(...depLines.map((line) => line.trim()));
    }
    documentation.dependencies = dependencies;

    // Extract update history
    const updateHistory: string[] = [];
    const historyMatch = /UPDATE HISTORY:\s*\n(.*?)(?:\n\s*\n|\n\s*\*|\/\*)/s.exec(content);
    if (historyMatch) {
      const historyContent = historyMatch[1];
      const historyLines = historyContent.split('\n').filter((line) => line.trim() && !line.includes('---'));
      updateHistory.push(...historyLines.map((line) => line.trim()));
    }

    if (updateHistory.length > 0) {
      documentation.updateHistory = updateHistory;
    }

    return documentation;
  }

  search(query: string): DDLObject[] {
    const normalizedQuery = query.toLowerCase().trim();
    const results: DDLObject[] = [];

    // Exact match
    const exactMatch = this.objects.get(normalizedQuery);
    if (exactMatch) {
      results.push(exactMatch);
      return results;
    }

    // Partial matches
    for (const [key, obj] of this.objects) {
      if (key.includes(normalizedQuery)
          ?? obj.name.toLowerCase().includes(normalizedQuery)
          ?? obj.database.toLowerCase().includes(normalizedQuery)
          ?? obj.type.toLowerCase().includes(normalizedQuery)) {
        results.push(obj);
      }
    }

    return results;
  }

  getObject(name: string, database?: string): DDLObject | null {
    const normalizedName = name.toLowerCase().trim();

    if (database) {
      const key = `${database}.${normalizedName}`;
      return this.objects.get(key) || null;
    }

    return this.objects.get(normalizedName) || null;
  }

  listAll(): DDLObject[] {
    // Use a Set to deduplicate objects since the same object can be stored with multiple keys
    const uniqueObjects = new Set<DDLObject>();
    for (const obj of this.objects.values()) {
      uniqueObjects.add(obj);
    }
    return Array.from(uniqueObjects);
  }

  // noinspection JSUnusedGlobalSymbols
  listByType(type: string): DDLObject[] {
    return Array.from(this.objects.values()).filter((obj) => obj.type === type);
  }

  // noinspection JSUnusedGlobalSymbols
  listByDatabase(database: string): DDLObject[] {
    return Array.from(this.objects.values()).filter((obj) => obj.database === database);
  }

  getRelationships(database: string): Schema | null {
    return this.relationships.get(database) || null;
  }

  getRelationshipsForTable(database: string, tableName: string): Table | null {
    const schema = this.relationships.get(database);
    if (!schema) {
      return null;
    }
    return schema.tables.find(
      (table) => table.name.toLowerCase() === tableName.toLowerCase(),
    ) ?? null;
  }

  getDependencyTree(database: string, startTableName: string): Table[] {
    const schema = this.relationships.get(database);
    if (!schema) {
      return [];
    }

    const directRelationships: Table[] = [];
    const normalizedStartTableName = startTableName.toLowerCase();

    // Find the specified table
    const startTable = schema.tables.find(
      (table) => table.name.toLowerCase() === normalizedStartTableName,
    );

    if (!startTable) {
      return [];
    }

    // Always include the specified table itself
    directRelationships.push(startTable);

    // Collect tables that the start table directly points to (outgoing relationships)
    const directlyReferencedTables = new Set<string>();
    for (const rel of startTable.relationships) {
      directlyReferencedTables.add(rel.to.toLowerCase());
    }

    // Collect tables that directly point to the start table (incoming relationships)
    const directlyReferencingTables = new Set<string>();
    for (const table of schema.tables) {
      for (const rel of table.relationships) {
        if (rel.to.toLowerCase() === normalizedStartTableName) {
          directlyReferencingTables.add(table.name.toLowerCase());
        }
      }
    }

    // Add all directly connected tables to the result
    for (const table of schema.tables) {
      const tableName = table.name.toLowerCase();
      if (tableName !== normalizedStartTableName
          && (directlyReferencedTables.has(tableName)
              || directlyReferencingTables.has(tableName))) {
        directRelationships.push(table);
      }
    }

    return directRelationships;
  }
}

const ddlIndex = new DDLIndex();

// Initialize the index when the server starts
let isIndexed = false;

async function ensureIndexed() {
  if (!isIndexed) {
    console.log('Indexing DDL and relationships files...');
    await ddlIndex.indexAll();
    isIndexed = true;
    console.log('DDL and relationships indexing complete');
  }
}

server.registerTool(
  'search_ddl_objects',
  {
    title: 'Search DDL Objects',
    description: 'Search for database objects (tables, views, procedures) by name, type, or database',
    inputSchema: {
      query: z.string().describe('Search query - can be object name, type, or database name'),
    },
  },
  async ({ query }) => {
    await ensureIndexed();

    const results = ddlIndex.search(query);

    if (results.length === 0) {
      return {
        content: [
          {
            type: 'text',
            text: `No database objects found matching "${query}". Try searching for a different term or use list_ddl_objects to see all available objects.`,
          },
        ],
      };
    }

    const resultText = results
      .map((obj) => {
        const doc = obj.documentation;
        return `**${obj.name}** (${obj.type}, ${obj.database})
${doc.description ?? doc.purpose ?? 'No description available'}
${doc.author ? `Author: ${doc.author}` : ''}
${doc.date ? `Date: ${doc.date}` : ''}
File: ${obj.filePath}`;
      })
      .join('\n\n');

    return {
      content: [
        {
          type: 'text',
          text: `Found ${results.length} database object(s) matching "${query}":\n\n${resultText}`,
        },
      ],
    };
  },
);

server.registerTool(
  'get_ddl_definition',
  {
    description: 'Get detailed DDL definition and documentation for a specific database object',
    inputSchema: {
      name: z.string().describe('Name of the database object (e.g., "Sparx_System", "SP_Update_SystemExchange")'),
      database: z.string().optional().describe('Optional: Database name to disambiguate (e.g., "sparx_support", "cedar_support")'),
    },
  },
  async ({ name, database }) => {
    await ensureIndexed();

    const obj = ddlIndex.getObject(name, database);

    if (!obj) {
      return {
        content: [
          {
            type: 'text',
            text: `Database object "${name}" not found. Try searching for similar names or use list_ddl_objects to see all available objects.`,
          },
        ],
      };
    }

    const doc = obj.documentation;
    let response = `# ${obj.name} (${obj.type}, ${obj.database})

## Overview
${doc.description ?? doc.purpose ?? 'No description available'}

## Metadata
- **Type**: ${obj.type}
- **Database**: ${obj.database}
- **File**: ${obj.filePath}
${doc.author ? `- **Author**: ${doc.author}` : ''}
${doc.date ? `- **Date**: ${doc.date}` : ''}

`;

    if (doc.parameters && doc.parameters.length > 0) {
      response += `## Parameters
${doc.parameters.map((param) => `- ${param}`).join('\n')}

`;
    }

    if (doc.dependencies && doc.dependencies.length > 0) {
      response += `## Dependencies
${doc.dependencies.map((dep) => `- ${dep}`).join('\n')}

`;
    }

    if (doc.updateHistory && doc.updateHistory.length > 0) {
      response += `## Update History
${doc.updateHistory.map((update) => `- ${update}`).join('\n')}

`;
    }

    response += `## DDL Definition
\`\`\`sql
${obj.content}
\`\`\``;

    return {
      content: [
        {
          type: 'text',
          text: response,
        },
      ],
    };
  },
);

// noinspection FunctionWithMultipleLoopsJS
server.registerTool(
  'get_database_table_relationships',
  {
    title: 'Get Database Table Relationships',
    description: 'Get the defined relationships between tables for a specific database, or a dependency tree for a specific table within that database.',
    inputSchema: {
      database: z.string().describe('Name of the database (e.g., "sparxdb")'),
      tableName: z.string().optional().describe('Optional: Name of a specific table to get its dependency tree.'),
    },
  },
  async ({ database, tableName }) => {
    await ensureIndexed();

    if (tableName) {
      const dependencyTree = ddlIndex.getDependencyTree(database, tableName);

      if (dependencyTree.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: `No relationships or table "${tableName}" found for database "${database}".`,
            },
          ],
        };
      }

      let response = `# Dependency Tree for Table: ${tableName} in ${database} Database\n\n`;
      for (const table of dependencyTree) {
        response += `## Table: ${table.name}\n`;
        if (table.relationships.length > 0) {
          response += '| To | Type | Source Column | Target Column | Confidence |\n';
          response += '|----|------|---------------|---------------|------------|\n';
          for (const rel of table.relationships) {
            response += `| ${rel.to} | ${rel.type} | ${rel.sourceColumn} | ${rel.targetColumn} | ${rel.confidence} |\n`;
          }
        } else {
          response += 'No direct relationships defined for this table.\n';
        }
        response += '\n';
      }
      return {
        content: [
          {
            type: 'text',
            text: response,
          },
        ],
      };
    }

    const relationships = ddlIndex.getRelationships(database);

    if (!relationships) {
      return {
        content: [
          {
            type: 'text',
            text: `No relationships found for database "${database}".`,
          },
        ],
      };
    }

    let response = `# All Relationships for ${database} Database\n\n`;

    for (const table of relationships.tables) {
      response += `## Table: ${table.name}\n`;
      if (table.relationships.length > 0) {
        response += '| To | Type | Source Column | Target Column | Confidence |\n';
        response += '|----|------|---------------|---------------|------------|\n';
        for (const rel of table.relationships) {
          response += `| ${rel.to} | ${rel.type} | ${rel.sourceColumn} | ${rel.targetColumn} | ${rel.confidence} |\n`;
        }
      } else {
        response += 'No relationships defined for this table.\n';
      }
      response += '\n';
    }

    return {
      content: [
        {
          type: 'text',
          text: response,
        },
      ],
    };
  },
);

// noinspection FunctionWithMultipleLoopsJS
server.registerTool(
  'list_ddl_objects',
  {
    description: 'List all available database objects, optionally filtered by type or database',
    inputSchema: {
      type: z.string().optional().describe('Optional: Filter by object type (view, procedure, table)'),
      database: z.string().optional().describe('Optional: Filter by database name'),
    },
  },
  async ({ type, database }) => {
    await ensureIndexed();

    let objects = ddlIndex.listAll();

    if (type) {
      objects = objects.filter((obj) => obj.type === type);
    }

    if (database) {
      objects = objects.filter((obj) => obj.database === database);
    }

    if (objects.length === 0) {
      return {
        content: [
          {
            type: 'text',
            text: [
              'No database objects found',
              type ? ` of type "${type}"` : '',
              database ? ` in database "${database}"` : '',
              '.',
            ].join(' '),
          },
        ],
      };
    }

    // Group by database and type
    const grouped = objects.reduce((acc, obj) => {
      if (!acc[obj.database]) {
        acc[obj.database] = {};
      }
      if (!acc[obj.database][obj.type]) {
        acc[obj.database][obj.type] = [];
      }
      acc[obj.database][obj.type].push(obj.name);
      return acc;
    }, {} as Record<string, Record<string, string[]>>);

    let response = `# Available Database Objects (${objects.length} total)\n`;

    for (const [db, types] of Object.entries(grouped)) {
      response += `## ${db}\n`;
      for (const [gType, names] of Object.entries(types)) {
        response += `### ${gType}s (${names.length})\n`;
        // SonarQube is grumpy about nested template strings.
        // eslint-disable-next-line prefer-template
        response += names.map((gName) => `- ${gName}`).join('\n') + '\n\n';
      }
    }

    return {
      content: [
        {
          type: 'text',
          text: response,
        },
      ],
    };
  },
);

// Start the server
await server.connect(transport);
