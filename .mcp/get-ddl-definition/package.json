{"name": "get-ddl-definition", "version": "1.0.0", "description": "MCP server for searching and understanding DDL files", "main": "main.js", "type": "module", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "inspect": "npx @modelcontextprotocol/inspector node dist/main.js", "start": "yarn build && node dist/main.js", "dev": "tsx main.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}}