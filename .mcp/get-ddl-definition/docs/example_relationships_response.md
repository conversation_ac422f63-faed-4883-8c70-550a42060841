# Dependency Tree for Table: t_connector in sparxdb Database

## Table: t_connector
| To                    | Type        | Source Column   | Target Column  | Confidence |
|-----------------------|-------------|-----------------|----------------|------------|
| t_object              | many-to-one | Start_Object_ID | Object_ID      | high       |
| t_object              | many-to-one | End_Object_ID   | Object_ID      | high       |
| t_diagram             | many-to-one | DiagramID       | Diagram_ID     | medium     |
| t_connectortypes      | many-to-one | Connector_Type  | Connector_Type | high       |
| t_diagramlinks        | one-to-many | Connector_ID    | ConnectorID    | high       |
| t_connectortag        | one-to-many | Connector_ID    | ElementID      | high       |
| t_connectorconstraint | one-to-many | Connector_ID    | ConnectorID    | high       |
| t_roleconstraint      | one-to-many | Connector_ID    | ConnectorID    | high       |

## Table: t_connectorconstraint
| To                | Type        | Source Column  | Target Column | Confidence |
|-------------------|-------------|----------------|---------------|------------|
| t_connector       | many-to-one | ConnectorID    | Connector_ID  | high       |
| t_constrainttypes | many-to-one | ConstraintType | Constraint    | high       |

## Table: t_connectortag
| To          | Type        | Source Column | Target Column | Confidence |
|-------------|-------------|---------------|---------------|------------|
| t_connector | many-to-one | ElementID     | Connector_ID  | high       |

## Table: t_connectortypes
| To          | Type        | Source Column  | Target Column  | Confidence |
|-------------|-------------|----------------|----------------|------------|
| t_connector | one-to-many | Connector_Type | Connector_Type | high       |

## Table: t_diagram
| To               | Type        | Source Column | Target Column | Confidence |
|------------------|-------------|---------------|---------------|------------|
| t_package        | many-to-one | Package_ID    | Package_ID    | high       |
| t_diagram        | many-to-one | ParentID      | Diagram_ID    | high       |
| t_diagramtypes   | many-to-one | Diagram_Type  | Diagram_Type  | high       |
| t_authors        | many-to-one | Author        | AuthorName    | high       |
| t_diagramobjects | one-to-many | Diagram_ID    | Diagram_ID    | high       |
| t_diagramlinks   | one-to-many | Diagram_ID    | DiagramID     | high       |
| t_connector      | one-to-many | Diagram_ID    | DiagramID     | medium     |

## Table: t_diagramlinks
| To          | Type        | Source Column | Target Column | Confidence |
|-------------|-------------|---------------|---------------|------------|
| t_diagram   | many-to-one | DiagramID     | Diagram_ID    | high       |
| t_connector | many-to-one | ConnectorID   | Connector_ID  | high       |

## Table: t_object
| To                     | Type        | Source Column | Target Column   | Confidence |
|------------------------|-------------|---------------|-----------------|------------|
| t_package              | many-to-one | Package_ID    | Package_ID      | high       |
| t_object               | many-to-one | ParentID      | Object_ID       | high       |
| t_objecttypes          | many-to-one | Object_Type   | Object_Type     | high       |
| t_authors              | many-to-one | Author        | AuthorName      | high       |
| t_complexitytypes      | many-to-one | Complexity    | Complexity      | high       |
| t_attribute            | one-to-many | Object_ID     | Object_ID       | high       |
| t_operation            | one-to-many | Object_ID     | Object_ID       | high       |
| t_connector            | one-to-many | Object_ID     | Start_Object_ID | high       |
| t_connector            | one-to-many | Object_ID     | End_Object_ID   | high       |
| t_diagramobjects       | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectproperties     | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectconstraint     | one-to-many | Object_ID     | Object_ID       | high       |
| t_objecteffort         | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectfiles          | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectproblems       | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectrequires       | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectresource       | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectrisks          | one-to-many | Object_ID     | Object_ID       | high       |
| t_objectscenarios      | one-to-many | Object_ID     | Object_ID       | high       |
| t_objecttests          | one-to-many | Object_ID     | Object_ID       | high       |
| t_objecttrx            | one-to-many | Object_ID     | Object_ID       | high       |
| t_method               | one-to-many | Object_ID     | Object_ID       | high       |
| t_attributeconstraints | one-to-many | Object_ID     | Object_ID       | high       |

## Table: t_roleconstraint
| To                | Type        | Source Column  | Target Column | Confidence |
|-------------------|-------------|----------------|---------------|------------|
| t_connector       | many-to-one | ConnectorID    | Connector_ID  | high       |
| t_constrainttypes | many-to-one | ConstraintType | Constraint    | high       |
