#!/usr/bin/env node

// Example of how to use the DDL Definition MCP Server
// This is a demonstration script showing the capabilities

const { spawn } = require('child_process');

// Start the MCP server
const server = spawn('node', ['main.js'], {
  cwd: __dirname,
  stdio: ['pipe', 'pipe', 'pipe']
});

// Example JSON-RPC messages to test the server
const testMessages = [
  // List all tools
  {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/list"
  },

  // Search for Sparx_System
  {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/call",
    params: {
      name: "search_ddl_objects",
      arguments: {
        query: "Sparx_System"
      }
    }
  },

  // Get detailed definition of a procedure
  {
    jsonrpc: "2.0",
    id: 3,
    method: "tools/call",
    params: {
      name: "get_ddl_definition",
      arguments: {
        name: "SP_Update_SystemExchange",
        database: "sparx_support"
      }
    }
  },

  // List all views
  {
    jsonrpc: "2.0",
    id: 4,
    method: "tools/call",
    params: {
      name: "list_ddl_objects",
      arguments: {
        type: "view"
      }
    }
  }
];

let messageIndex = 0;

server.stdout.on('data', (data) => {
  const response = data.toString().trim();
  if (response) {
    try {
      const parsed = JSON.parse(response);
      console.log(`Response ${parsed.id}:`, JSON.stringify(parsed, null, 2));
    } catch (e) {
      console.log({
        message: 'error parsing response',
        response,
        error: e,
      });
    }
  }

  // Send next message
  if (messageIndex < testMessages.length) {
    const message = testMessages[messageIndex];
    server.stdin.write(JSON.stringify(message) + '\n');
    messageIndex++;
  } else {
    // Close after all messages
    setTimeout(() => {
      server.kill();
      process.exit(0);
    }, 1000);
  }
});

server.stderr.on('data', (data) => {
  console.error('Server error:', data.toString());
});

server.on('close', (code) => {
  console.log(`Server exited with code ${code}`);
});

// Send first message after a short delay
setTimeout(() => {
  const message = testMessages[messageIndex];
  server.stdin.write(JSON.stringify(message) + '\n');
  messageIndex++;
}, 500);
