// Modules.
import { Request, Response } from 'express';
import {
  get,
  isArray,
  isEmpty,
  isError,
  isString,
  isUndefined,
} from 'lodash';
import asyncHandler from 'express-async-handler';
// Custom.

import { tryAsync } from '../../../utils/general';
import Messages from '../../../utils/constants/messages';
import {
  StoredProcedureParam,
  StoredProcedureData,
  StoredProcedureQuery,
  ExchangePayload,
  ExchangeStatusPayload,
  MappedExchangeStatusPayload,
  PageDataExchangeRequest,
  IWmInsertDataExchangeResult,
} from '../../../types';
import {
  logAppError, sendError, sendSuccess,
} from '../../../utils/express/responses';
import {
  mapExchangeToSPJson,
  mapExchangeToSPRelation,
  mapStatusToSPSenderJson,
  mapStatusToSPReceiverJson,
  insertReceiver,
  insertSender,
  updateR<PERSON>eiver,
  updateSender,
  DataExchangeAddMessages,
} from '../../../utils/dataExchange';

import {
  getDb,
  SP_INSERT_SYSTEMDATAEXCHANGE,
  SP_UPDATE_SYSTEMDATAEXCHANGE,
} from '../../../utils/db-helpers';
import MssqlData from '../../../data-sources/mssql';

const handler = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const app = get(req, 'systemApp');
  if (!app) {
    res.status(500).send({ error: 'Unable to get the application from request' });
    return;
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    res.status(500).send({ error: 'Database Unavailable' });
    return;
  }

  try {
    const payload: PageDataExchangeRequest = req.body;
    const { DataExchanges: dataExchanges, systemId: globalSystemId } = payload;

    // Input validation: DataExchanges array must be present and not empty
    if (!dataExchanges || !isArray(dataExchanges) || isEmpty(dataExchanges)) {
      sendError(res, 400, { message: [DataExchangeAddMessages.error_data_exchanges_missing] });
      return;
    }

    if (!globalSystemId || !isString(globalSystemId)) {
      sendError(res, 400, { message: [DataExchangeAddMessages.error_system_id_missing] });
      return;
    }

    const exchangesToAdd: ExchangePayload[] = [];
    const exchangesToUpdate: ExchangePayload[] = [];
    const statusesToAdd: ExchangeStatusPayload[] = [];
    const statusesToUpdate: ExchangeStatusPayload[] = [];

    for (let i = 0; i < dataExchanges.length; i += 1) {
      const dataExchange = dataExchanges[i];
      const {
        Exchange: exchange,
        Status: status,
        deleted,
        updated,
      } = dataExchange;

      if (!exchange || isEmpty(exchange)) {
        sendError(res, 400, {
          message: [DataExchangeAddMessages.error_exchange_data_missing],
        });
        return;
      }
      if (!status || isEmpty(status)) {
        sendError(res, 400, {
          message: [DataExchangeAddMessages.error_exchange_status_missing],
        });
        return;
      }

      // Validate required status fields
      if (!status.systemId || !isString(status.systemId)) {
        sendError(res, 400, {
          message: [DataExchangeAddMessages.error_system_id_missing_in_status],
        });
        return;
      }
      if (!status.direction || !isString(status.direction)) {
        sendError(res, 400, {
          message: [DataExchangeAddMessages.error_direction_missing_in_status],
        });
        return;
      }
      if (!status.partnerStatus || !isString(status.partnerStatus)) {
        sendError(res, 400, {
          message: [DataExchangeAddMessages.error_partner_status_missing_in_status],
        });
        return;
      }
      if (!status.systemStatus || !isString(status.systemStatus)) {
        sendError(res, 400, {
          message: [DataExchangeAddMessages.error_system_status_missing_in_status],
        });
        return;
      }
      if (!status.reviewerStatus || !isString(status.reviewerStatus)) {
        sendError(res, 400, {
          message: [DataExchangeAddMessages.error_reviewer_status_missing_in_status],
        });
        return;
      }

      // Determine if add or update for the main exchange
      if (exchange.exchangeId
        && isString(exchange.exchangeId)
        && !isEmpty(exchange.exchangeId)
        && updated
      ) {
        exchangesToUpdate.push(exchange);
        // Status object will be updated or inserted based on whether exchangeId matches
        statusesToUpdate.push(status);
      } else if (!exchange.exchangeId && !deleted && !updated) {
        exchangesToAdd.push(exchange);
        // Status object will be inserted after getting new exchangeId
        statusesToAdd.push(status);
      }
      // If deleted is true, it's not handled by this add/update endpoint based on Webmethods logic
    }

    // adding/inserting logic
    const objectsToAdd = exchangesToAdd.map((exchange) => ({
      RefStr: exchange.exchangeId,
      ClassName: 'InformationFlow',
      Id: exchange.exchangeId, // Assuming ID is also the RefStr for update
      Values: mapExchangeToSPJson(exchange),
    }));

    const relationsToAdd = exchangesToAdd.map(
      (exchange, index) => mapExchangeToSPRelation(index.toString(), exchange),
    );

    const jsonInputToAdd = {
      CurrentProfile: 'API User',
      Objects: objectsToAdd,
      Relations: relationsToAdd,
    };

    const insertedExchangeIds: string[] = [];
    const insertedExchangeStatusIds: string[] = [];
    // let affectedExchangeRows = 0;
    const stringifiedAddInput = JSON.stringify(jsonInputToAdd);
    // Process additions for main exchanges
    let insertedFlag = false;
    try {
      const spParams: StoredProcedureParam[] = [{
        name: 'jsonOutput',
        type: 'nvarchar',
        param: 'max',
      }];
      const spData: StoredProcedureData[] = [{
        name: 'jsonInput',
        value: stringifiedAddInput,
      }, {
        name: 'jsonOutput',
        value: null, // Output parameter
        isOutput: true,
      }];
      const spQuery: StoredProcedureQuery[] = [{
        resultKey: 'output',
        paramName: 'jsonOutput',
      }];
      if (exchangesToAdd.length !== 0) {
        const [spError, spResult] = await tryAsync(
          db.queryStoredProceduresTyped<[unknown[], number]>(
            SP_INSERT_SYSTEMDATAEXCHANGE,
            spParams,
            spData,
            spQuery,
          ),
        );

        if (spError || isError(spResult)) {
          app.logger.error({ error: spError ?? spResult });
          throw new Error(Messages.db_query_stored_procedure_error);
        }

        if (isUndefined(spResult)) {
          app.logger.error({ error: 'spResult is undefined' });
          throw new Error('Result is undefined');
        }

        const spArrLastIndex = spResult[0].length - 1;
        const queryStatus = get(spResult, `[0][${spArrLastIndex}]`, '');
        // eslint-disable-next-line @typescript-eslint/no-explicit-any

        // yes, convoluted, I know
        const guidOutput = Object.values(get(spResult, `[0][${spArrLastIndex - 1}]`, '') as object)[0] as string;
        // yes, i'm super aware of how terrible this is... but it works
        const guidArr = JSON.parse(guidOutput.slice(15, -1).concat('] }')).GUID;
        // const guidArr = JSON.parse(guidOutput.slice(15).concat('] }')).GUID;
        if (isEmpty(guidArr)) {
          return;
        }
        for (let i = 0; i < guidArr.length; i += 1) {
          const status = statusesToAdd[i];
          status.exchangeId = guidArr[i];
          insertedExchangeIds.push(guidArr[i]);
        }
        insertedFlag = true;
        if (!queryStatus) {
          app.logger.error({ error: new Error('No output from stored procedure') });
          throw new Error(Messages.db_query_result_missing);
        }
      }
    } catch (error) {
      app.logger.error({ error });
      sendError(res, 500, { message: [(error as Error).message] });
      return;
    }

    // update logic
    // Process updates for main exchanges
    const objectsToUpdate = exchangesToUpdate.map((exchange, index) => ({
      RefStr: exchange.exchangeId,
      ClassName: 'InformationFlow',
      Id: index, // Assuming ID is also the RefStr for update
      Values: mapExchangeToSPJson(exchange),
    }));
    const relationsToUpdate = exchangesToUpdate.map(
      (exchange, index) => mapExchangeToSPRelation(index.toString(), exchange),
    );

    const jsonInputToUpdate = {
      CurrentProfile: 'API User',
      Objects: objectsToUpdate,
      Relations: relationsToUpdate,
    };
    // let affectedExchangeRows = 0;

    let updatedFlag = false;
    const stringifiedUpdateInput = JSON.stringify(jsonInputToUpdate);

    const updatedExchangeIds: string[] = [];
    const updatedExchangeStatusIds: string[] = [];
    // Process updates for main exchanges
    try {
      const spParams: StoredProcedureParam[] = [{
        name: 'jsonOutput',
        type: 'nvarchar',
        param: 'max',
      }];
      const spData: StoredProcedureData[] = [{
        name: 'jsonInput',
        value: stringifiedUpdateInput,
      }, {
        name: 'jsonOutput',
        value: null, // Output parameter
        isOutput: true,
      }];
      const spQuery: StoredProcedureQuery[] = [{
        resultKey: 'output',
        paramName: 'jsonOutput',
      }];
      if (exchangesToUpdate.length !== 0) {
        const [spError, spResult] = await tryAsync(
          db.queryStoredProceduresTyped<[unknown[], number]>(
            SP_UPDATE_SYSTEMDATAEXCHANGE,
            spParams,
            spData,
            spQuery,
          ),
        );

        if (isUndefined(spResult)) {
          app.logger.error({ error: 'spResult is undefined' });
          throw new Error('Result is undefined');
        }

        if (spError || isError(spResult)) {
          app.logger.error({ error: spError ?? spResult });
          throw new Error(Messages.db_query_stored_procedure_error);
        }

        const spArrLastIndex = spResult[0].length - 1;
        const queryStatusObj = get(spResult, '[0][1]', '');
        const queryStatus = get(queryStatusObj, 'queryStatus', null);

        // yes, even more convoluted, I know
        const guidOutput = Object.values(get(spResult, `[0][${spArrLastIndex - 1}]`, '') as object)[0] as string;
        // yes, i'm super aware of how even more terrible this is... but it works
        const slicedOutputOne = guidOutput.replaceAll('"GUID": ', '');
        const slicedOutputTwo = slicedOutputOne.substring(0, slicedOutputOne.length - 1);
        const slicedOutputThree = slicedOutputTwo.replace('{"NewObjects": {"', '{"NewObjects": ["').concat(']}');
        const parsedSlicedOutput = JSON.parse(slicedOutputThree);
        const guidArr = parsedSlicedOutput.NewObjects;

        for (let i = 0; i < guidArr.length; i += 1) {
          const guid = guidArr[i];
          const status = statusesToUpdate[i];
          status.exchangeId = guid;
          updatedExchangeIds.push(guid);
        }

        if (queryStatus !== 0) {
          app.logger.error({ error: new Error('Stored procedure update failed') });
          throw new Error('Stored procedure update failed');
        }
        updatedFlag = true;
      }
    } catch (error) {
      app.logger.error({ error });
      sendError(res, 500, { message: [(error as Error).message] });
      return;
    }

    // Process status records (insert/update into System_Census.SYSTEM_DATA_EXCHANGE_STATUS)
    // i.e. `addExchangeIdToNewStatusRecords`
    const newStatusesForDb: ExchangeStatusPayload[] = statusesToAdd.map((s) => ({
      exchangeId: s.exchangeId,
      systemId: s.systemId,
      systemStatus: s.systemStatus,
      partnerId: s.partnerId,
      partnerStatus: s.partnerStatus,
      reviewerStatus: s.reviewerStatus,
      direction: s.direction,
      deleted: s.deleted ?? false,
    }));
    const mappedAddedStatuses: MappedExchangeStatusPayload[] = [];
    newStatusesForDb.forEach((status) => {
      if (status.direction === 'sender') {
        const senderStatus = mapStatusToSPSenderJson(status);
        mappedAddedStatuses.push(senderStatus);
      } else {
        const receiverStatus = mapStatusToSPReceiverJson(status);
        mappedAddedStatuses.push(receiverStatus);
      }
    });

    const updatedStatusesForDb: ExchangeStatusPayload[] = statusesToUpdate.map((s) => ({
      exchangeId: s.exchangeId,
      systemId: s.systemId,
      systemStatus: s.systemStatus,
      partnerId: s.partnerId,
      partnerStatus: s.partnerStatus,
      reviewerStatus: s.reviewerStatus,
      direction: s.direction,
      deleted: s.deleted ?? false,
    }));
    const mappedUpdatedStatuses: MappedExchangeStatusPayload[] = [];
    updatedStatusesForDb.forEach((status) => {
      if (status.direction === 'sender') {
        const senderStatus = mapStatusToSPSenderJson(status);
        mappedUpdatedStatuses.push(senderStatus);
      } else {
        const receiverStatus = mapStatusToSPReceiverJson(status);
        mappedUpdatedStatuses.push(receiverStatus);
      }
    });

    try {
      // send new statuses
      await Promise.all(mappedAddedStatuses.map(async (exchangeStatus) => {
        let insertStatusResult: IWmInsertDataExchangeResult;
        if (exchangeStatus['@direction'] === 'sender') {
          insertStatusResult = await insertSender(db, exchangeStatus);
        } else {
          insertStatusResult = await insertReceiver(db, exchangeStatus);
        }
        insertedExchangeStatusIds.push(insertStatusResult.insertedIds[0]);
      }));
    } catch (error) {
      app.logger.error({ error });
      sendError(res, 500, { message: [(error as Error).message] });
      return;
    }

    try {
      // send updated statuses
      await Promise.all(mappedUpdatedStatuses.map(async (exchangeStatus) => {
        let updateStatusResult: number[] | Error;
        // returns affected rows
        if (exchangeStatus['@direction'] === 'sender') {
          updateStatusResult = await updateSender(app, db, exchangeStatus);
        } else {
          updateStatusResult = await updateReceiver(app, db, exchangeStatus);
        }
        if (isError(updateStatusResult)) {
          throw updateStatusResult;
        }

        if (updateStatusResult[0] === 0) {
          throw new Error('No rows affected');
        }

        updatedExchangeStatusIds.push(exchangeStatus.EXCHANGE_ID as string);
      }));
    } catch (error) {
      app.logger.error({ error });
      sendError(res, 500, { message: [(error as Error).message] });
      return;
    }
    const insertedMessageVisible = insertedFlag ? `Inserted exchange IDs: ${insertedExchangeIds.join(', ')} // Inserted exchange status IDs: ${insertedExchangeStatusIds.join(', ')}` : '';
    const updatedMessageVisible = updatedFlag ? `Updated exchange IDs: ${updatedExchangeIds.join(', ')} // Updated exchange status IDs: ${updatedExchangeStatusIds.join(', ')}` : '';
    sendSuccess(res, {
      result: 'success',
      message: [
        `${insertedFlag ? 'Successfully inserted exchanges' : 'Did not insert exchanges'}`,
        `${insertedMessageVisible}`,
        '-----------------------------',
        `${updatedFlag ? 'Successfully updated exchanges' : 'Did not update exchanges'}`,
        `${updatedMessageVisible}`,
      ],
    });
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'dataExchange',
        action: 'add',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: [Messages.internal_server_error] });
  }
});

export default handler;
