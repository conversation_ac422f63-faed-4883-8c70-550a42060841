import {
  get,
  isError,
  isString,
  isEmpty,
  isBoolean,
  unset,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import {
  SPARX_SYSTEM_DATAEXCHANGE,
  SYSTEM_SURVEY_EXCHANGE_STATUS,
} from '../db-helpers';

import MssqlData, {
  IWmInsertResult,
} from '../../data-sources/mssql';

import {
  strToBoolOrNull,
  tryAsync,
  boolToStr,
} from '../general';
// import Messages from '../constants/messages';
import {
  Where,
  OrObject,
  CMSApp,
  ExchangeListResult,
  Exchange,
  PageDataExchangeStatus,
  DbPageDataExchangeStatus,
  ExchangePayload,
  ExchangeStatusPayload,
  MappedExchangeStatusPayload,
} from '../../types';

/**
 * Interface for data exchange notes delete endpoint query parameters
 */
export interface DataExchangeNotesDeleteQueryParams {
  /**
   * An array of data exchange IDs whose associated notes should be deleted.
   * Can be provided as a single string or array of strings.
   */
  id: string | string[];
}

/**
 * Messages specific to data exchange notes delete operations
 */
export enum DataExchangeNotesDeleteMessages {
  error_objects_not_found = 'Object(s) could not be found',
  error_ids_empty = 'The id parameter(s) must not be empty',
  error_ids_not_string = 'All id parameters must be strings',
}

// Helper function to map DB results to API response format, mimicking Webmethods logic
export const convertDbToApiStatus = (
  dbStatus: DbPageDataExchangeStatus,
  requestSystemId: string,
): PageDataExchangeStatus => {
  const apiStatus: PageDataExchangeStatus = {
    exchangeId: dbStatus.EXCHANGE_ID,
    systemId: null,
    systemStatus: null,
    partnerId: null,
    partnerStatus: null,
    reviewerStatus: null,
    direction: null,
    // deleted is in Designer, but not in the actual Webmethods
    // deleted: null,
  };

  // Webmethods logic determines "direction" based on whether the input systemId
  // matches ORIGINAL_RECEIVER_ID or ORIGINAL_SENDER_ID from the DB record.
  if (dbStatus.ORIGINAL_RECEIVER_ID === requestSystemId) {
    // System in question is the receiver
    apiStatus.systemId = dbStatus.ORIGINAL_RECEIVER_ID;
    apiStatus.systemStatus = dbStatus.RECEIVER_STATUS;
    apiStatus.partnerId = dbStatus.ORIGINAL_SENDER_ID;
    apiStatus.partnerStatus = dbStatus.SENDER_STATUS;
    apiStatus.reviewerStatus = dbStatus.RECEIVER_QA_STATUS;
    apiStatus.direction = 'receiver';
    // deleted is in Designer, but not in the actual Webmethods
    // apiStatus.deleted = dbStatus.RECEIVER_REPORTED_DELETE_FLAG;
  } else if (dbStatus.ORIGINAL_SENDER_ID === requestSystemId) {
    // System in question is the sender
    apiStatus.systemId = dbStatus.ORIGINAL_SENDER_ID;
    apiStatus.systemStatus = dbStatus.SENDER_STATUS;
    apiStatus.partnerId = dbStatus.ORIGINAL_RECEIVER_ID;
    apiStatus.partnerStatus = dbStatus.RECEIVER_STATUS;
    apiStatus.reviewerStatus = dbStatus.SENDER_QA_STATUS;
    apiStatus.direction = 'sender';
    // deleted is in Designer, but not in the actual Webmethods
    // apiStatus.deleted = dbStatus.SENDER_REPORTED_DELETE_FLAG;
  }
  // If the requestSystemId doesn't match either, the original Webmethods flow
  // implies it wouldn't be returned by the specific JDBC adapters for receiver/sender,
  // or it would default to sender if found in the 'both' query but wasn't receiver.
  // For 'both' query, we check receiver first, then sender.
  return apiStatus;
};

// get
const dataExchangeStatusFindUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
  direction: 'receiver' | 'sender' | 'both',
) => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db)) {
    app.logger.error({ error: db });
    return new Error('Database Unavailable');
  }

  if (!systemId || !isString(systemId)) {
    const error = new Error('The systemId parameter is required and must be a string');
    app.logger.error({ error });
    return error;
  }

  // 'both' or any other value defaults to 'both'
  const whereClause: Where = {
    where: {
      operation: {
        column: 'ORIGINAL_RECEIVER_ID',
        operator: '=',
        value: systemId,
      },
    },
    or: [
      {
        operation: {
          column: 'ORIGINAL_SENDER_ID',
          operator: '=',
          value: systemId,
        },
      },
    ],
  };

  if (direction === 'receiver') {
    whereClause.where = {
      operation: {
        column: 'ORIGINAL_RECEIVER_ID',
        operator: '=',
        value: systemId,
      },
    };
    delete whereClause.or;
  }
  if (direction === 'sender') {
    whereClause.where = {
      operation: {
        column: 'ORIGINAL_SENDER_ID',
        operator: '=',
        value: systemId,
      },
    };
    delete whereClause.or;
  }

  const [queryError, queryResult] = await tryAsync(
    db.queryView(
      'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_STATUS',
      [
        'SYSTEM_SURVEY_EXCHANGE_STATUS_ID',
        'EXCHANGE_ID',
        'NEW_EXCHANGE_FLAG',
        'ORIGINAL_RECEIVER_ID',
        'ORIGINAL_SENDER_ID',
        'RECEIVER_REPORTED_DELETE_FLAG',
        'SENDER_REPORTED_DELETE_FLAG',
        'RECEIVER_STATUS',
        'RECEIVER_LAST_SUBMIT_DATETIME',
        'SENDER_STATUS',
        'SENDER_LAST_SUBMIT_DATETIME',
        'RECEIVER_QA_STATUS',
        'SENDER_QA_STATUS',
      ],
      whereClause,
    ),
  );

  if (queryError || isError(queryResult)) {
    app.logger.error({ error: queryError || queryResult });
    return new Error('There was an error fetching the query');
  }

  if (!queryResult || !Array.isArray(queryResult)) {
    app.logger.error({ error: new Error('Unable to get result from query') });
    return new Error('Unable to get result from query');
  }

  const statusRaw: DbPageDataExchangeStatus[] = queryResult[0] as DbPageDataExchangeStatus[];
  const exchangeStatus: PageDataExchangeStatus[] = statusRaw.map(
    (dbItem) => convertDbToApiStatus(dbItem, systemId),
  );

  return {
    count: exchangeStatus.length,
    ExchangeStatus: exchangeStatus,
  };
};

const getDataExchangeListUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
  direction: string,
  _version: string = '',
) => {
  const baseSchema = Joi.string();

  const allowedDirections = [
    'both',
    'receiver',
    'sender',
  ];

  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db)) {
    app.logger.error({ error: db });
    return new Error('Database Unavailable');
  }

  if (!isString(systemId) || isEmpty(systemId)) {
    const error = new Error('Please provide required parameters \'systemId\' and \'direction\'');
    app.logger.error({ error });
    return error;
  }

  if (!isString(direction) || isEmpty(direction)) {
    const error = new Error('Please provide required parameters \'systemId\' and \'direction\'');
    app.logger.error({ error });
    return error;
  }

  if (!allowedDirections.includes(direction)) {
    const error = new Error('The provided direction is not valid.');
    app.logger.error({ error });
    return error;
  }

  const paramOrs: OrObject[] | null = [];

  const byIdWhere: Where = {
    where: {},
    or: [],
  };

  const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
  if (systemIdVal.error) {
    app.logger.error({ error: systemIdVal.error });
    return new Error('The system ID is not valid');
  }

  if (direction === 'sender') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Sendor GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
  }
  if (direction === 'receiver') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Receiver GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
  }
  if (direction === 'both') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Sendor GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
    paramOrs.push({
      operation: {
        column: 'Sparx Receiver GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    });
    byIdWhere.or = paramOrs;
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_DATAEXCHANGE, [
    '"Connection GUID" as exchangeId',
    '"Connection Name" as exchangeName',
    '"Exchange Description" as exchangeDescription',
    '"Exchange Version" as exchangeVersion',
    '"Object State" as exchangeState',
    '"Exchange Start Date" as exchangeStartDate',
    '"Exchange End Date" as exchangeEndDate',
    '"Retire Date" as exchangeRetiredDate',
    '"Sparx Sendor GUID" as fromOwnerId',
    '"Sender Name" as fromOwnerName',
    '"Sender Type" as fromOwnerType',
    '"Sparx Receiver GUID" as toOwnerId',
    '"Receiver Name" as toOwnerName',
    '"Receiver Type" as toOwnerType',
    // Convert to string array
    '"Exchange Frequency" as connectionFrequency',
    // Convert to boolean
    '"IE Agreement" as dataExchangeAgreement',
    // Convert to boolean
    '"Exchange includes Beneficiary Address Data" as containsBeneficiaryAddress',
    // Convert to string array
    '"Beneficiary Address Purpose" as businessPurposeOfAddress',
    // Convert to boolean
    '"Address Data Editable" as isAddressEditable',
    // Convert to boolean
    '"Exchange Contains PII" as containsPii',
    // Convert to boolean
    '"Exchange Contains PHI" as containsPhi',
    // Convert to boolean
    '"Contains Health Disparity Data" as containsHealthDisparityData',
    // Convert to boolean
    '"Exchange Supports Mailing to Beneficiaries" as isBeneficiaryMailingFile',
    // Convert to boolean
    '"Exchange Includes Banking Data" as containsBankingData',
    // Convert to boolean
    '"Data Shared via API" as sharedViaApi',
    '"API Ownership" as apiOwnership',
    // Convert to array of object
    '"Type of Data" as typeOfDataName',
    '"Type of Data ID" as typeOfDataId',
    '"Number of Records Exchanged" as numOfRecords',
    '"Exchange Format" as dataFormat',
    '"Exchange Format Other" dataFormatOther',
    // Convert to boolean
    '"Exchange Contains CUI" as exchangeContainsCUI',
    '"Exchange CUI Description" as exchangeCUIDescription',
    // Convert to string array
    '"Exchange CUI Type" as exchangeCUIType',
    // Convert to boolean
    '"Exchange Connection Authenticated" as exchangeConnectionAuthenticated',
    // Convert to string array
    '"Exchange Network Protocol" as exchangeNetworkProtocol',
    '"Exchange Network Protocol Other" as exchangeNetworkProtocolOther',
  ], byIdWhere));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('Unable to retrieve exchange by system ID');
  }

  const exchangeResults: Exchange[] = get(viewResult, '[0]', []).map((item: ExchangeListResult) => {
    const connectionFrequency = get(item, 'connectionFrequency', '') as string;
    const containsBeneficiaryAddress = get(item, 'containsBeneficiaryAddress', null);
    const businessPurposeOfAddress = get(item, 'businessPurposeOfAddress', null) as string | null;
    const isAddressEditable = get(item, 'isAddressEditable', null);
    const containsPii = get(item, 'containsPii', null);
    const containsPhi = get(item, 'containsPhi', null);
    const containsHealthDisparityData = get(item, 'containsHealthDisparityData', null);
    const containsBankingData = get(item, 'containsBankingData', null);
    const sharedViaApi = get(item, 'sharedViaApi', null);
    const typeOfDataName = get(item, 'typeOfDataName', null) as string | null;
    const typeOfDataId = get(item, 'typeOfDataId', null) as string | null;
    const exchangeContainsCUI = get(item, 'exchangeContainsCUI', null);
    const exchangeCUIType = get(item, 'exchangeCUIType', null) as string | null;
    const exchangeConnectionAuthenticated = get(item, 'exchangeConnectionAuthenticated', null);
    const exchangeNetworkProtocol = get(item, 'exchangeNetworkProtocol', null) as string | null;

    let formattedConnectionFrequency: string[] = [];
    if (!isEmpty(connectionFrequency)) {
      formattedConnectionFrequency = connectionFrequency.split('|');
    }

    let formattedBusinessPurposeOfAddress: string[] = [];
    if (businessPurposeOfAddress) {
      formattedBusinessPurposeOfAddress = businessPurposeOfAddress.split('|');
    }

    const formattedTypeOfData: { id: string | null; name: string | null; }[] | null = [];
    if ((!isEmpty(typeOfDataName)) && (!isEmpty(typeOfDataId))) {
      formattedTypeOfData.push({
        id: typeOfDataId,
        name: typeOfDataName,
      });
    }

    let formattedExchangeCUIType: string[] = [];
    if (exchangeCUIType) {
      formattedExchangeCUIType = exchangeCUIType.split('|');
    }

    let formattedExchangeNetworkProtocol: string[] = [];
    if (exchangeNetworkProtocol) {
      formattedExchangeNetworkProtocol = exchangeNetworkProtocol.split('|');
    }

    // deleting these as they're converted
    unset(item, 'typeOfDataId');
    unset(item, 'typeOfDataName');

    return {
      ...item,
      connectionFrequency: formattedConnectionFrequency,
      containsBeneficiaryAddress: strToBoolOrNull(containsBeneficiaryAddress),
      businessPurposeOfAddress: formattedBusinessPurposeOfAddress,
      isAddressEditable: strToBoolOrNull(isAddressEditable),
      containsPii: strToBoolOrNull(containsPii),
      containsPhi: strToBoolOrNull(containsPhi),
      containsHealthDisparityData: strToBoolOrNull(containsHealthDisparityData),
      containsBankingData: strToBoolOrNull(containsBankingData),
      sharedViaApi: strToBoolOrNull(sharedViaApi),
      typeOfData: formattedTypeOfData,
      exchangeContainsCUI: strToBoolOrNull(exchangeContainsCUI),
      exchangeCUIType: formattedExchangeCUIType,
      exchangeConnectionAuthenticated: strToBoolOrNull(exchangeConnectionAuthenticated),
      exchangeNetworkProtocol: formattedExchangeNetworkProtocol,
    };
  });

  return {
    count: exchangeResults.length,
    Exchanges: exchangeResults,
  };
};

const getDataExchangeByIdUtil = async (
  app: CMSApp,
  db: MssqlData,
  id: string,
) => {
  const querySchema = Joi.string().uuid();
  const { error } = querySchema.validate(id.slice(1).slice(0, -1));

  if (error) {
    app.logger.error({ error });
    return new Error('Invalid object id');
  }

  const where = {
    where: {
      operation: {
        column: 'Connection GUID',
        operator: '=',
        value: id,
      },
    },
  };

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_DATAEXCHANGE, [
    '"Connection GUID" as exchangeId',
    '"Connection Name" as exchangeName',
    '"Exchange Version" as exchangeVersion',
    '"Object State" as exchangeState',
    '"Exchange Start Date" as exchangeStartDate',
    '"Exchange End Date" as exchangeEndDate',
    '"Retire Date" as exchangeRetiredDate',
    '"Sparx Sendor GUID" as fromOwnerId',
    '"Sender Name" as fromOwnerName',
    '"Sender Type" as fromOwnerType',
    '"Sparx Receiver GUID" as toOwnerId',
    '"Receiver Name" as toOwnerName',
    '"Receiver Type" as toOwnerType',
    // Convert string to string array after call
    '"Exchange Frequency" as connectionFrequency',
    // Convert string to string array after call
    '"Beneficiary Address Purpose" as businessPurposeOfAddress',
    // Convert Yes/No to boolean after call
    '"Contains Health Disparity Data" as containsHealthDisparityData',
    // Convert string to string array after call
    '"Type of Data" as typeOfData',
    // Convert Yes/No to boolean after call
    '"Exchange Contains CUI" as exchangeContainsCUI',
    '"Exchange CUI Description" as exchangeCUIDescription',
    // Convert string to string array after call
    '"Exchange Network Protocol" as exchangeNetworkProtocol',
    '"Exchange Network Protocol Other" as exchangeNetworkProtocolOther',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('Unable to retrieve exchange by system ID');
  }

  const exchangeResults = get(viewResult, '[0]', []).map((item: object) => {
    const connectionFrequency = get(item, 'connectionFrequency', null) as string | null;
    const businessPurposeOfAddress = get(item, 'businessPurposeOfAddress', null) as string | null;
    const containsHealthDisparityData = get(item, 'containsHealthDisparityData', null) as string | null;
    const typeOfData = get(item, 'typeOfData', null) as string | null;
    const exchangeContainsCUI = get(item, 'exchangeContainsCUI', null) as string | null;
    const exchangeNetworkProtocol = get(item, 'exchangeNetworkProtocol', null) as string | null;

    let formattedConnectionFrequency: string[] = [];
    if (connectionFrequency) {
      formattedConnectionFrequency = connectionFrequency.split('|');
    }

    let formattedBusinessPurposeOfAddress: string[] = [];
    if (businessPurposeOfAddress) {
      formattedBusinessPurposeOfAddress = businessPurposeOfAddress.split('|');
    }

    let formattedTypeOfData: string[] = [];
    if (typeOfData) {
      formattedTypeOfData = typeOfData.split('|');
    }

    let formattedExchangeNetworkProtocol: string[] = [];
    if (exchangeNetworkProtocol) {
      formattedExchangeNetworkProtocol = exchangeNetworkProtocol.split('|');
    }

    return {
      ...item,
      connectionFrequency: formattedConnectionFrequency,
      businessPurposeOfAddress: formattedBusinessPurposeOfAddress,
      containsHealthDisparityData: strToBoolOrNull(containsHealthDisparityData),
      typeOfData: formattedTypeOfData,
      exchangeContainsCUI: strToBoolOrNull(exchangeContainsCUI),
      exchangeNetworkProtocol: formattedExchangeNetworkProtocol,
    };
  });

  return get(exchangeResults, '[0]');
};

/**
 * Placeholder function for sending exchange note notifications.
 * Based on the webmethods sendExchangeNoteNotification service analysis.
 *
 * The original webmethods service:
 * 1. Checks global config %census.notes.notification.exchange.enabled%
 * 2. If enabled, calls selectExchangeDetailsById to get sender/receiver names
 * 3. Concatenates sender and receiver names for exchange description
 * 4. Sends notification via nibble.utils.notifications.producer.common:sendNotification
 *
 * This is a placeholder for future implementation.
 */
export const sendExchangeNoteNotification = async (
  app: CMSApp,
  exchangeId: string,
  userId: string,
): Promise<void> => {
  // Check if notifications are enabled (placeholder for global config check)
  const notificationsEnabled = process.env.CENSUS_NOTES_NOTIFICATION_EXCHANGE_ENABLED === 'true';

  if (!notificationsEnabled) {
    app.logger.debug('Exchange note notifications are disabled');
    return;
  }

  // TODO: Implement the full notification logic:
  // 1. Query exchange details to get sender/receiver names
  // 2. Build exchange name from sender|receiver format
  // 3. Construct notification message
  // 4. Send via notification service

  app.logger.info({
    message: 'Exchange note notification would be sent here',
    exchangeId,
    userId,
  });
};

// add
enum DataExchangeAddMessages {
  error_data_exchanges_missing = 'Please provide required input DataExchanges',
  error_system_id_missing = 'Missing or invalid `systemId` in top level payload',
  error_exchange_data_missing = 'Missing or invalid `Exchange` data for a data exchange',
  error_exchange_status_missing = 'Missing or invalid `Status` data for a data exchange',
  error_exchange_id_missing_for_update = '`exchangeId` is required for updates',
  error_system_id_missing_in_status = '`systemId` is required in Status object',
  error_direction_missing_in_status = '`direction` is required in Status object',
  error_partner_status_missing_in_status = '`partnerStatus` is required in Status object',
  error_system_status_missing_in_status = '`systemStatus` is required in Status object',
  error_reviewer_status_missing_in_status = '`reviewerStatus` is required in Status object',
}

const mapExchangeToSPJson = (exchange: ExchangePayload): Record<string, unknown> => {
  const json: Record<string, unknown> = {
    exchangeId: exchange.exchangeId,
    cms_exchange: exchange.exchangeName,
    description: exchange.exchangeDescription,
    name: `${exchange.fromOwnerName}${exchange.exchangeVersion}>>${exchange.toOwnerName}${exchange.exchangeVersion}`,
    objectstate: exchange.exchangeState,
    startdate: exchange.exchangeStartDate,
    enddate: exchange.exchangeEndDate,
    // convert to date MM/dd/yyyy
    cms_exchange_retire_date: exchange.exchangeRetiredDate,
    fromowner: exchange.fromOwnerId,
    toowner: exchange.toOwnerId,
    cms_connection_frequency: Array.isArray(exchange.connectionFrequency) ? exchange.connectionFrequency.join('|') : null,
    cms_ie_agreement: exchange.dataExchangeAgreement,
    cms_data_exch_beneficiary_add: isBoolean(exchange.containsBeneficiaryAddress)
      ? boolToStr(exchange.containsBeneficiaryAddress)
      : null,
    cms_beneficiary_address_pur: Array.isArray(exchange.businessPurposeOfAddress) ? exchange.businessPurposeOfAddress.join('|') : null,
    cms_adress_data_edits: isBoolean(exchange.isAddressEditable)
      ? boolToStr(exchange.isAddressEditable)
      : null,
    cms_data_exchange_pii: isBoolean(exchange.containsPii) ? boolToStr(exchange.containsPii) : null,
    cms_data_exchange_phi: isBoolean(exchange.containsPhi) ? boolToStr(exchange.containsPhi) : null,
    cms_health_disparity_data: isBoolean(exchange.containsHealthDisparityData)
      ? boolToStr(exchange.containsHealthDisparityData)
      : null,
    cms_data_exch_benef_mailing: isBoolean(exchange.isBeneficiaryMailingFile)
      ? boolToStr(exchange.isBeneficiaryMailingFile)
      : null,
    cms_data_exch_api_data_share: isBoolean(exchange.sharedViaApi)
      ? boolToStr(exchange.sharedViaApi)
      : null,
    cms_api_owner: exchange.apiOwnership,
    typeOfData: Array.isArray(exchange.typeOfData) ? exchange.typeOfData.map((t) => t.id).filter(Boolean).join('|') : null, // Assuming only ID is needed by SP
    cms_data_exch_num_recs: exchange.numOfRecords,
    cms_data_exch_format: exchange.dataFormat,
    cms_data_exch_format_other: exchange.dataFormatOther,
    cms_exchange_contains_cui: isBoolean(exchange.exchangeContainsCUI)
      ? boolToStr(exchange.exchangeContainsCUI)
      : null,
    cms_exchange_connection_authenticated: isBoolean(exchange.exchangeConnectionAuthenticated)
      ? boolToStr(exchange.exchangeConnectionAuthenticated)
      : null,
    cms_exchange_cui_description: exchange.exchangeCUIDescription,
    cms_exchange_cui_type: Array.isArray(exchange.exchangeCUIType) ? exchange.exchangeCUIType.join('|') : null,
    cms_exchange_network_protocol: Array.isArray(exchange.exchangeNetworkProtocol) ? exchange.exchangeNetworkProtocol.join('|') : null,
    cms_exchange_network_protocol_other: exchange.exchangeNetworkProtocolOther,
    cms_data_exchange_banking: isBoolean(exchange.containsBankingData)
      ? boolToStr(exchange.containsBankingData)
      : null,
  };
  // Remove null/undefined values to avoid issues with JSON parsing in SP
  Object.keys(json).forEach((key) => (
    json[key] === null || json[key] === undefined ? delete json[key] : {}
  ));
  return json;
};

const createRelation = (
  FromId: string,
  Property: string,
  ToRef: string,
) => ({ FromId, Property, ToRef });

const insertReceiver = async (
  db: MssqlData,
  mappedInput: MappedExchangeStatusPayload,
  index: number,
): Promise<IWmInsertResult> => {
  const insertStatusResult: IWmInsertResult = await db.wmInsert(
    SYSTEM_SURVEY_EXCHANGE_STATUS,
    [
      'EXCHANGE_ID',
      'ORIGINAL_SENDER_ID',
      'SENDER_STATUS',
      'ORIGINAL_RECEIVER_ID',
      'RECEIVER_STATUS',
      'SENDER_QA_STATUS',
      'SENDER_REPORTED_DELETE_FLAG',
    ],
    [[
      index,
      mappedInput.ORIGINAL_SENDER_ID,
      mappedInput.SENDER_STATUS,
      mappedInput.ORIGINAL_RECEIVER_ID,
      mappedInput.RECEIVER_STATUS,
      mappedInput.SENDER_QA_STATUS,
      mappedInput.SENDER_REPORTED_DELETE_FLAG,
    ]],
    'EXCHANGE_ID',
  );
  return insertStatusResult;
};

const insertSender = async (
  db: MssqlData,
  mappedInput: MappedExchangeStatusPayload,
  index: number,
): Promise<IWmInsertResult> => {
  const insertStatusResult: IWmInsertResult = await db.wmInsert(
    SYSTEM_SURVEY_EXCHANGE_STATUS,
    [
      'EXCHANGE_ID',
      'ORIGINAL_RECEIVER_ID',
      'RECEIVER_STATUS',
      'ORIGINAL_SENDER_ID',
      'SENDER_STATUS',
      'RECEIVER_QA_STATUS',
      'RECEIVER_REPORTED_DELETE_FLAG',
    ],
    [[
      index,
      mappedInput.ORIGINAL_RECEIVER_ID,
      mappedInput.RECEIVER_STATUS,
      mappedInput.ORIGINAL_SENDER_ID,
      mappedInput.SENDER_STATUS,
      mappedInput.RECEIVER_QA_STATUS,
      mappedInput.RECEIVER_REPORTED_DELETE_FLAG,
    ]],
    'EXCHANGE_ID',
  );
  return insertStatusResult;
};

const updateReceiver = async (
  db: MssqlData,
  mappedInput: MappedExchangeStatusPayload,
) => {
  if (mappedInput.EXCHANGE_ID === null || !mappedInput.EXCHANGE_ID) {
    return new Error('EXCHANGE_ID is null');
  }

  const whereObj: Where = {
    where: {
      operation: {
        column: 'EXCHANGE_ID',
        operator: '=',
        value: mappedInput.EXCHANGE_ID || '',
      },
    },
  };
  const updateStatusResult: number | Error = await db.wmUpdate(
    SYSTEM_SURVEY_EXCHANGE_STATUS,
    [
      'EXCHANGE_ID',
      'ORIGINAL_SENDER_ID',
      'SENDER_STATUS',
      'ORIGINAL_RECEIVER_ID',
      'RECEIVER_STATUS',
      'SENDER_QA_STATUS',
      'SENDER_REPORTED_DELETE_FLAG',
    ],
    [
      mappedInput.EXCHANGE_ID,
      mappedInput.ORIGINAL_SENDER_ID,
      mappedInput.SENDER_STATUS,
      mappedInput.ORIGINAL_RECEIVER_ID,
      mappedInput.RECEIVER_STATUS,
      mappedInput.SENDER_QA_STATUS,
      mappedInput.SENDER_REPORTED_DELETE_FLAG,
      // Always insert as not deleted by default, following Webmethods behavior
    ],
    whereObj,
  );

  return updateStatusResult;
};

const updateSender = async (
  db: MssqlData,
  mappedInput: MappedExchangeStatusPayload,
) => {
  if (mappedInput.EXCHANGE_ID === null || !mappedInput.EXCHANGE_ID) {
    return new Error('EXCHANGE_ID is null');
  }

  const whereObj: Where = {
    where: {
      operation: {
        column: 'EXCHANGE_ID',
        operator: '=',
        value: mappedInput.EXCHANGE_ID || '',
      },
    },
  };
  const updateStatusResult: number | Error = await db.wmUpdate(
    SYSTEM_SURVEY_EXCHANGE_STATUS,
    [
      'EXCHANGE_ID',
      'ORIGINAL_RECEIVER_ID',
      'RECEIVER_STATUS',
      'ORIGINAL_SENDER_ID',
      'SENDER_STATUS',
      'RECEIVER_QA_STATUS',
      'RECEIVER_REPORTED_DELETE_FLAG',
    ],
    [
      mappedInput.EXCHANGE_ID,
      mappedInput.ORIGINAL_RECEIVER_ID,
      mappedInput.RECEIVER_STATUS,
      mappedInput.ORIGINAL_SENDER_ID,
      mappedInput.SENDER_STATUS,
      mappedInput.RECEIVER_QA_STATUS,
      mappedInput.RECEIVER_REPORTED_DELETE_FLAG,
      // Always insert as not deleted by default, following Webmethods behavior
    ],
    whereObj,
  );
  return updateStatusResult;
};

const mapExchangeToSPRelation = (
  index: string,
  exchange: ExchangePayload,
): Record<string, unknown>[] => {
  const Relations = [];
  const relationOne = createRelation(index, 'From', exchange.fromOwnerId as string);
  Relations.push(relationOne);
  const relationTwo = createRelation(index, 'To', exchange.toOwnerId as string);
  Relations.push(relationTwo);
  const relationThree = createRelation(index, 'FromOwner', exchange.fromOwnerId as string);
  Relations.push(relationThree);
  const relationFour = createRelation(index, 'ToOwner', exchange.toOwnerId as string);
  Relations.push(relationFour);
  const relationFive = createRelation(index, 'cms_data_exch_type_of_data', index);
  Relations.push(relationFive);
  return Relations;
};

const mapStatusToSPSenderJson = (statusObj: ExchangeStatusPayload) => ({
  '@direction': statusObj.direction || '',
  EXCHANGE_ID: statusObj.exchangeId || '',
  ORIGINAL_RECEIVER_ID: statusObj.systemId || '',
  RECEIVER_STATUS: statusObj.systemStatus || '',
  ORIGINAL_SENDER_ID: statusObj.partnerId || '',
  SENDER_STATUS: statusObj.partnerStatus || '',
  RECEIVER_QA_STATUS: statusObj.reviewerStatus || '',
  RECEIVER_REPORTED_DELETE_FLAG: statusObj.deleted ?? 'false',
});

const mapStatusToSPReceiverJson = (statusObj: ExchangeStatusPayload) => ({
  '@direction': statusObj.direction || '',
  EXCHANGE_ID: statusObj.exchangeId || '',
  ORIGINAL_SENDER_ID: statusObj.systemId || '',
  SENDER_STATUS: statusObj.systemStatus || '',
  ORIGINAL_RECEIVER_ID: statusObj.partnerId || '',
  RECEIVER_STATUS: statusObj.partnerStatus || '',
  SENDER_QA_STATUS: statusObj.reviewerStatus || '',
  SENDER_REPORTED_DELETE_FLAG: statusObj.deleted ?? 'false',
});

// const validateIndividualExchange = (dataExchange: DataExchangeInput) => {
//   const {
//     Exchange: exchange,
//     Status: status,
//   } = dataExchange;

//   if (!exchange || isEmpty(exchange)) {
//     return new Error(DataExchangeAddMessages.error_exchange_data_missing);
//   };

//   if (!status || isEmpty(status)) {
//     return new Error(DataExchangeAddMessages.error_exchange_status_missing);
//   }

//   // Validate required status fields
//   if (!status.systemId || !isString(status.systemId)) {
//     return new Error(DataExchangeAddMessages.error_system_id_missing_in_status);
//   }
//   if (!status.direction || !isString(status.direction)) {
//     return new Error(DataExchangeAddMessages.error_direction_missing_in_status);
//   }

//   if (!status.partnerStatus || !isString(status.partnerStatus)) {
//     return new Error(DataExchangeAddMessages.error_partner_status_missing_in_status);
//   }
//   if (!status.systemStatus || !isString(status.systemStatus)) {
//     return new Error(DataExchangeAddMessages.error_system_status_missing_in_status);
//   }

//   if (!status.reviewerStatus || !isString(status.reviewerStatus)) {
//     return new Error(DataExchangeAddMessages.error_reviewer_status_missing_in_status);
//   }

//   return true;
// };

// const addAndUpdateExchangeUtil = async (
//   app: CMSApp,
//   db: MssqlData,
//   payload: PageDataExchangeRequest,
// ) => {
//   if (!app) {
//     return new Error('Unable to get the application from request');
//   }

//   if (isError(db)) {
//     app.logger.error({ error: db });
//     return new Error('Database Unavailable');
//   }

//   const { DataExchanges: dataExchanges, systemId: globalSystemId } = payload;

//   // Input validation: DataExchanges array must be present and not empty
//   if (!dataExchanges || !isArray(dataExchanges) || isEmpty(dataExchanges)) {
//     return new Error(DataExchangeAddMessages.error_data_exchanges_missing);
//   }

//   if (!globalSystemId || !isString(globalSystemId)) {
//     return new Error(DataExchangeAddMessages.error_system_id_missing);
//   }

//   const exchangesToAdd: ExchangePayload[] = [];
//   const exchangesToUpdate: ExchangePayload[] = [];
//   const statusesToAdd: ExchangeStatusPayload[] = [];
//   const statusesToUpdate: ExchangeStatusPayload[] = [];

// }

export {
  dataExchangeStatusFindUtil,
  getDataExchangeListUtil,
  getDataExchangeByIdUtil,
  DataExchangeAddMessages,
  mapExchangeToSPJson,
  mapExchangeToSPRelation,
  mapStatusToSPSenderJson,
  mapStatusToSPReceiverJson,
  insertReceiver,
  insertSender,
  updateReceiver,
  updateSender,
  createRelation,
};
