create table CEDAR_API.INTAKE_REQUEST
(
    INTAKE_REQUEST_ID            int identity
        constraint INTAKE_REQUEST_PK
            primary key,
    CLIENT_ID                    nvarchar(255) not null,
    CLIENT_NAME                  nvarchar(255) not null,
    REQUEST_TYPE                 nvarchar(50)  not null,
    REQUEST_TYPE_SCHEMA          nvarchar(255) not null,
    REQUEST_CLIENT_STATUS        nvarchar(255) not null,
    REQUEST_CEDAR_STATUS         nvarchar(50)  not null,
    REQUEST_DATA_FORMAT          nvarchar(50)  not null,
    REQUEST_DATA                 nvarchar(max),
    CLIENT_CREATED_DATE          datetime,
    CLIENT_LAST_UPDATED_DATE     datetime,
    CREATED_DATE                 datetime,
    CREATED_BY                   nvarchar(255),
    LAST_UPDATED_DATE            datetime,
    LAST_UPDATED_BY              nvarchar(255),
    CLIENT_VERSION               nvarchar(50)  not null,
    REQUEST_CEDAR_STATUS_MESSAGE nvarchar(4000)
)
go





CREATE TRIGGER [CEDAR_API].[INTAKE_REQUEST_CD]
ON [CEDAR_API].[INTAKE_REQUEST]
AFTER INSERT
AS
    UPDATE [CEDAR_API].[INTAKE_REQUEST]
    SET CREATED_DATE = GETDATE(), LAST_UPDATED_DATE = GETDATE()
    WHERE [INTAKE_REQUEST_ID] IN (SELECT DISTINCT [INTAKE_REQUEST_ID] FROM Inserted)

go





CREATE TRIGGER [CEDAR_API].[INTAKE_REQUEST_UD]
ON [CEDAR_API].[INTAKE_REQUEST]
AFTER UPDATE
AS
    UPDATE [CEDAR_API].[INTAKE_REQUEST]
    SET LAST_UPDATED_DATE = GETDATE()
    WHERE [INTAKE_REQUEST_ID] IN (SELECT DISTINCT [INTAKE_REQUEST_ID] FROM Inserted)

go
