


CREATE VIEW [dbo].[Sparx_System_URL]
AS

Select 
 [System ID] = src.Alias
,[Sparx System ID] = src.Object_ID
,[Sparx System GUID] = src.ea_guid
,[System Name] = src.Name
,[URL ID] = dest.<PERSON>as
,[Sparx URL ID] = dest.Object_ID
,[Sparx URL GUID] = dest.ea_guid
,[Connection GUID] = c.ea_guid
,[Connection Name] = c.Name
,[URL Name] = dest.Name
,[Confidence Level] = MAX(CASE WHEN destid.Property = 'Confidence Level' THEN destid.Value ELSE NULL END)
,[Hosting Environment] = MAX(CASE WHEN destid.Property = 'Hosting Environment' THEN destid.Value ELSE NULL END)
,[Is Intranet Only] = MAX(CASE WHEN destid.Property = 'Is Intranet Only' THEN destid.Value ELSE NULL END)
,[Portal Services Used] = MAX(CASE WHEN destid.Property = 'Portal Services Used' THEN destid.Value ELSE NULL END)
,[Provides Version Code Repository Access] = MAX(CASE WHEN destid.Property = 'Provides Version Code Repository Access' THEN destid.Value ELSE NULL END)
,[URL API AWF] = MAX(CASE WHEN destid.Property = 'URL API AWF' THEN destid.Value ELSE NULL END)
,[URL API Endpoint] = MAX(CASE WHEN destid.Property = 'URL API Endpoint' THEN destid.Value ELSE NULL END)
,[URLLink] = MAX(CASE WHEN destid.Property = 'URLLink' THEN destid.Value ELSE NULL END)
,[Used for Beneficiary] = MAX(CASE WHEN destid.Property = 'Used for Beneficiary' THEN destid.Value ELSE NULL END)
,[Uses HTTPS] = MAX(CASE WHEN destid.Property = 'Uses HTTPS' THEN destid.Value ELSE NULL END)
,[No URLs Flag] = srcp.Value
--,[Import Year] = MAX(CASE WHEN ct.Property = 'Import Year' THEN ct.Value ELSE NULL END)
from [SparxDB].[dbo].t_connector c
join [SparxDB].[dbo].t_object src on src.Object_ID = c.Start_Object_ID and src.Stereotype = 'System'
join [SparxDB].[dbo].t_object dest on dest.Object_ID = c.End_Object_ID and dest.Stereotype = 'System URL'
join [SparxDB].[dbo].[t_objectproperties] destid on dest.Object_ID = destid.Object_ID 
join [SparxDB].[dbo].[t_objectproperties] srcp on src.Object_ID = srcp.Object_ID and srcp.Property = 'No URLs Flag'
--left join [SparxDb].[dbo].t_connectortag ct on ct.ElementID = c.Connector_ID
where c.Stereotype = 'System has URL'
group by src.Alias
	,src.Object_ID
	,src.ea_guid
	,src.Name
	,dest.Alias
	,dest.Name
	,dest.Object_ID
	,dest.ea_guid
	,c.ea_guid
	,c.name
	, srcp.Value














