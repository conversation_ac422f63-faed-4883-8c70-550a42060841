


CREATE VIEW [dbo].[Sparx_System_Vendor]
AS

Select 
 [System ID] = src.Ali<PERSON>
,[Vendor ID] = dest.Alias

,[Sparx System ID] = src.Object_ID
,[Sparx System GUID] = src.ea_guid
,[Sparx System Name] = src.Name
,[Sparx Vendor ID] = dest.Object_ID
,[Sparx Vendor GUID] = dest.ea_guid
,[Sparx Vendor Name] = dest.Name
,[Connection GUID] = c.ea_guid
,[Connection Name] = c.Name

,[Import Year] = (select distinct ct.Value from [SparxDB].[dbo].t_connectortag ct where c.Connector_ID = ct.ElementID and ct.Property = 'Import Year')

from [SparxDB].[dbo].t_connector c
join [SparxDB].[dbo].t_object src on src.Object_ID = c.Start_Object_ID and src.Stereotype = 'System'
join [SparxDB].[dbo].t_object dest on dest.Object_ID = c.End_Object_ID and dest.Stereotype = 'Vendor'
where c.Stereotype = 'Built On'









