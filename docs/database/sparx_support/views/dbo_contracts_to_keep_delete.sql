




CREATE VIEW [dbo].[contracts_to_keep_delete]
AS

with to_keep as (
select distinct --526
  REPLACE(x.[Contract Number], '-', '') as [Contract Number]
 , REPLACE(x.[Order Number], '-', '') as [Order Number]
from Sparx_Support.dbo.Sparx_Contract_All x
--where x.[Contract Number] like '%-%' or x.[Order Number] like '%-%'
except
select distinct --2755
	ca.[Contract Number], ca.[Order Number]
from Sparx_Support.dbo.Sparx_Contract_All ca
)
select x.[Sparx Contract GUID], x.[Contract Number], x.[Order Number], x.[Contract Name]
from to_keep a
join Sparx_Support.dbo.Sparx_Contract_All x on  REPLACE(x.[Contract Number], '-', '') = a.[Contract Number] and REPLACE(x.[Order Number], '-', '') = a.[Order Number]







