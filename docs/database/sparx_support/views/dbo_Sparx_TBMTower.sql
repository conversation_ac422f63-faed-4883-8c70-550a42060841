

CREATE VIEW [dbo].[Sparx_TBMTower]
AS
Select '143-17-0' AS [TBMTower ID],'{B8959917-B724-487D-9827-EBA1E0EAE2A6}' as [Sparx TBMTower GUID], 'Application' as [TBMTower Name] UNION ALL
Select '143-18-0' AS [TBMTower ID],'{D5486490-E1B9-4A93-99F4-B7063436E439}' as [Sparx TBMTower GUID], 'Compute' as [TBMTower Name] UNION ALL
Select '143-19-0' AS [TBMTower ID],'{A0B3A3C7-31E5-43F8-A517-B703F58E5B2F}' as [Sparx TBMTower GUID], 'Data Center' as [TBMTower Name] UNION ALL
Select '143-20-0' AS [TBMTower ID],'{D575F333-FACF-4C7D-BE52-5BB51E52EC18}' as [Sparx TBMTower GUID], 'Delivery' as [TBMTower Name] UNION ALL
Select '143-21-0' AS [TBMTower ID],'{E03DCBAE-4017-49F2-A063-078EE4BA94AB}' as [Sparx TBMTower GUID], 'End User' as [TBMTower Name] UNION ALL
Select '143-22-0' AS [TBMTower ID],'{2ABC8D51-7936-46E8-8CD5-9289F13DFA6E}' as [Sparx TBMTower GUID], 'IT Management' as [TBMTower Name] UNION ALL
Select '143-23-0' AS [TBMTower ID],'{077CA037-7D58-47CF-A4CB-B75B0F5A692D}' as [Sparx TBMTower GUID], 'Network' as [TBMTower Name] UNION ALL
Select '143-24-0' AS [TBMTower ID],'{278B5380-ACD9-402E-9B67-9510424C7C25}' as [Sparx TBMTower GUID], 'Output' as [TBMTower Name] UNION ALL
Select '143-25-0' AS [TBMTower ID],'{3A742CA9-BE8D-4275-B8B1-E3B92BE71F7D}' as [Sparx TBMTower GUID], 'Platform' as [TBMTower Name] UNION ALL
Select '143-26-0' AS [TBMTower ID],'{D0C078CC-AB56-415E-886E-AEF7E81F3CEB}' as [Sparx TBMTower GUID], 'Security & Compliance' as [TBMTower Name] UNION ALL
Select '143-27-0' AS [TBMTower ID],'{CE84A471-1A08-4158-8C6E-ECDB21498325}' as [Sparx TBMTower GUID], 'Storage' as [TBMTower Name] 






