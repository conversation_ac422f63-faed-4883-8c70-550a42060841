














CREATE VIEW [dbo].[Sparx_URL]
As

Select 
 [URL ID] = o.Alias
,[URL Name] = o.Name
,[Sparx URL ID] = o.Object_ID
,[Sparx URL GUID] = o.ea_guid
,[Confidence Level] =  MAX(CASE WHEN op.Property = 'Confidence Level' THEN op.Value ELSE NULL END)
,[Hosting Environment] =  MAX(CASE WHEN op.Property = 'Hosting Environment' THEN op.Value ELSE NULL END)
,[Is Intranet Only] =  MAX(CASE WHEN op.Property = 'Is Intranet Only' THEN op.Value ELSE NULL END)
,[Portal Services Used] =  MAX(CASE WHEN op.Property = 'Portal Services Used' THEN op.Value ELSE NULL END)
,[Provides Version Code Repository Access] =  MAX(CASE WHEN op.Property = 'Provides Version Code Repository Access' THEN op.Value ELSE NULL END)
,[URL API AWF] =  MAX(CASE WHEN op.Property = 'URL API AWF' THEN op.Value ELSE NULL END)
,[URL API Endpoint] =  MAX(CASE WHEN op.Property = 'URL API Endpoint' THEN op.Value ELSE NULL END)
,[URLLink] =  MAX(CASE WHEN op.Property = 'URLLink' THEN op.Value ELSE NULL END)
,[Used for Beneficiary] =  MAX(CASE WHEN op.Property = 'Used for Beneficiary' THEN op.Value ELSE NULL END)
,[Uses HTTPS] =  MAX(CASE WHEN op.Property = 'Uses HTTPS' THEN op.Value ELSE NULL END)
,[Legacy ID] = MAX(CASE WHEN op.Property = 'Legacy ID' THEN op.Value ELSE NULL END)
,o.Package_ID
from	[SparxDB].[dbo].[t_object] o
join 	[SparxDB].[dbo].[t_objectproperties] op on op.Object_ID = o.Object_ID
where	o.Stereotype = 'System URL'
and o.Package_ID IN (513,712,1138)
group by o.Alias, o.Name, o.Object_ID, o.ea_guid, o.Package_ID










