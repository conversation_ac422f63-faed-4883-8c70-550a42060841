






CREATE VIEW [dbo].[Sparx_Vendor]
AS

Select 
 [Vendor ID] = o<PERSON><PERSON>
,[Vendor Name] = o.Name
,[Data Center Type] = MAX(CASE WHEN op.Property = 'Data Center Type' THEN op.Value ELSE NULL END)
,[CMS FEDRAMP] = MAX(CASE WHEN op.Property = 'CMS FEDRAMP' THEN op.Value ELSE NULL END)
,[CMS ATO] = MAX(CASE WHEN op.Property = 'CMS ATO' THEN op.Value ELSE NULL END)
,[Legacy ID] = MAX(CASE WHEN op.Property = 'Legacy ID' THEN op.Value ELSE NULL END)

,[City] = MAX(CASE WHEN op.Property = 'City' THEN op.Value ELSE NULL END)
,[Country] = MAX(CASE WHEN op.Property = 'Country' THEN op.Value ELSE NULL END)
,[Email] = MAX(CASE WHEN op.Property = 'Email' THEN op.Value ELSE NULL END)
,[Fax] = MAX(CASE WHEN op.Property = 'Fax' THEN op.Value ELSE NULL END)
,[Phone] = MAX(CASE WHEN op.Property = 'Phone' THEN op.Value ELSE NULL END)
,[State] = MAX(CASE WHEN op.Property = 'State' THEN op.Value ELSE NULL END)
,[Street] = MAX(CASE WHEN op.Property = 'Street' THEN op.Value ELSE NULL END)
,[Website] = MAX(CASE WHEN op.Property = 'Website' THEN op.Value ELSE NULL END)
,[Zip] = MAX(CASE WHEN op.Property = 'Zip' THEN op.Value ELSE NULL END)
,[Vendor Description] = o.Note


,[Sparx Vendor ID] = o.Object_ID
,[Sparx Vendor GUID] = o.ea_guid
,package_guid = p.ea_guid
,package_name = p.Name
from [SparxDB].[dbo].[t_object] o
join [SparxDB].[dbo].[t_objectproperties] op on o.Object_ID = op.Object_ID
join [SparxDB].[dbo].[t_package] p on p.Package_ID = o.Package_ID
where o.Stereotype = 'Vendor'
--and o.Package_ID IN (538,497,701,724)
group by o.Alias
		,o.Name
		,o.Note
		,o.Object_ID
		,o.ea_guid
		,p.ea_guid
		,p.Name










