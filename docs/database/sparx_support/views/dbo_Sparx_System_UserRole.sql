















CREATE VIEW [dbo].[Sparx_System_UserRole]
AS


with census_system as (

Select 
 [System ID] = o.Alias
,[Sparx System ID] = o.Object_ID
,[Sparx System GUID] =o.ea_guid
,[System Name] = o.Name
,[Acronym] = MAX(CASE WHEN op.Property = 'Acronym' THEN op.Value ELSE NULL END)
,[Object State] = MAX(CASE WHEN op.Property = 'Object State' THEN op.Value ELSE NULL END)
,[CMS UUID] = MAX(CASE WHEN op.Property = 'CMS UUID' THEN op.Value ELSE NULL END)
,[ID] =  MAX(CASE WHEN op.Property = 'ID' THEN op.Value ELSE NULL END)
,nextVersionID = null
,previousVersionID = null
,[ICT Object ID] = o.Alias
,[Description] = MAX(o.Note)
,[Version] = null
,status = null
,belongsTo=MAX(opar.ea_guid)

,[Next System Survey] = MAX(CASE WHEN op.Property = 'Next System Survey' THEN op.Value ELSE NULL END)
,[Current System Survey] = MAX(CASE WHEN op.Property = 'Current System Survey' THEN op.Value ELSE NULL END)
from [SparxDB].[dbo].[t_object] o
join [SparxDB].[dbo].[t_objectproperties] op on o.Object_ID = op.Object_ID
join [SparxDB].[dbo].[t_objectproperties] oid on o.Object_ID = oid.Object_ID and oid.Property = 'ID'
left join [SparxDB].[dbo].[t_object] opar on opar.Object_ID = o.ParentID
where o.Stereotype = 'System'
group by o.Alias, o.Object_ID, o.Name, o.ea_guid
)

select
	 s.*
	,businessOwnerOrg = bo.[Organization Name]
	,[Business Owner Organization Component] = bo.[Organization Component]
	,[System Maintainer Organization] = sm.[Organization Name]
	,[System Maintainer Organization Component] =  sm.[Organization Component]
	,RoleAssignmentId = scq.[Connection GUID]
	,Role = scq.[Role Name]
	,RoleId = scq.[Sparx Role GUID]
	,PersonFirstName = pq.[First Name]
	,PersonLastName = pq.[Last Name]
	,PersonEUA = pq.[User Name]

from census_system s
left join dbo.Sparx_System_BusinessOwner_Organization bo on bo.[Sparx System GUID] = s.[Sparx System GUID]
left join dbo.Sparx_System_Maintainer_Organization sm on sm.[Sparx System GUID] = s.[Sparx System GUID]
left join dbo.Sparx_System_Contact scq on scq.[Sparx System GUID] = s.[Sparx System GUID] 
left join dbo.Sparx_Person pq on pq.[Sparx Person GUID] = scq.[Sparx Person GUID]















