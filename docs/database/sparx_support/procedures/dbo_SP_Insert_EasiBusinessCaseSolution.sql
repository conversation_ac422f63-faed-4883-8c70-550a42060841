






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_EasiBusinessCaseSolution
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the EasiBusinessCaseSolution attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_EasiBusinessCaseSolution] 
			 @AcquisitionApproach
			,@Cons
			,@CostSavings
			,@CreationDate
			,@EndDate
			,@ExternalID
			,@HostingNeeds
			,@InProcessofCMSITSecurityApproval
			,@ITSecurityApproved
			,@LastUpdate
			,@Pros
			,@SolutionType
			,@StartDate
			,@UserInterface
			,@BusinessCaseID
			,@summary
			,@hostingCloudServiceType
			,@hostingLocation
			,@hostingType
			,@TargetContractAward
			,@TargetCompletionDate
			,@ZeroTrustAlignment
			,@HostingCloudStrategy
			,@WorkforceTrainingReqs
			,@GUID



 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_EasiBusinessCaseSolution
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 04/18/2025	Aditya Sharma				Added New Fields
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_EasiBusinessCaseSolution] 
--Add the parameters for the stored procedure here
	@AcquisitionApproach nvarchar(max) = NULL, 
	@Cons nvarchar(max) = NULL, 
	@CostSavings nvarchar(255) = NULL, 
	@CreationDate nvarchar(255) = NULL, 
	@EndDate nvarchar(255) = NULL, 
	@ExternalID nvarchar(255) = NULL, 
	@HostingNeeds nvarchar(255) = NULL, 
	@InProcessofCMSITSecurityApproval nvarchar(255) = NULL, 
	@ITSecurityApproved nvarchar(255) = NULL, 
	@LastUpdate nvarchar(255) = NULL, 
	@Pros nvarchar(max) = NULL, 
	@SolutionType nvarchar(255) = NULL, 
	@StartDate nvarchar(255) = NULL, 
	@UserInterface nvarchar(255) = NULL,
	@BusinessCaseID nvarchar(255) = NULL,
	@summary nvarchar(max) = NULL,
	@hostingCloudServiceType  nvarchar(255),
	@hostingLocation nvarchar(255),
	@hostingType nvarchar(255),
	--@title nvarchar(255),

	@TargetContractAward nvarchar(255),
	@TargetCompletionDate nvarchar(255),
	@ZeroTrustAlignment nvarchar(max),
	@HostingCloudStrategy nvarchar(max),
	@WorkforceTrainingReqs nvarchar(max),

	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Insert EasiBusinessCaseSolution';
PRINT 'GUID = '+@GUID;
BEGIN

	BEGIN TRY
		DECLARE @Parent_ObjectID INT = 0;
		DECLARE @New_ObjectID INT = 0;

		IF len(ISNULL(@GUID,'')) = 0 
			THROW 50001,'Input GUID is empty',1;

		IF len(@GUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;

PRINT 'BusinessCaseID :'+@BusinessCaseID

				SELECT @Parent_ObjectID = Object_ID from SparxDB.dbo.t_object where ea_guid = @BusinessCaseID;
PRINT '@Parent_ObjectID'

				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=EASi Business Case Solution;FQName=CMS EA::EASi Business Case Solution;@ENDSTEREO;', @GUID, '<none>');
PRINT 'Before Update';
				UPDATE SparxDB.dbo.t_object set Stereotype = 'EASi Business Case Solution', ParentID = @Parent_ObjectID, Note = @summary--, Name = @title
				WHERE t_object.ea_guid = @GUID;
PRINT 'GUID = '+@GUID;
				SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
PRINT @New_ObjectID
PRINT 'Before Insert'
				INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
				Select ObjectID, Property, Val, Notes, ea_guid from
				(
					Select @New_ObjectID as ObjectID, 'Acquisition Approach' as Property, '' as Val, @AcquisitionApproach as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Cons' as Property, '' as Val, @Cons as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Cost Savings' as Property, @CostSavings as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Creation Date' as Property, @CreationDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'End Date' as Property, @EndDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'External ID' as Property, @ExternalID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Hosting Needs' as Property, @HostingNeeds as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'In Process of CMS IT Security Approval' as Property, @InProcessofCMSITSecurityApproval as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'IT Security Approved' as Property, @ITSecurityApproved as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Last Update' as Property, @LastUpdate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Pros' as Property, '' as Val, @Pros as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Solution Type' as Property, @SolutionType as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Start Date' as Property, @StartDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'User Interface' as Property, @UserInterface as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Hosting Cloud Service Type' as Property, @hostingCloudServiceType as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Hosting Location' as Property, @hostingLocation as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Hosting Type' as Property, @hostingType as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL

					Select @New_ObjectID as ObjectID, 'Target Contract Award Date' as Property, @TargetContractAward as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Target Completion Date' as Property, @TargetCompletionDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Zero Trust Alignment' as Property, '' as Val, @ZeroTrustAlignment as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Hosting Cloud Strategy' as Property, '' as Val, @HostingCloudStrategy as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Workforce Training Reqs' as Property, '' as Val, @WorkforceTrainingReqs as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
					
				) t;

				--Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

				--ROLLBACK;
		END
	END TRY
	BEGIN CATCH
PRINT 'Inside SP_Insert_EasiBusinessCaseSolution exception'
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;


