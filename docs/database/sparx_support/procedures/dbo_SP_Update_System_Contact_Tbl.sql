







/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_System_Contact_Tbl
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   06/24/2024
 *
 * DESCRIPTION/PURPOSE:
 *	Update the Sparx_System_Contact_Tbl in SparxDB after an update to underlying data. A System GUID is required as a mandatory parameter to be passed.
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_System_Contact_Tbl] 
			@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_System_Contact_Tbl
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 
 * 
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_System_Contact_Tbl] 
--Add the parameters for the stored procedure here
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update System Contact Tbl ';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

PRINT 'Before Update'


		DELETE FROM Sparx_Support.[CEDAR_API].[Sparx_System_Contact_Tbl]
		WHERE [Sparx System GUID] = @GUID;

		INSERT INTO Sparx_Support.[CEDAR_API].[Sparx_System_Contact_Tbl]
				   ([System ID]
				   ,[Sparx System ID]
				   ,[Sparx System GUID]
				   ,[System Name]
				   ,[Person ID]
				   ,[Sparx Person ID]
				   ,[Sparx Person GUID]
				   ,[Person Name]
				   ,[Role ID]
				   ,[Sparx Role ID]
				   ,[Sparx Role GUID]
				   ,[Role Name]
				   ,[Connection GUID]
				   ,[Connection Name])
		SELECT 
					[System ID]
				   ,[Sparx System ID]
				   ,[Sparx System GUID]
				   ,[System Name]
				   ,[Person ID]
				   ,[Sparx Person ID]
				   ,[Sparx Person GUID]
				   ,[Person Name]
				   ,[Role ID]
				   ,[Sparx Role ID]
				   ,[Sparx Role GUID]
				   ,[Role Name]
				   ,[Connection GUID]
				   ,[Connection Name]
		FROM Sparx_Support.dbo.Sparx_System_Contact
		WHERE [Sparx System GUID] = @GUID;

		DELETE FROM Sparx_Support.[CEDAR_API].[Sparx_EASi_System_UserRole_Tbl]
		WHERE [Sparx System GUID] = @GUID;

		INSERT INTO [CEDAR_API].[Sparx_EASi_System_UserRole_Tbl]
           ([System ID]
           ,[Sparx System ID]
           ,[Sparx System GUID]
           ,[System Name]
           ,[Acronym]
           ,[Object State]
           ,[CMS UUID]
           ,[ID]
           ,[nextVersionID]
           ,[previousVersionID]
           ,[ICT Object ID]
           ,[Description]
           ,[Version]
           ,[status]
           ,[belongsTo]
           ,[Next System Survey]
           ,[Current System Survey]
           ,[businessOwnerOrg]
           ,[Business Owner Organization Component]
           ,[System Maintainer Organization]
           ,[System Maintainer Organization Component]
           ,[RoleAssignmentId]
           ,[Role]
           ,[RoleId]
           ,[PersonFirstName]
           ,[PersonLastName]
           ,[PersonEUA]
           ,[ATO Effective Date]
           ,[ATO Expiration Date])
		SELECT
			[System ID]
           ,[Sparx System ID]
           ,[Sparx System GUID]
           ,[System Name]
           ,[Acronym]
           ,[Object State]
           ,[CMS UUID]
           ,[ID]
           ,[nextVersionID]
           ,[previousVersionID]
           ,[ICT Object ID]
           ,[Description]
           ,[Version]
           ,[status]
           ,[belongsTo]
           ,[Next System Survey]
           ,[Current System Survey]
           ,[businessOwnerOrg]
           ,[Business Owner Organization Component]
           ,[System Maintainer Organization]
           ,[System Maintainer Organization Component]
           ,[RoleAssignmentId]
           ,[Role]
           ,[RoleId]
           ,[PersonFirstName]
           ,[PersonLastName]
           ,[PersonEUA]
           ,[ATO Effective Date]
           ,[ATO Expiration Date]
		FROM Sparx_Support.[dbo].[Sparx_EASi_System_UserRole]
		WHERE [Sparx System GUID] = @GUID;
		
PRINT 'After Row updates'

PRINT 'End of Update System Contact Tbl';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Contact Tbl Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












