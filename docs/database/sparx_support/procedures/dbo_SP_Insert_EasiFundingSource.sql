






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_EasiFundingSource
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/02/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the EasiFundingSource attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_EasiFundingSource] 
			 @fundingSourceId
			,@fundingSourceName
			,@fundingSourceNumber
			,@GUID

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_EasiFundingSource
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_EasiFundingSource] 
--Add the parameters for the stored procedure here
	@fundingSourceId nvarchar(255) = '', 
	@fundingSourceName nvarchar(255) = '', 
	@fundingSourceNumber nvarchar(255) = '', 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Insert EasiFundingSource';
PRINT 'GUID = '+@GUID;

BEGIN

	BEGIN TRY

		DECLARE @New_ObjectID INT = 0;

		IF len(ISNULL(@GUID,'')) = 0 
			THROW 50001,'Input GUID is empty',1;

		IF len(@GUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;


PRINT @GUID;
PRINT 'fundingSourceNumber :'+@fundingSourceNumber
				--BEGIN TRAN
				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=EASi Funding Source;FQName=CMS EA::EASi Funding Source;@ENDSTEREO;', @GUID, '<none>');
PRINT 'Before Update';
				UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'EASi Funding Source', Name  = @fundingSourceName
				WHERE t_object.ea_guid = @GUID;
PRINT 'GUID = '+@GUID;
				SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
PRINT @New_ObjectID
PRINT 'Before Insert'
				INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
				Select ObjectID, Property, Val, Notes, ea_guid from
				(
					Select @New_ObjectID as ObjectID, 'Funding Source Number' as Property, @fundingSourceNumber as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'EASi Funding Source ID' as Property, '' as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Funding Source Id' as Property, @fundingSourceId as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 

				) t;

				--Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

				--ROLLBACK;
		END
	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;



