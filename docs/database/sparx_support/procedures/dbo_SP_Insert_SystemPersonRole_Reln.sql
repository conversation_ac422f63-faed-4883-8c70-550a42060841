








/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemPersonRole_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/01/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemPersonRole attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemPersonRole_Reln] 
			 	@objectId, 
				@roleTypeId, 
				@assigneeId





 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemPersonRole_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 01/30/2023	Aditya Sharma	1310		Change SP to prevent DA or QA addition if one already exists
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemPersonRole_Reln] 
--Add the parameters for the stored procedure here
	@objectId nvarchar(255) = '', 
	@roleTypeId nvarchar(255) = '', 
	@assigneeId nvarchar(255) = ''


AS

PRINT 'Inside Insert SystemPersonRole';
PRINT 'SystemId = '+@objectId;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @System_ObjectId nvarchar(255) = '';
DECLARE	@Person_ObjectId nvarchar(255) = '';
DECLARE @RoleType_ObjectId nvarchar(255) = '';
DECLARE @RoleType_Name nvarchar(255) = '';
DECLARE @RoleType_Count INT = 0;



IF len(@objectId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @objectId;
		Select @Person_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @assigneeId;
		Select @RoleType_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @roleTypeId;
		Select @RoleType_Name = Name from SparxDB.dbo.t_object where ea_guid = @roleTypeId;

		IF @RoleType_Name IN ('QA Reviewer', 'DA Reviewer')
		BEGIN
			SELECT @RoleType_Count = count(1) from dbo.Sparx_System_Contact
			WHERE [Sparx Role GUID] = @roleTypeId and [Sparx System GUID] = @objectId;
		END;

PRINT @System_ObjectId;
PRINT @Person_ObjectId;
PRINT @RoleType_ObjectId;
PRINT @RoleType_Name;
PRINT @RoleType_Count;

		IF @RoleType_Count = 0 
		BEGIN

			Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
			Values (@objectId+' : '+@assigneeId+' : '+@roleTypeId, 'Association', 'System has Contact','Source -> Destination',@System_ObjectId ,@Person_ObjectId , CONCAT('{',NEWID(),'}'))

			Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where [Name] = @objectId+' : '+@assigneeId+' : '+@roleTypeId;

			---now insert the stereotype into the xref using the ea_guid as the client value   Note also that this is a connector property for type
			INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
			VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=System has Contact;FQName=CMS EA::System has Contact;@ENDSTEREO;', @New_ConnectorGUID, '<none>');


			---insert the values into the tagged values spefified in the stereotype using the ID of the connector from above.
			Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
			Values (@New_ConnectorID, 'Relationship Status', '', '', CONCAT('{',NEWID(),'}'))

			Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
			Values (@New_ConnectorID, 'Relationship Impact', '', '', CONCAT('{',NEWID(),'}'))

			Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
			Values (@New_ConnectorID, 'Business Contact Role', @roleTypeId, '', CONCAT('{',NEWID(),'}'))

		END

		-- Check the inserts

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records

-- Update the Reporting Table for the System

		DELETE FROM Sparx_Support.CEDAR_API.Sparx_EASi_System_UserRole_Tbl where [Sparx System GUID] = @objectId;
		DELETE FROM Sparx_Support.CEDAR_API.Sparx_System_Contact_Tbl where [Sparx System GUID] = @objectId;

		INSERT INTO Sparx_Support.CEDAR_API.Sparx_EASi_System_UserRole_Tbl
		SELECT * FROM Sparx_Support.dbo.Sparx_EASi_System_UserRole where [Sparx System GUID] = @objectId;

		INSERT INTO Sparx_Support.CEDAR_API.Sparx_System_Contact_Tbl
		SELECT * FROM Sparx_Support.dbo.Sparx_System_Contact where [Sparx System GUID] = @objectId;



	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












