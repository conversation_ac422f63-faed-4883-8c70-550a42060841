






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemDataCenter_Reln
 *
 * AUTHOR: <PERSON>
 * DATE:   7/31/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemDataCenter attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemDataCenter_Reln] 
				@ApplicationSoftwareReplicated, 
				@CCICIntegrationFlag, 
				@ContractorName, 
				@DataReplicated, 
				@Environment, 
				@FedContactOrg, 
				@HotSite, 
				@ImportLookup, 
				@IncidentResponseContact, 
				@MultiFactorAuthentication, 
				@ProductionDataUseFlag, 
				@RelationshipImpact, 
				@RelationshipStatus, 
				@SystemServerSoftwareReplicated, 
				@UtilizesVPN, 
				@WANType,
				@UsersRequiringMultifactorAuthentication,
				@OtherSpecialUsers,
				@NetworkEncryption,
				@WANTypeOther,
				@GUID




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemDataCenter_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 08/12/2024	Aditya Sharma				Add FY25 new field: WAN Type - Other 
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE     PROCEDURE [dbo].[SP_Update_SystemDataCenter_Reln] 
--Add the parameters for the stored procedure here
	@ApplicationSoftwareReplicated nvarchar(255) = NULL,
	@CCICIntegrationFlag nvarchar(255) = NULL,
	@ContractorName nvarchar(255) = NULL,
	@DataReplicated nvarchar(255) = NULL,
	@Environment nvarchar(255) = NULL,
	@FedContactOrg nvarchar(255) = NULL,
	@HotSite nvarchar(255) = NULL,
	@ImportLookup nvarchar(255) = NULL,
	@IncidentResponseContact nvarchar(255) = NULL,
	@MultiFactorAuthentication nvarchar(255) = NULL,
	@ProductionDataUseFlag nvarchar(255) = NULL,
	@RelationshipImpact nvarchar(255) = NULL,
	@RelationshipStatus nvarchar(255) = NULL,
	@SystemServerSoftwareReplicated nvarchar(255) = NULL,
	@UtilizesVPN nvarchar(255) = NULL,
	@WANType nvarchar(max) = NULL,
	@UsersRequiringMultifactorAuthentication nvarchar(max)= NULL,
	@OtherSpecialUsers nvarchar(255)= NULL,
	@NetworkEncryption nvarchar(max)= NULL,
	@WANTypeOther nvarchar(max) = NULL,
	--GUID is [Sparx_Support].[dbo].[Sparx_System_DataCenter_Full].[Connection GUID]
	@GUID nvarchar(60) = NULL

AS

PRINT 'Inside SP_Update_SystemDataCenter_Reln';

DECLARE @ConnectorID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		Select @ConnectorID = c.Connector_ID 
		  from SparxDB.dbo.t_connector c
		  where ea_guid = @GUID 
			and Stereotype = 'Host';

PRINT 'Connector = '+CAST(@ConnectorID AS NVARCHAR(10))
PRINT 'GUID = '+@GUID

--Select count(1) from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID;

--Select distinct ct.ElementID, ct.Property, ct.VALUE
--		from SparxDB.dbo.t_connector c
--		join SparxDB.dbo.t_object src on src.Object_ID = c.Start_Object_ID
--		join SparxDB.dbo.t_object dest on dest.Object_ID = c.End_Object_ID
--		join SparxDB.[dbo].[t_connectortag] ct on ct.ElementID = c.Connector_ID
--		where c.Stereotype IN ('Host')
--		and c.ea_guid = @GUID

--		Select distinct ct.Property
--		from SparxDB.dbo.t_connector c
--		join SparxDB.dbo.t_object src on src.Object_ID = c.Start_Object_ID
--		join SparxDB.dbo.t_object dest on dest.Object_ID = c.End_Object_ID
--		join SparxDB.[dbo].[t_connectortag] ct on ct.ElementID = c.Connector_ID
--		where c.Stereotype IN ('Host')
--		EXCEPT
--		Select distinct ct.Property
--		from SparxDB.dbo.t_connector c
--		join SparxDB.dbo.t_object src on src.Object_ID = c.Start_Object_ID
--		join SparxDB.dbo.t_object dest on dest.Object_ID = c.End_Object_ID
--		join SparxDB.[dbo].[t_connectortag] ct on ct.ElementID = c.Connector_ID
--		where c.Stereotype IN ('Host')
--		and c.ea_guid = @GUID

		-- Create the missing attributes aka connectortag (if any) for this particular record

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Select @ConnectorID, t.Property, NULL, NULL, CONCAT('{',NEWID(),'}') 
		from
		(
		Select distinct ct.Property
		from SparxDB.dbo.t_connector c
		join SparxDB.dbo.t_object src on src.Object_ID = c.Start_Object_ID
		join SparxDB.dbo.t_object dest on dest.Object_ID = c.End_Object_ID
		join SparxDB.[dbo].[t_connectortag] ct on ct.ElementID = c.Connector_ID
		where c.Stereotype IN ('Host')
		EXCEPT
		Select distinct ct.Property
		from SparxDB.dbo.t_connector c
		join SparxDB.dbo.t_object src on src.Object_ID = c.Start_Object_ID
		join SparxDB.dbo.t_object dest on dest.Object_ID = c.End_Object_ID
		join SparxDB.[dbo].[t_connectortag] ct on ct.ElementID = c.Connector_ID
		where c.Stereotype IN ('Host')
		and c.ea_guid = @GUID
		) t;

--Select count(1) from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID;

--Select distinct ct.ElementID, ct.Property, ct.VALUE
--		from SparxDB.dbo.t_connector c
--		join SparxDB.dbo.t_object src on src.Object_ID = c.Start_Object_ID
--		join SparxDB.dbo.t_object dest on dest.Object_ID = c.End_Object_ID
--		join SparxDB.[dbo].[t_connectortag] ct on ct.ElementID = c.Connector_ID
--		where c.Stereotype IN ('Host')
--		and c.ea_guid = @GUID


		---Update the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		UPDATE ct SET Value = 
				(CASE      
				WHEN Property = 'Application Software Replicated' THEN ISNULL(@ApplicationSoftwareReplicated,Value)
				WHEN Property = 'CCIC Integration Flag' THEN ISNULL(@CCICIntegrationFlag,Value)
				WHEN Property = 'Contractor Name' THEN ISNULL(@ContractorName,Value)
				WHEN Property = 'Data Replicated' THEN ISNULL(@DataReplicated,Value)
				WHEN Property = 'Environment' THEN ISNULL(@Environment,Value)
				WHEN Property = 'Fed Contact Org' THEN ISNULL(@FedContactOrg,Value)
				WHEN Property = 'Hot Site' THEN ISNULL(@HotSite,Value)
				WHEN Property = 'ImportLookup' THEN ISNULL(@ImportLookup,Value)
				WHEN Property = 'Incident Response Contact' THEN ISNULL(@IncidentResponseContact,Value)
				WHEN Property = 'Multi Factor Authentication' THEN ISNULL(@MultiFactorAuthentication,Value)
				WHEN Property = 'Production Data Use Flag' THEN ISNULL(@ProductionDataUseFlag,Value)
				WHEN Property = 'Relationship Impact' THEN ISNULL(@RelationshipImpact,Value)
				WHEN Property = 'Relationship Status' THEN ISNULL(@RelationshipStatus,Value)
				WHEN Property = 'System Server Software Replicated' THEN ISNULL(@SystemServerSoftwareReplicated,Value)
				WHEN Property = 'Utilizes VPN' THEN ISNULL(@UtilizesVPN,Value)
				WHEN Property = 'Other Special Users' THEN ISNULL(@OtherSpecialUsers,Value)
		
				ELSE Value
		END)
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'Host';

		Print 'Network Encryption = ' +@NetworkEncryption
		UPDATE ct SET Notes = 
				(CASE      
				WHEN Property = 'WAN Type' THEN ISNULL(@WANType,ct.Notes)
				WHEN Property = 'Users Requiring Multifactor Authentication'  THEN ISNULL(@UsersRequiringMultifactorAuthentication,ct.Notes)
				WHEN Property = 'Network Encryption' THEN ISNULL(@NetworkEncryption,ct.Notes)
				WHEN Property = 'WAN Type - Other' THEN ISNULL(@WANTypeOther,ct.Notes)
				ELSE ct.Notes
		END)
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'Host'
		and ct.Property IN ('WAN Type','Users Requiring Multifactor Authentication','Network Encryption', 'WAN Type - Other');

		UPDATE ct 
		SET Value = NULL
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'Host'
		and Value = '';

		UPDATE ct 
		SET Notes = NULL
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'Host'
		and ct.Property IN ('WAN Type','Users Requiring Multifactor Authentication','Network Encryption', 'WAN Type - Other')
		and ct.Notes = '';


PRINT 'Properties Updated'

		-- Check the Updates

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID; -- check connection value records


	END TRY
	BEGIN CATCH

PRINT 'Inside SP_Update_SystemDataCenter_Reln Exception'
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;

















