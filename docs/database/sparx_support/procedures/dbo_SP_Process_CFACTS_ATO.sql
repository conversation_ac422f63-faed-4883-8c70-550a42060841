








/*********************************************************************************************
 * PROCEDURE_NAME: SP_Process_CFACTS_ATO
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/27/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the ATO attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Process_CFACTS_ATO] 
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Process_CFACTS_ATO
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 12/17/2024	Aditya Sharma	CSUP-653	Add 3 new fields to CFACTs Threat/POAM data
 * 03/25/2025	Aditya Sharma				Backup the POAM Staging table also to retain last 3 months data
 * 06/10/2025	Aditya Sharma				Add OA Status field
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Process_CFACTS_ATO] 
--Add the parameters for the stored procedure here



AS

DECLARE 

-- ATO attributes

		@GUID nvarchar(60) = '',
		@RC Int,
		@AccessedbyNonOrgUsers nvarchar(255) = '', 
		@Acronym nvarchar(255) = '', 
		@informationSystemProgramName nvarchar(255) = '', 
		@ActualDispositionDate nvarchar(255) = '', 
		@ATOID nvarchar(255) = '', 
		@CMS_UUID nvarchar(255) = '', 
		@CollectMaintainSharePII nvarchar(255) = '', 
		@CountofOpenPOAMs nvarchar(255) = '', 
		@EAuthenticationLevel nvarchar(255) = '', 
		@EffectiveDate nvarchar(255) = '', 
		@ExpirationDate nvarchar(255) = '', 
		@FIPS199OverallImpactRating nvarchar(255) = '', 
		@HasPHI nvarchar(255) = '', 
		@LastActDate nvarchar(255) = '', 
		@LastAssessmentDate nvarchar(255) = '', 
		@LastContingencyPlanCompletionDate nvarchar(255) = '', 
		@LastPentestDate nvarchar(255) = '', 
		@NonPrivilegedUserPopulation nvarchar(255) = '', 
		@PIACompletionDate nvarchar(255) = '', 
		@PIIisLimitedtoUsernameandPassword nvarchar(255) = '', 
		@PrivacyPOC nvarchar(255) = '', 
		@PrivilegedUserPopulation nvarchar(255) = '', 
		@RecoveryPointObjective nvarchar(255) = '', 
		@RecoveryTimeObjective nvarchar(255) = '', 
		@TLCPhase nvarchar(255) = '',
		@primaryCyberRiskAdvisor nvarchar(255) = '', 
		@OAStatus nvarchar(255) = '',

-- ATO Threat attributes

		@ControlFamily nvarchar(255) = '', 
		@DaysOpen nvarchar(255) = '', 
		@RiskLevel nvarchar(255) = '', 
		@PoamId nvarchar(255) = '',
		@OverallStatus nvarchar(255) = '', 
		@ClosedDate nvarchar(255) = '', 
		@RequiredRemediationDate nvarchar(255) = '',

-- ATO SORN attributes

		@SORN nvarchar(255) = '',

-- GUID storage fields

		@ATO_GUID nvarchar(255) = '',
		@Threat_GUID nvarchar(255) = '',
		@SORN_GUID nvarchar(255) = '';

BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


-- =======================================================================================================

-- Update ATO processing

PRINT 'Starting Update ATO processing';

		-- 2 - Declare Cursor
		DECLARE db_update_cursor CURSOR FOR 
		SELECT distinct o.ea_guid, op.Value
		FROM [Staging].[CFACTS_ATO] a
		join SparxDB.dbo.t_object o on o.Stereotype = 'ATO Security Boundary'
		join SparxDB.dbo.t_objectproperties op on op.Object_ID = o.Object_ID and op.Property = 'CMS_UUID' and op.Value = a.uid
		where exists (Select 1 from dbo.[Sparx_ATO] pa where pa.CMS_UUID = a.uid);
PRINT 'Open cursor';
		-- Open the Cursor
		OPEN db_update_cursor
PRINT 'Fetch';
		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_update_cursor INTO @GUID, @CMS_UUID   

		-- Set the status for the cursor
		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			-- 4 - Begin the custom business logic
PRINT 'GUID = '+@GUID+'   CMS_UUID = ' + @CMS_UUID;		
			Select 
				 @Acronym = [acronym]
				--,[dataFeedTrackingId]
				,@informationSystemProgramName = [informationSystemProgramName]
				,@ActualDispositionDate = [actualDispositionDate]
				,@CollectMaintainSharePII = [containsPersonallyIdentifiableInformation]
				,@CountofOpenPOAMs = [countOfOpenPoams]
				,@NonPrivilegedUserPopulation = [countOfTotalNonPrivilegedUserPopulation]
				,@PrivilegedUserPopulation = [countOfTotalPrivilegedUserPopulation]
				,@EffectiveDate = [dateAuthorizationMemoSigned]
				,@ExpirationDate = [dateAuthorizationMemoExpires]
				,@EAuthenticationLevel = [eAuthenticationLevel]
				,@FIPS199OverallImpactRating = [fips199OverallImpactRating]
				,@AccessedbyNonOrgUsers = [isAccessedByNonOrganizationalUsers]
				,@PIIisLimitedtoUsernameandPassword = [isPiiLimitedToUserNameAndPass]
				,@HasPHI = [isProtectedHealthInformation]
				,@LastActDate = [lastActScaDate]
				,@LastContingencyPlanCompletionDate = [lastContingencyPlanCompletionDate]
				,@LastAssessmentDate = [lastAssessmentDate]
				,@LastPentestDate = [lastPenTestDate]
				,@PIACompletionDate = [piaCompletionDate]
				,@primaryCyberRiskAdvisor = [primaryCyberRiskAdvisor]
				,@PrivacyPOC = [privacySubjectMatterExpert]
				,@RecoveryPointObjective = [recoveryPointObjective]
				,@RecoveryTimeObjective = [recoveryTimeObjective]
				--,[systemOfRecordsNotice]
				,@TLCPhase = [tlcPhase]
				,@OAStatus = [OAStatus]
				,@CMS_UUID = [uid]
			FROM [Staging].[CFACTS_ATO]
			WHERE uid = @CMS_UUID;

PRINT 'Before Update call';
			EXECUTE  @RC = [dbo].[SP_Update_ATO] 
					 @informationSystemProgramName
					,@AccessedbyNonOrgUsers
					,@Acronym
					,@ActualDispositionDate
					,NULL
					,@CMS_UUID
					,@CollectMaintainSharePII
					,@CountofOpenPOAMs
					,@EAuthenticationLevel
					,@EffectiveDate
					,@ExpirationDate
					,@FIPS199OverallImpactRating
					,@HasPHI
					,@LastActDate
					,@LastAssessmentDate
					,@LastContingencyPlanCompletionDate
					,@LastPentestDate
					,@NonPrivilegedUserPopulation
					,@PIACompletionDate
					,@PIIisLimitedtoUsernameandPassword
					,@PrivacyPOC
					,@PrivilegedUserPopulation
					,@RecoveryPointObjective
					,@RecoveryTimeObjective
					,@TLCPhase
					,@primaryCyberRiskAdvisor
					,@OAStatus
					,@GUID;
					

PRINT 'After Update Call';
			-- 5 - Fetch the next record from the cursor
 			FETCH NEXT FROM db_update_cursor INTO @GUID, @CMS_UUID; 
		END 
PRINT 'Close Cursor';
		-- 6 - Close the cursor
		CLOSE db_update_cursor;  
PRINT 'Deallocate cursor';
		-- 7 - Deallocate the cursor
		DEALLOCATE db_update_cursor; 


-- =======================================================================================================

-- Add ATO processing

PRINT 'Starting ATO Add processing';

		-- 2 - Declare Cursor
		DECLARE db_add_cursor CURSOR FOR 
		SELECT distinct o.ea_guid, o.Alias 
		FROM [Staging].[CFACTS_ATO] a
		join SparxDB.dbo.t_object o on o.Alias = a.uid
		where not exists (Select 1 from dbo.[Sparx_ATO] pa where pa.CMS_UUID = a.uid)
		--and o.ea_guid in ('{91F8C6AA-AA5A-4975-9751-BCBF1BB7F37F}','{FAE80936-ED22-458a-88D3-7CD110CD798E}','{FB0560A6-7E89-4aca-93BE-8D102973A49B}')

PRINT 'Open Cursor';

		-- Open the Cursor
		OPEN db_add_cursor
PRINT 'Fetch';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_cursor INTO @GUID, @CMS_UUID  
PRINT @GUID+' - '+ @CMS_UUID;  
PRINT 'Start Loop';
		-- Set the status for the cursor
		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			-- 4 - Begin the custom business logic
PRINT 'Select values';			
			Select 
				 @Acronym = [acronym]
				--,[dataFeedTrackingId]
				,@informationSystemProgramName = [informationSystemProgramName]
				,@ActualDispositionDate = [actualDispositionDate]
				,@CollectMaintainSharePII = [containsPersonallyIdentifiableInformation]
				,@CountofOpenPOAMs = [countOfOpenPoams]
				,@NonPrivilegedUserPopulation = [countOfTotalNonPrivilegedUserPopulation]
				,@PrivilegedUserPopulation = [countOfTotalPrivilegedUserPopulation]
				,@EffectiveDate = [dateAuthorizationMemoSigned]
				,@ExpirationDate = [dateAuthorizationMemoExpires]
				,@EAuthenticationLevel = [eAuthenticationLevel]
				,@FIPS199OverallImpactRating = [fips199OverallImpactRating]
				,@AccessedbyNonOrgUsers = [isAccessedByNonOrganizationalUsers]
				,@PIIisLimitedtoUsernameandPassword = [isPiiLimitedToUserNameAndPass]
				,@HasPHI = [isProtectedHealthInformation]
				,@LastActDate = [lastActScaDate]
				,@LastContingencyPlanCompletionDate = [lastContingencyPlanCompletionDate]
				,@LastAssessmentDate = [lastAssessmentDate]
				,@LastPentestDate = [lastPenTestDate]
				,@PIACompletionDate = [piaCompletionDate]
				,@primaryCyberRiskAdvisor = [primaryCyberRiskAdvisor]
				,@PrivacyPOC = [privacySubjectMatterExpert]
				,@RecoveryPointObjective = [recoveryPointObjective]
				,@RecoveryTimeObjective = [recoveryTimeObjective]
				--,[systemOfRecordsNotice]
				,@TLCPhase = [tlcPhase]
				,@OAStatus = [OAStatus]
			FROM [Staging].[CFACTS_ATO]
			WHERE uid = @CMS_UUID;

PRINT 'POAM = ' + @CountofOpenPOAMs;

PRINT 'Call Insert';

			EXECUTE @RC = [dbo].[SP_Insert_ATO] 
					 @AccessedbyNonOrgUsers
					,@Acronym
					,@ActualDispositionDate
					,@CMS_UUID
					,@CMS_UUID
					,@CollectMaintainSharePII
					,@CountofOpenPOAMs
					,@EAuthenticationLevel
					,@EffectiveDate
					,@ExpirationDate
					,@FIPS199OverallImpactRating
					,@HasPHI
					,@informationSystemProgramName
					,@LastActDate
					,@LastAssessmentDate
					,@LastContingencyPlanCompletionDate
					,@LastPentestDate
					,@NonPrivilegedUserPopulation
					,@PIACompletionDate
					,@PIIisLimitedtoUsernameandPassword
					,@PrivacyPOC
					,@PrivilegedUserPopulation
					,@RecoveryPointObjective
					,@RecoveryTimeObjective
					,@TLCPhase
					,@primaryCyberRiskAdvisor
					,@OAStatus
					,@GUID;

PRINT 'After Call Insert';

			-- 5 - Fetch the next record from the cursor
 			FETCH NEXT FROM db_add_cursor INTO @GUID,  @CMS_UUID
		END 
PRINT 'Close loop';
		-- 6 - Close the cursor
		CLOSE db_add_cursor  
PRINT 'Deallocate and end Add ATO';
		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_cursor 


-- =======================================================================================================

-- Update ATO Threat processing

PRINT 'Starting Update ATO Threat processing';

		-- 2 - Declare Cursor
		DECLARE db_update_threat_cursor CURSOR FOR 
		SELECT distinct o.ea_guid, o.Name 
		FROM [Staging].[CFACTS_ATO_POAM] a
		join SparxDB.dbo.t_object o on o.Name = a.id 
		where exists (Select 1 from dbo.[Sparx_ATO_Threat] pa where pa.Threat = a.id)

PRINT 'Open Cursor';

		-- Open the Cursor
		OPEN db_update_threat_cursor
PRINT 'Fetch';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_update_threat_cursor INTO @GUID, @PoamId

PRINT 'GUID - '+@GUID+'  POAM -'+ @PoamId;  
PRINT 'Start Loop';
		-- Set the status for the cursor
		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			-- 4 - Begin the custom business logic
PRINT 'Select values';			
			Select 
				 @ControlFamily = [controlFamily]
				,@DaysOpen = [daysOpen]
				,@RiskLevel = [weaknessRiskLevel]
				,@OverallStatus = [overallStatus]
				,@ClosedDate = [closedDate]
				,@RequiredRemediationDate = [requiredRemediationDate]
			FROM [Staging].[CFACTS_ATO_POAM]
			WHERE id = @PoamId;

PRINT '[daysOpen] = ' + @DaysOpen;
PRINT 'PoamId = '+@PoamId;
PRINT 'OverallStatus = '+@OverallStatus
PRINT 'GUID = '+@GUID;


PRINT 'Call Update Threat';

			EXECUTE	 @RC = [dbo].[SP_Update_ATO_Threat] 
					 @ControlFamily
					,@DaysOpen
					,@RiskLevel
					,@PoamId
					,@GUID
					,@OverallStatus
					,@ClosedDate
					,@RequiredRemediationDate;


PRINT 'After Update Call';

			-- 5 - Fetch the next record from the cursor
 			FETCH NEXT FROM db_update_threat_cursor INTO @GUID, @PoamId
		END 
PRINT 'Close loop';
		-- 6 - Close the cursor
		CLOSE db_update_threat_cursor  
PRINT 'Deallocate and close Update Threat processing';
		-- 7 - Deallocate the cursor
		DEALLOCATE db_update_threat_cursor 

-- =======================================================================================================

-- Add ATO Threat processing

PRINT 'Starting Add Threat processing';

		-- 2 - Declare Cursor
		DECLARE db_add_threat_cursor CURSOR FOR 
		SELECT distinct o.ea_guid, o.Name 
		FROM [Staging].[CFACTS_ATO_POAM] a
		join SparxDB.dbo.t_object o on o.Name = a.id 
		where not exists (Select 1 from dbo.[Sparx_ATO_Threat] pa where pa.Threat = a.id)
		--and o.ea_guid in ('{A96B9A74-0277-456f-BC5E-2F3109A94710}','{64AE7841-DDF5-4962-9A50-22B06DB00656}','{E758A58A-0D4C-4f57-83CF-196F055A91E9}','{2C11DAD5-A467-41a6-8092-62AAFB59A875}')

PRINT 'Open Cursor';

		-- Open the Cursor
		OPEN db_add_threat_cursor
PRINT 'Fetch';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_threat_cursor INTO @GUID, @PoamId

PRINT 'GUID - '+@GUID+'  POAM - '+ @PoamId;  
PRINT 'Start Loop';
		-- Set the status for the cursor
		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			-- 4 - Begin the custom business logic
PRINT 'Select values';			
			Select 
				 @ControlFamily = [controlFamily]
				,@DaysOpen = [daysOpen]
				,@RiskLevel = [weaknessRiskLevel]
				,@OverallStatus = [overallStatus]
				,@ClosedDate = [closedDate]
				,@RequiredRemediationDate = [requiredRemediationDate]
			FROM [Staging].[CFACTS_ATO_POAM]
			WHERE id = @PoamId;

PRINT '[daysOpen] = ' + @DaysOpen;
PRINT 'PoamId = '+@PoamId;
PRINT 'GUID = '+@GUID;

PRINT 'Call Insert';

			EXECUTE	 @RC = [dbo].[SP_Insert_ATO_Threat] 
					 @ControlFamily
					,@DaysOpen
					,@RiskLevel
					,@PoamId
					,@GUID
					,@OverallStatus
					,@ClosedDate
					,@RequiredRemediationDate;

PRINT 'After Call Insert';

			-- 5 - Fetch the next record from the cursor
 			FETCH NEXT FROM db_add_threat_cursor INTO @GUID,  @PoamId
		END 
PRINT 'Close loop';
		-- 6 - Close the cursor
		CLOSE db_add_threat_cursor  
PRINT 'Deallocate and end Threat Add';
		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_threat_cursor 

-- =======================================================================================================

-- Add ATO Threat Reln

PRINT 'Starting ATO Threat Reln processing';

		SET @ATO_GUID = '' ;
		SET @Threat_GUID = '';

		-- 2 - Declare Cursor

		DECLARE db_add_ATOThreat_cursor CURSOR FOR 
		SELECT distinct ao.ea_guid as ATO_GUID, ath.ea_guid as Threat_GUID 
		FROM [Staging].[CFACTS_ATO_POAM] a
		join SparxDB.dbo.t_object ao on ao.Alias = a.uid
		join SparxDB.dbo.t_object ath on ath.Alias = a.id
		where not exists (Select 1 from [dbo].[Sparx_ATO_Threat] atv where atv.[Sparx ATO GUID] = ao.ea_guid and atv.[Sparx Threat GUID] = ath.ea_guid)

PRINT 'Open Cursor';

		-- Open the Cursor
		OPEN db_add_ATOThreat_cursor
PRINT 'Fetch';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_ATOThreat_cursor INTO @ATO_GUID, @Threat_GUID

PRINT 'ATO Threat Reln GUIDs :'+@ATO_GUID+'  '+@Threat_GUID;

PRINT 'Start Loop';
		-- Set the status for the cursor
		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			-- 4 - Begin the custom business logic
PRINT 'Call ATOThreat_Reln Insert';

			EXECUTE	 @RC = [dbo].[SP_Insert_ATOThreat_Reln] 
					 @ATO_GUID
					,@Threat_GUID;

PRINT 'After ATOThreat_Reln Insert Call';

			-- 5 - Fetch the next record from the cursor
 			FETCH NEXT FROM db_add_ATOThreat_cursor INTO @ATO_GUID, @Threat_GUID
		END 
PRINT 'Close loop';
		-- 6 - Close the cursor
		CLOSE db_add_ATOThreat_cursor  
PRINT 'Deallocate and end ATOThreat_Reln creation';
		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_ATOThreat_cursor 


-- =======================================================================================================

-- Add ATO SORN 


PRINT 'Starting SORN add';

		SET @SORN_GUID = '' ;
		SET @SORN = '';

		-- 2 - Declare Cursor
		DECLARE db_add_sorn_cursor CURSOR FOR 
		SELECT	DISTINCT 
			 ao.ea_guid as SORN_GUID
			,[systemOfRecordsNotice]
		FROM
		(
			SELECT
					[uid],
					[systemOfRecordsNotice] = LTRIM(RTRIM(m.n.value('.[1]','varchar(8000)'))) 
			FROM
			(
				SELECT	 [acronym]
						,[uid]
						,CAST('<XMLRoot><RowData>' + REPLACE(REPLACE([systemOfRecordsNotice],'|',','),',','</RowData><RowData>') + '</RowData></XMLRoot>' AS XML) AS x
				FROM [Sparx_Support].[Staging].[CFACTS_ATO]
				where ISNULL([systemOfRecordsNotice],'') != ''
			) t
			CROSS APPLY x.nodes('/XMLRoot/RowData')m(n)
		) t1
		join SparxDB.dbo.t_object ao on ao.Alias = t1.systemOfRecordsNotice
		where not exists (Select 1 from [dbo].Sparx_ATO_SORN os where os.SORN = t1.systemOfRecordsNotice)

PRINT 'Open Cursor';

		-- Open the Cursor
		OPEN db_add_sorn_cursor
PRINT 'Fetch';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_sorn_cursor INTO @SORN_GUID, @SORN

PRINT 'Sorn = '+ @SORN;  
PRINT 'Start Loop';
		-- Set the status for the cursor
		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			-- 4 - Begin the custom business logic
PRINT 'Call SORN Insert';

			EXECUTE	 @RC = [dbo].[SP_Insert_SORN] 
					 @SORN
					,@SORN_GUID;

PRINT 'After SORN Insert Call';

			-- 5 - Fetch the next record from the cursor
 			FETCH NEXT FROM db_add_sorn_cursor INTO @SORN_GUID, @SORN

		END 
PRINT 'Close loop';
		-- 6 - Close the cursor
		CLOSE db_add_sorn_cursor  
PRINT 'Deallocate and end SORN Add';
		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_sorn_cursor 


-- =======================================================================================================

-- Add ATO SORN Reln

PRINT 'Starting ATO SORN Reln Processing';

		SET @SORN_GUID = '' ;
		SET @ATO_GUID = '';

-- Delete existing SORN reln for ATO's with SORN value sent over

		DELETE FROM SparxDB.dbo.t_xref
		where Client IN
		(
			SELECT	DISTINCT 
				t.ea_guid
			FROM
			(
				SELECT
						[uid],
						[systemOfRecordsNotice] = LTRIM(RTRIM(m.n.value('.[1]','varchar(8000)'))) 
				FROM
				(
					SELECT	 [acronym]
							,[uid]
							,CAST('<XMLRoot><RowData>' + REPLACE(REPLACE([systemOfRecordsNotice],'|',','),',','</RowData><RowData>') + '</RowData></XMLRoot>' AS XML) AS x
					FROM [Sparx_Support].[Staging].[CFACTS_ATO]
					where ISNULL([systemOfRecordsNotice],'') != ''
				) t
				CROSS APPLY x.nodes('/XMLRoot/RowData')m(n)
			) t1
			join SparxDB.dbo.t_object ao on ao.Alias = t1.uid and ao.Stereotype = 'ATO Security Boundary'
			join SparxDB.dbo.t_connector t on t.Start_Object_ID = ao.Object_ID 
			join SparxDB.dbo.t_object aso on aso.Stereotype = 'SORN' and aso.Object_ID = t.End_Object_ID
		);


		DELETE FROM SparxDB.dbo.t_connector
		where ea_guid IN
		(
			SELECT	DISTINCT 
				t.ea_guid
			FROM
			(
				SELECT
						[uid],
						[systemOfRecordsNotice] = LTRIM(RTRIM(m.n.value('.[1]','varchar(8000)'))) 
				FROM
				(
					SELECT	 [acronym]
							,[uid]
							,CAST('<XMLRoot><RowData>' + REPLACE(REPLACE([systemOfRecordsNotice],'|',','),',','</RowData><RowData>') + '</RowData></XMLRoot>' AS XML) AS x
					FROM [Sparx_Support].[Staging].[CFACTS_ATO]
					where ISNULL([systemOfRecordsNotice],'') != ''
				) t
				CROSS APPLY x.nodes('/XMLRoot/RowData')m(n)
			) t1
			join SparxDB.dbo.t_object ao on ao.Alias = t1.uid and ao.Stereotype = 'ATO Security Boundary'
			join SparxDB.dbo.t_connector t on t.Start_Object_ID = ao.Object_ID 
			join SparxDB.dbo.t_object aso on aso.Stereotype = 'SORN' and aso.Object_ID = t.End_Object_ID
		);

		-- 2 - Declare Cursor
		DECLARE db_add_ATOSORN_cursor CURSOR FOR 
		SELECT	DISTINCT 
			 ao.ea_guid as ATO_GUID
			,aso.ea_guid as SORN_GUID
		FROM
		(
			SELECT
					[uid],
					[systemOfRecordsNotice] = LTRIM(RTRIM(m.n.value('.[1]','varchar(8000)'))) 
			FROM
			(
				SELECT	 [acronym]
						,[uid]
						,CAST('<XMLRoot><RowData>' + REPLACE(REPLACE([systemOfRecordsNotice],'|',','),',','</RowData><RowData>') + '</RowData></XMLRoot>' AS XML) AS x
				FROM [Sparx_Support].[Staging].[CFACTS_ATO]
				where ISNULL([systemOfRecordsNotice],'') != ''
			) t
			CROSS APPLY x.nodes('/XMLRoot/RowData')m(n)
		) t1
		join SparxDB.dbo.t_object ao on ao.Alias = t1.uid and ao.Stereotype = 'ATO Security Boundary'
		join SparxDB.dbo.t_object aso on aso.Alias = t1.systemOfRecordsNotice and aso.Stereotype = 'SORN'
		where not exists (Select 1 from [dbo].Sparx_ATO_SORN os where os.SORN = t1.systemOfRecordsNotice and os.CMS_UUID = t1.uid)


PRINT 'Open Cursor';

		-- Open the Cursor
		OPEN db_add_ATOSORN_cursor
PRINT 'Fetch';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_ATOSORN_cursor INTO @ATO_GUID, @SORN_GUID

PRINT 'ATO SORN Reln GUIDs:'+@ATO_GUID+'   '+@SORN_GUID;
PRINT 'Start Loop';
		-- Set the status for the cursor
		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			-- 4 - Begin the custom business logic
PRINT 'Call ATO SORN Reln Insert';

			EXECUTE	 @RC = [dbo].[SP_Insert_ATOSORN_Reln] 
					 @ATO_GUID
					,@SORN_GUID;

PRINT 'After ATO SORN Reln Insert Call';

			-- 5 - Fetch the next record from the cursor
 			FETCH NEXT FROM db_add_ATOSORN_cursor INTO @ATO_GUID, @SORN_GUID
		END 
PRINT 'Close loop';
		-- 6 - Close the cursor
		CLOSE db_add_ATOSORN_cursor  
PRINT 'Deallocate and end ATO SORN Reln process';
		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_ATOSORN_cursor 

--------------------------------------------------------------------------------------------------------------------

-- Clean out Staging tables

	-- Clean out Prior Run recs more than 3 months old
		DELETE FROM [Staging].[CFACTS_ATO_PriorRun] where DATEDIFF(d,[Process_Date], GETDATE()) > 90 ;
		DELETE FROM [Staging].[CFACTS_ATO_POAM_PriorRun] where DATEDIFF(d,[Process_Date], GETDATE()) > 90 ;

	-- Insert the current run records into Prior Run

		INSERT INTO [Staging].[CFACTS_ATO_PriorRun]
           ([Process_Date]
           ,[acronym]
           ,[dataFeedTrackingId]
           ,[informationSystemProgramName]
           ,[actualDispositionDate]
           ,[containsPersonallyIdentifiableInformation]
           ,[countOfOpenPoams]
           ,[countOfTotalNonPrivilegedUserPopulation]
           ,[countOfTotalPrivilegedUserPopulation]
           ,[dateAuthorizationMemoSigned]
           ,[dateAuthorizationMemoExpires]
           ,[eAuthenticationLevel]
           ,[fips199OverallImpactRating]
           ,[isAccessedByNonOrganizationalUsers]
           ,[isPiiLimitedToUserNameAndPass]
           ,[isProtectedHealthInformation]
           ,[lastActScaDate]
           ,[lastContingencyPlanCompletionDate]
           ,[lastAssessmentDate]
           ,[lastPenTestDate]
           ,[piaCompletionDate]
           ,[primaryCyberRiskAdvisor]
           ,[privacySubjectMatterExpert]
           ,[recoveryPointObjective]
           ,[recoveryTimeObjective]
           ,[systemOfRecordsNotice]
           ,[tlcPhase]
           ,[uid]
           ,[xlcPhase]
           ,[PlanOfActionsAndMilestonesList]
		   ,[OAStatus])
		SELECT
			[Process_Date] = GETDATE()
           ,[acronym]
           ,[dataFeedTrackingId]
           ,[informationSystemProgramName]
           ,[actualDispositionDate]
           ,[containsPersonallyIdentifiableInformation]
           ,[countOfOpenPoams]
           ,[countOfTotalNonPrivilegedUserPopulation]
           ,[countOfTotalPrivilegedUserPopulation]
           ,[dateAuthorizationMemoSigned]
           ,[dateAuthorizationMemoExpires]
           ,[eAuthenticationLevel]
           ,[fips199OverallImpactRating]
           ,[isAccessedByNonOrganizationalUsers]
           ,[isPiiLimitedToUserNameAndPass]
           ,[isProtectedHealthInformation]
           ,[lastActScaDate]
           ,[lastContingencyPlanCompletionDate]
           ,[lastAssessmentDate]
           ,[lastPenTestDate]
           ,[piaCompletionDate]
           ,[primaryCyberRiskAdvisor]
           ,[privacySubjectMatterExpert]
           ,[recoveryPointObjective]
           ,[recoveryTimeObjective]
           ,[systemOfRecordsNotice]
           ,[tlcPhase]
           ,[uid]
           ,[xlcPhase]
           ,[PlanOfActionsAndMilestonesList]
		   ,[OAStatus]
		FROM [Staging].[CFACTS_ATO];

		INSERT INTO [Staging].[CFACTS_ATO_POAM_PriorRun]
           ([Process_Date]
           ,[uid]
           ,[id]
           ,[controlFamily]
           ,[daysOpen]
           ,[weaknessRiskLevel]
           ,[overallStatus]
           ,[closedDate]
           ,[requiredRemediationDate])
		SELECT
			[Process_Date] = GETDATE()
		   ,[uid]
           ,[id]
		   ,[controlFamily]
		   ,[daysOpen]
		   ,[weaknessRiskLevel]
		   ,[overallStatus]
		   ,[closedDate]
		   ,[requiredRemediationDate]
		FROM [Staging].[CFACTS_ATO_POAM];

		DELETE FROM [Staging].[CFACTS_ATO];
		DELETE FROM [Staging].[CFACTS_ATO_POAM];

--------------------------------------------------------------------------------------------------------------------

PRINT 'At end of Process CFACTS ATO';
	END TRY

	BEGIN CATCH
PRINT 'In Process CFACTS ATO exception';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;













