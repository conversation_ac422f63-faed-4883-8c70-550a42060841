






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Get_SystemList
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/08/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Get the SystemList attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 	DECLARE @Outputjson nvarchar(max);
	DECLARE @RC INT;

	EXECUTE @RC = [dbo].[SP_Get_SystemList] 
			 'Active'
			,NULL
			,NULL
			,NULL
			,NULL
			,@Outputjson OUT;

	SELECT @Outputjson;

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Get_SystemList
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 05/31/2024	Aditya Sharma				Change the source from View to Table
 * 06/13/2024	Aditya Sharma				Change the source Table and add ATO Dates to Output
 * 08/13/2024	Aditya Sharma				Change the Survey Indicator
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Get_SystemList] 
--Add the parameters for the stored procedure here
	@objectState nvarchar(255) = NULL,
	@status nvarchar(255) = NULL, 
	@version nvarchar(255) = NULL, 
	@belongsTo nvarchar(255) = NULL, 
	@includeInSurvey nvarchar(255) = NULL,
	@Outputjson nvarchar(max) OUT

AS

BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		SET @Outputjson = 
		(Select
            a.[Sparx System GUID] AS [id],
            ISNULL(CAST(a.[nextVersionID] AS NVARCHAR(30)),'') as nextVersionId,
            ISNULL(CAST(a.[previousVersionID] AS NVARCHAR(30)),'') as previousVersionId,
            a.[Sparx System GUID] as ictObjectId,
            ISNULL(CAST(a.[CMS UUID] AS NVARCHAR(40)),'') as uuid,
            a.[System Name] AS [name],
            a.Description AS [description],
            ISNULL(CAST(a.VERSION AS NVARCHAR(30)),'') AS [version], 
            ISNULL(a.[Acronym],'') AS [acronym], 
            ISNULL(CAST(a.[Object State] AS NVARCHAR(30)),'') as [objectState] ,
            ISNULL(CAST(a.STATUS AS NVARCHAR(30)),'') AS [status],
            ISNULL(CAST(a.[belongsTo] AS NVARCHAR(40)),'') AS [belongsTo],
            ISNULL(a.[businessOwnerOrg],'') AS [businessOwnerOrg],
            ISNULL(a.[Business Owner Organization Component],'') AS [businessOwnerComp],
            ISNULL(a.[System Maintainer Organization],'') AS [systemMaintainerOrg], 
            ISNULL(a.[System Maintainer Organization Component],'') AS [systemMaintainerComp],
			ISNULL(a.[ATO Effective Date],'') AS [ATO Effective Date],
			ISNULL(a.[ATO Expiration Date],'') AS [ATO Expiration Date]
		FROM [Sparx_Support].[CEDAR_API].[Sparx_EASi_System_Tbl] a
		WHERE	(@objectState is null or (@objectState is not null and @objectState = a.[Object State]))
			AND (@status is null or (@status is not null and @status = a.STATUS))
			AND (@version is null or (@version is not null and @version = a.VERSION))
			AND (@belongsTo is null or (@belongsTo is not null and @belongsTo = a.[belongsTo]))
			AND (@includeInSurvey is null or ((@includeInSurvey = 'TRUE' and @includeInSurvey = a.[Current System Survey]) or (@includeInSurvey = 'FALSE' and @includeInSurvey = a.[Current System Survey])))
		FOR JSON PATH, ROOT('ResultSet')
		)

--Select @Outputjson;

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












