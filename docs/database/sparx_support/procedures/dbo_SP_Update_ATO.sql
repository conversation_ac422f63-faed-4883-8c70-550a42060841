




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_ATO
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/02/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theATO attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_ATO] 
			 @informationSystemProgramName
			,@AccessedbyNonOrgUsers
			,@Acronym
			,@ActualDispositionDate
			,@ATOID
			,@CMS_UUID
			,@CollectMaintainSharePII
			,@CountofOpenPOAMs
			,@EAuthenticationLevel
			,@EffectiveDate
			,@ExpirationDate
			,@FIPS199OverallImpactRating
			,@HasPHI
			,@LastActDate
			,@LastAssessmentDate
			,@LastContingencyPlanCompletionDate
			,@LastPentestDate
			,@NonPrivilegedUserPopulation
			,@PIACompletionDate
			,@PIIisLimitedtoUsernameandPassword
			,@PrivacyPOC
			,@PrivilegedUserPopulation
			,@RecoveryPointObjective
			,@RecoveryTimeObjective
			,@TLCPhase
			,@primaryCyberRiskAdvisor
			,@OAStatus
			,@GUID
			
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_ATO
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 06/10/2025	Aditya Sharma				Add OA Status field
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_ATO] 
--Add the parameters for the stored procedure here
	@informationSystemProgramName nvarchar(255) = '', 
	@AccessedbyNonOrgUsers nvarchar(255) = '', 
	@Acronym nvarchar(255) = '', 
	@ActualDispositionDate nvarchar(255) = '', 
	@ATOID nvarchar(255) = '', 
	@CMS_UUID nvarchar(255) = '', 
	@CollectMaintainSharePII nvarchar(255) = '', 
	@CountofOpenPOAMs nvarchar(255) = '', 
	@EAuthenticationLevel nvarchar(255) = '', 
	@EffectiveDate nvarchar(255) = '', 
	@ExpirationDate nvarchar(255) = '', 
	@FIPS199OverallImpactRating nvarchar(255) = '', 
	@HasPHI nvarchar(255) = '', 
	@LastActDate nvarchar(255) = '', 
	@LastAssessmentDate nvarchar(255) = '', 
	@LastContingencyPlanCompletionDate nvarchar(255) = '', 
	@LastPentestDate nvarchar(255) = '', 
	@NonPrivilegedUserPopulation nvarchar(255) = '', 
	@PIACompletionDate nvarchar(255) = '', 
	@PIIisLimitedtoUsernameandPassword nvarchar(255) = '', 
	@PrivacyPOC nvarchar(255) = '', 
	@PrivilegedUserPopulation nvarchar(255) = '', 
	@RecoveryPointObjective nvarchar(255) = '', 
	@RecoveryTimeObjective nvarchar(255) = '', 
	@TLCPhase nvarchar(255) = '', 
	@primaryCyberRiskAdvisor nvarchar(255) = '', 
	@OAStatus nvarchar(255) = '',
	@GUID nvarchar(60) = ''
	

AS

PRINT 'Inside Update ATO';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_object SET Name = @informationSystemProgramName
		WHERE t_object.ea_guid = @GUID;


		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'Accessed by Non-Org Users' THEN ISNULL(@AccessedbyNonOrgUsers,Value)
				WHEN Property = 'Acronym' THEN ISNULL(@Acronym,Value)
				WHEN Property = 'Actual Disposition Date' THEN ISNULL(@ActualDispositionDate,Value)
				WHEN Property = 'ATO ID' THEN ISNULL(@ATOID,Value)
				WHEN Property = 'CMS_UUID' THEN ISNULL(@CMS_UUID,Value)
				WHEN Property = 'Collect Maintain Share PII' THEN ISNULL(@CollectMaintainSharePII,Value)
				WHEN Property = 'Count of Open POAMs' THEN ISNULL(@CountofOpenPOAMs,Value)
				WHEN Property = 'EAuthentication Level' THEN ISNULL(@EAuthenticationLevel,Value)
				WHEN Property = 'Effective Date' THEN ISNULL(@EffectiveDate,Value)
				WHEN Property = 'Expiration Date' THEN ISNULL(@ExpirationDate,Value)
				WHEN Property = 'FIPS 199 Overall Impact Rating' THEN ISNULL(@FIPS199OverallImpactRating,Value)
				WHEN Property = 'Has PHI' THEN ISNULL(@HasPHI,Value)
				WHEN Property = 'Last Act Date' THEN ISNULL(@LastActDate,Value)
				WHEN Property = 'Last Assessment Date' THEN ISNULL(@LastAssessmentDate,Value)
				WHEN Property = 'Last Contingency Plan Completion Date' THEN ISNULL(@LastContingencyPlanCompletionDate,Value)
				WHEN Property = 'Last Pentest Date' THEN ISNULL(@LastPentestDate,Value)
				WHEN Property = 'NonPrivileged User Population' THEN ISNULL(@NonPrivilegedUserPopulation,Value)
				WHEN Property = 'PIA Completion Date' THEN ISNULL(@PIACompletionDate,Value)
				WHEN Property = 'PII is Limited to Username and Password' THEN ISNULL(@PIIisLimitedtoUsernameandPassword,Value)
				WHEN Property = 'Privacy POC' THEN ISNULL(@PrivacyPOC,Value)
				WHEN Property = 'Privileged User Population' THEN ISNULL(@PrivilegedUserPopulation,Value)
				WHEN Property = 'Recovery Point Objective' THEN ISNULL(@RecoveryPointObjective,Value)
				WHEN Property = 'Recovery Time Objective' THEN ISNULL(@RecoveryTimeObjective,Value)
				WHEN Property = 'TLC Phase' THEN ISNULL(@TLCPhase,Value)
				WHEN Property = 'Primary Cyber Risk Advisor' THEN ISNULL(@primaryCyberRiskAdvisor,Value)
				WHEN Property = 'OA Status' THEN ISNULL(@OAStatus,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'ATO Security Boundary';

		--UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		--FROM		SparxDB.dbo.t_object 
		--INNER JOIN	SparxDB.dbo.t_objectproperties 
		--	ON t_object.Object_ID = t_objectproperties.Object_ID
		--WHERE t_object.ea_guid = @GUID
		--and t_object.Stereotype = 'ATO Security Boundary'
		--and Value = '';

PRINT 'End of Update ATO';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update ATO Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;





