









/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_URL
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/31/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theURL attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *  DECLARE @RC INT;
 *	EXECUTE @RC = [dbo].[SP_Update_URL] 
			 @ConfidenceLevel
			,@HostingEnvironment
			,@IsIntranetOnly
			,@PortalServicesUsed
			,@ProvidesVersionCodeRepositoryAccess
			,@URLAPIAWF
			,@URLAPIEndpoint
			,@URLLink
			,@UsedforBeneficiary
			,@UsesHTTPS
			,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_URL
 *
 * UPDATE HISTORY:
 * ---------------------------------------------------------------------------------------------------------------------------
 * Date			Changed By			Issue/Bug								Description
 * 08/07/2023	David Scarborough	GUID							Input GUID changed from Object to Connection logic
 *																	Use [dbo].[Sparx_System_URL].[Connection GUID] 
 * 09/25/2023	Aditya Sharma		GUID							Based on api Input parameters, the GUID is changed to use the URL GUID
 *
 *
 * ----------------------------------------------------------------------------------------------------------------------------
 ******************************************************************************************************************************/
CREATE    PROCEDURE [dbo].[SP_Update_URL] 
--Add the parameters for the stored procedure here
	@ConfidenceLevel nvarchar(255) = NULL, 
	@HostingEnvironment nvarchar(255) = NULL, 
	@IsIntranetOnly nvarchar(255) = NULL, 
	@PortalServicesUsed nvarchar(255) = NULL, 
	@ProvidesVersionCodeRepositoryAccess nvarchar(255) = NULL, 
	@URLAPIAWF nvarchar(255) = NULL, 
	@URLAPIEndpoint nvarchar(255) = NULL, 
	@URLLink nvarchar(255) = NULL, 
	@UsedforBeneficiary nvarchar(255) = NULL, 
	@UsesHTTPS nvarchar(255) = NULL, 
	@GUID nvarchar(60) = ''  --Use [dbo].[Sparx_System_URL].[Sparx URL GUID] 

AS

DECLARE @URL_ObjectId INT;

PRINT 'Inside Update URL';
PRINT 'GUID: ' +@GUID ;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		PRINT 'Before Update'

		Select @URL_ObjectId = Object_ID
		  from SparxDB.dbo.t_object
		 where ea_guid = @GUID;

		UPDATE SparxDB.dbo.t_objectproperties 
		   SET Value = 
				CASE	WHEN Property = 'Confidence Level' THEN ISNULL(@ConfidenceLevel,Value)
						WHEN Property = 'Hosting Environment' THEN ISNULL(@HostingEnvironment,Value)
						WHEN Property = 'Is Intranet Only' THEN ISNULL(@IsIntranetOnly,Value)
						WHEN Property = 'Portal Services Used' THEN ISNULL(@PortalServicesUsed,Value)
						WHEN Property = 'Provides Version Code Repository Access' THEN ISNULL(@ProvidesVersionCodeRepositoryAccess,Value)
						WHEN Property = 'URL API AWF' THEN ISNULL(@URLAPIAWF,Value)
						WHEN Property = 'URL API Endpoint' THEN ISNULL(@URLAPIEndpoint,Value)
						WHEN Property = 'URLLink' THEN ISNULL(@URLLink,Value)
						WHEN Property = 'Used for Beneficiary' THEN ISNULL(@UsedforBeneficiary,Value)
						WHEN Property = 'Uses HTTPS' THEN ISNULL(@UsesHTTPS,Value)

						ELSE Value
				END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.Object_ID = @URL_ObjectId
		  and t_object.Stereotype = 'System URL';

		PRINT 'After Property updates'

		UPDATE		SparxDB.dbo.t_objectproperties SET Value = NULL
		  FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			    ON	t_object.Object_ID = t_objectproperties.Object_ID
		WHERE		t_object.Object_ID = @URL_ObjectId
		  and t_object.Stereotype = 'System URL'
		  and Value = '';

		PRINT 'End of Update URL';
/**		Select		t_object.*
		FROM		SparxDB.dbo.t_object 
		WHERE		t_object.ea_guid = @GUID
		and			t_object.Stereotype = 'System URL'
		;**/

		Select		t_object.* 
					,t_objectproperties.* 
		  FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			    ON	t_object.Object_ID = t_objectproperties.Object_ID
		WHERE		t_object.ea_guid = @GUID;

	END TRY


	BEGIN CATCH
	
		PRINT 'Inside Update URL Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);

	END CATCH

END;













