








/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemOrgRole_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/03/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemOrgRole attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemOrgRole_Reln] 
			 	@objectId, 
				@roleTypeId, 
				@orgId




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemOrgRole_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemOrgRole_Reln] 
--Add the parameters for the stored procedure here
	@objectId nvarchar(255) = '', 
	@roleTypeId nvarchar(255) = '', 
	@orgId nvarchar(255) = '',
	@roleTypeName nvarchar(255) = ''


AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@Org_ObjectId nvarchar(255) = '',
	@RoleType_ObjectId nvarchar(255) = '';


Select 'Inside Insert SystemOrgRole';
Select 'SystemId = '+@objectId;
Select 'Role Type = '+@roleTypeName;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';


IF len(@objectId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--BEGIN TRAN

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @objectId;
		Select @Org_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @orgId;
		Select @RoleType_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @roleTypeId;

		If @roleTypeName = 'Business Owner'
		BEGIN
Select 'Inside Business Owner processing'
			Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
			Values (@objectId+' : '+@orgId, 'Association', 'Owner','Source -> Destination', @Org_ObjectId, @System_ObjectId , CONCAT('{',NEWID(),'}'))
Select 'Connector created'
			Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where [Name] = @objectId+' : '+@orgId 
				and Stereotype = 'Owner';
Select 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

			INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
			VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Owner;FQName=CMS EA::Owner;@ENDSTEREO;', @New_ConnectorGUID, '<none>');

		END

		If @roleTypeName = 'System Maintainer'
		BEGIN
			Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
			Values (@objectId+' : '+@orgId, 'Association', 'Maintain','Source -> Destination', @Org_ObjectId, @System_ObjectId , CONCAT('{',NEWID(),'}'))

			Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where [Name] = @objectId+' : '+@orgId
				and Stereotype = 'Maintain';

			INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
			VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Maintain;FQName=CMS EA::Maintain;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
		END


		---insert the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Status', '', '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Impact', '', '', CONCAT('{',NEWID(),'}'))

		-- Check the inserts

		Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records

		--ROLLBACK;

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;














