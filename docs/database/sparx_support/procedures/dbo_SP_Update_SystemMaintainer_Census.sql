



/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemMaintainer_Census
 *
 * AUTHOR: Sita Paturi
 * DATE:   06/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update System attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemMaintainer_Census] 
			 @frontendAccessType,@netAccessibility,@ipEnabledAssetCount,@ip6EnabledAssetPercent,@ip6TransitionPlan,@systemCustomization
			,@majorRefreshDate,@nextMajorRefreshDate,@hasMetadataGlossary,@storeInCentralDataCatalog,@edlPlan,@agileUsed,@deploymentFrequency
			,@plansToRetireReplace,@quarterToRetireReplace,@yearToRetireReplace,@identityManagementSolution,@identityManagementSolutionOther
			,@businessArtifactsOnDemand,@businessArtifactsLocation,@testPlanOnDemand,@testPlanLocation,@systemRequirementsOnDemand,@systemRequirementsLocation
			,@testScriptsOnDemand,@testScriptsLocation,@systemDesignOnDemand,@systemDesignLocation,@testReportsOnDemand,@testReportsLocation
			,@sourceCodeOnDemand,@sourceCodeLoction,@omDocumentationOnDemand,@omDocumentationLocation
			,@isRecordManagementScheduleApproved
			,@authoritativeDataSource,@systemDataLocation,@systemDataDocationNotes,@adHocAgileDeplolymentFreq,@locallyStoredUserInfo,@mfaMethod,@mfaOther
			,@networkTrafficEncryptionKeyMgmt,@dataAtRestTrafficEncryptionKeyMgmt,@noPersistentRecords
			,@recordsUnderLegalHold,@legalHoldCaseName,@noMajorRefresh,@noPlannedMajorRefresh
			,@recordsManagementBucket,@devWorkDescription,@recordsMgmtTypeIdentification,@SystemGUID

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemMaintainer_Census
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 07/17/2023	Sita Paturi					Moved "System Data Location", "MFA Method"
 *												, "Records Management Format, "Records Management Metadata"
 *											to Notes field from Value field in t_ObjectProperties table
 *
 * 07/28/2023	Sita Paturi					Moved "Identity Management Solution" to Notes field from Value field in t_ObjectProperties table
 * 08/31/2023	Sita Paturi	CEDARTEST-1120 	Moved "System Data Authoritative Source" to Notes field from Value field in t_ObjectProperties table
 * 08/31/2023	Aditya Sharma				Set all | delimeted Notes parameters to nvarchar(Max)
 * 02/05/2024	Sita Paturi					Moved "Identity Management Solution Other" to Notes from Value field in t_ObjectProperties table
 * 07/18/2024	Aditya Sharma				FY25 Changes for field removal/addition
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE .[dbo].[SP_Update_SystemMaintainer_Census] 
--Add the parameters for the stored procedure here
	@frontendAccessType nvarchar(255) = NULL,
	@netAccessibility nvarchar(255) = NULL,
	@ipEnabledAssetCount nvarchar(255) = NULL,
	@ip6EnabledAssetPercent nvarchar(255) = NULL,
	@ip6TransitionPlan nvarchar(255) = NULL,
	--@hardCodedIpAddress nvarchar(255) = NULL,
	@systemCustomization nvarchar(255) = NULL,
	@majorRefreshDate nvarchar(255) = NULL,
	@nextMajorRefreshDate nvarchar(255) = NULL,
	@hasMetadataGlossary nvarchar(255) = NULL,
	@storeInCentralDataCatalog nvarchar(255) = NULL,
	@edlPlan nvarchar(255) = NULL,
	@agileUsed nvarchar(255) = NULL,
	@deploymentFrequency nvarchar(255) = NULL,
	@plansToRetireReplace nvarchar(255) = NULL,
	@quarterToRetireReplace nvarchar(255) = NULL,
	@yearToRetireReplace nvarchar(255) = NULL,
	@identityManagementSolution nvarchar(Max) = NULL,
	@identityManagementSolutionOther nvarchar(Max) = NULL,
	@businessArtifactsOnDemand nvarchar(255) = NULL,
	@businessArtifactsLocation nvarchar(255) = NULL,
	@testPlanOnDemand nvarchar(255) = NULL,
	@testPlanLocation nvarchar(255) = NULL,
	@systemRequirementsOnDemand nvarchar(255) = NULL,
	@systemRequirementsLocation nvarchar(255) = NULL,
	@testScriptsOnDemand nvarchar(255) = NULL,
	@testScriptsLocation nvarchar(255) = NULL,
	@systemDesignOnDemand nvarchar(255) = NULL,
	@systemDesignLocation nvarchar(255) = NULL,
	@testReportsOnDemand nvarchar(255) = NULL,
	@testReportsLocation nvarchar(255) = NULL,
	@sourceCodeOnDemand nvarchar(255) = NULL,
	@sourceCodeLoction nvarchar(255) = NULL,
	@omDocumentationOnDemand nvarchar(255) = NULL,
	@omDocumentationLocation nvarchar(255) = NULL,
	@isRecordManagementScheduleApproved nvarchar(255) = NULL,
	--@recordManagementMetadata nvarchar(Max) = NULL, --pipe delimited values
	--@recordManagementFormat nvarchar(Max) = NULL, --pipe delimited values
	--@recordManagementFormatOther nvarchar(255) = NULL,	
	@authoritativeDataSource nvarchar(Max) = NULL,
	@systemDataLocation nvarchar(Max) = NULL,
	@systemDataDocationNotes nvarchar(max) = NULL,
	@adHocAgileDeplolymentFreq nvarchar(255) = NULL,
	@locallyStoredUserInfo nvarchar(255) = NULL,
	@mfaMethod nvarchar(Max) = NULL,
	@mfaOther nvarchar(255) = NULL,
	@networkTrafficEncryptionKeyMgmt nvarchar(255) = NULL,
	@dataAtRestTrafficEncryptionKeyMgmt nvarchar(255) = NULL,
	@noPersistentRecords nvarchar(255) = NULL,
	--@canDisposeRecordsData nvarchar(255) = NULL,
	--@recordsMgmtDisposalLocation nvarchar(255) = NULL,
	--@recordsMgmtDisposalPlan nvarchar(255) = NULL,
	@recordsUnderLegalHold nvarchar(255) = NULL,
	@legalHoldCaseName nvarchar(255) = NULL,
	@noMajorRefresh nvarchar(255) = NULL,
	@noPlannedMajorRefresh	nvarchar(255) = NULL,
	@recordsManagementBucket nvarchar(max) = NULL, 
	@devWorkDescription nvarchar(max) = NULL,
/***FY25 new fields ***/
	@recordsMgmtTypeIdentification nvarchar(255) = NULL,
/*****************************/
	@SystemGUID nvarchar(60) = ''

AS

PRINT 'Inside Update System Maintainer';
DECLARE @RC INT = 0;

BEGIN
	BEGIN TRY

		IF len(ISNULL(@SystemGUID,'')) = 0 
			THROW 50001,'Input GUID is empty',1;


		IF len(@SystemGUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;


PRINT 'Before Update';
PRINT 'GUID = '+@SystemGUID;

				
				UPDATE SparxDB.dbo.t_objectproperties SET Value = 
				CASE	
						WHEN Property = 'Front End Access Type' THEN @frontendAccessType
						WHEN Property = 'CMS System Access' THEN @netAccessibility
						WHEN Property = 'IP Enabled Asset Count' THEN @ipEnabledAssetCount
						WHEN Property = 'Percent IPV6' THEN @ip6EnabledAssetPercent
						WHEN Property = 'Long Term IPV6 Plan' THEN @ip6TransitionPlan
--						WHEN Property = 'Hard Coded IP Address' THEN @hardCodedIpAddress
						WHEN Property = 'System Customization' THEN @systemCustomization
						WHEN Property = 'Last Major Tech Refresh Date' THEN CONVERT(VARCHAR(10),@majorRefreshDate, 101)
						WHEN Property = 'Next Major Tech Refresh Date' THEN CONVERT(VARCHAR(10),@nextMajorRefreshDate,101)
						WHEN Property = 'Metadata Glossary' THEN @hasMetadataGlossary
						WHEN Property = 'Centralized Data Catalog' THEN @storeInCentralDataCatalog
						WHEN Property = 'EDL Plan' THEN @edlPlan ----not in sample json
						WHEN Property = 'Agile Methodology Use' THEN @agileUsed
						WHEN Property = 'Deployment Frequency' THEN @deploymentFrequency
						WHEN Property = 'Retire or Replace' THEN @plansToRetireReplace
						WHEN Property = 'Planned Retirement Quarter' THEN @quarterToRetireReplace
						WHEN Property = 'Retire or Replace Date' THEN @yearToRetireReplace
						WHEN Property = 'Business Artifacts on Demand' THEN @businessArtifactsOnDemand
						WHEN Property = 'Business Artifact Location' THEN @businessArtifactsLocation
						WHEN Property = 'Requirements on Demand' THEN @systemRequirementsOnDemand
						WHEN Property = 'Requirements Location' THEN @systemRequirementsLocation
						WHEN Property = 'Design on Demand' THEN @systemDesignOnDemand
						WHEN Property = 'System Design Location' THEN @systemDesignLocation
						WHEN Property = 'Source Code on Demand' THEN @sourceCodeOnDemand
						WHEN Property = 'Souce Code Location' THEN @sourceCodeLoction
						WHEN Property = 'Test Plan on Demand' THEN @testPlanOnDemand
						WHEN Property = 'Test Plan Location' THEN @testPlanLocation
						WHEN Property = 'Test Script on Demand' THEN @testScriptsOnDemand
						WHEN Property = 'Test Script Location' THEN @testScriptsLocation
						WHEN Property = 'Test Reports on Demand' THEN @testReportsOnDemand
						WHEN Property = 'Test Report Location' THEN @testReportsLocation
						WHEN Property = 'Ops and Maintenance Plans on Demand' THEN @omDocumentationOnDemand
						WHEN Property = 'Ops and Maintenance Plan Location' THEN @omDocumentationLocation
						WHEN Property = 'Records Management Approved Schedule' THEN @isRecordManagementScheduleApproved
						--WHEN Property = 'Records Management Format Other' THEN @recordManagementFormatOther

						----WHEN Property = 'System Data Authoritative Source' THEN @authoritativeDataSource
						----WHEN Property = 'System Data Location' THEN @systemDataLocation
						WHEN Property = 'Deployment AdHoc Frequency' THEN @adHocAgileDeplolymentFreq
						WHEN Property = 'Locally Stored User Info' THEN @locallyStoredUserInfo
						----WHEN Property = 'MFA Method' THEN @mfaMethod
						WHEN Property = 'MFA Other' THEN @mfaOther
						WHEN Property = 'Network Traffic Encryption Management' THEN @networkTrafficEncryptionKeyMgmt
						WHEN Property = 'Data At Rest Encryption Management' THEN @dataAtRestTrafficEncryptionKeyMgmt
						WHEN Property = 'No Persistent Records Flag' THEN @noPersistentRecords
						--WHEN Property = 'Records Management Disposal' THEN @canDisposeRecordsData
						--WHEN Property = 'Records Management Disposal Location' THEN @recordsMgmtDisposalLocation
						--WHEN Property = 'Records Management Disposal Plan' THEN @recordsMgmtDisposalPlan
						WHEN Property = 'Records Under Legal Hold' THEN @recordsUnderLegalHold
						WHEN Property = 'Legal Hold Case Name' THEN @legalHoldCaseName
						WHEN Property = 'No Major Refresh Flag' THEN @noMajorRefresh
						WHEN Property = 'No Planned Major Refresh Flag' THEN @noPlannedMajorRefresh
/*****************************/		
						WHEN Property = 'Records Management Record Type Identification' THEN @recordsMgmtTypeIdentification
/*****************************/		
						ELSE Value
				END
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties 
					ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @SystemGUID
				and t_object.Stereotype = 'System';

				UPDATE SparxDB.dbo.t_ObjectProperties SET Notes = 
								(CASE WHEN t_objectproperties.[Property] ='Development Work Still Underway' THEN @devWorkDescription
									  WHEN t_objectproperties.[Property] ='Identity Management Solution' THEN @identityManagementSolution
									  WHEN Property = 'Identity Management Solution Other' THEN @identityManagementSolutionOther
									  WHEN Property = 'System Data Authoritative Source' THEN @authoritativeDataSource
									  WHEN t_objectproperties.[Property] ='System Data Location Notes' THEN @systemDataDocationNotes
									  WHEN t_objectproperties.[Property] ='System Data Location' THEN @systemDataLocation
									  WHEN t_objectproperties.[Property] ='MFA Method' THEN @mfaMethod
									  --WHEN t_objectproperties.[Property] ='Records Management Metadata' THEN @recordManagementMetadata
									  --WHEN t_objectproperties.[Property] ='Records Management Format' THEN @recordManagementFormat
									  END)
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @SystemGUID
				and t_object.Stereotype = 'System'
				--and t_objectproperties.[Property] in ('Development Work Still Underway','Identity Management Solution','Identity Management Solution Other'
				--										,'System Data Location Notes', 'System Data Authoritative Source'
				--										,'System Data Location','MFA Method','Records Management Metadata','Records Management Format');
				and t_objectproperties.[Property] in ('Development Work Still Underway','Identity Management Solution','Identity Management Solution Other'
														,'System Data Location Notes', 'System Data Authoritative Source'
														,'System Data Location','MFA Method');

PRINT 'Delete existing System->Record Management Bucket relationships';

				EXECUTE @RC = [dbo].[SP_Delete_SystemRecMgmtBucket_Reln_Census] 

								@SystemGUID;
PRINT 'Create System->Record Management Bucket relationships from input @recordsManagementBucket string values';

Declare @recordMgmtBucketFormatted nvarchar(max);

Select @recordMgmtBucketFormatted = REPLACE(REPLACE(@recordsManagementBucket, CHAR(10), ''),CHAR(13), '')
				
/*****/				IF len(@recordMgmtBucketFormatted) > 0
						DECLARE @RecordMgmtBucketGUID nvarchar(100) = NULL,
								@RecordMgmtBucketItem nvarchar(100) = NULL;

							DECLARE db_add_cursor CURSOR FOR
								Select * from  string_split(@recordMgmtBucketFormatted, '|');
							OPEN db_add_cursor;
								FETCH NEXT FROM db_add_cursor INTO @RecordMgmtBucketItem;
		PRINT '1st Fetch';
								WHILE @@FETCH_STATUS = 0
									BEGIN
									Select @RecordMgmtBucketGUID = ea_guid from SparxDb.dbo.t_Object where Name = @RecordMgmtBucketItem
									EXECUTE @RC = [dbo].[SP_Insert_SystemRecMgmtBucket_Reln_Census] 
														@SystemGUID,
														@RecordMgmtBucketGUID;
									-- Fetch next record
									FETCH NEXT FROM db_add_cursor INTO @RecordMgmtBucketItem;
		PRINT 'Next Fetch';
								END
							-- Close the cursor
							CLOSE db_add_cursor;
							-- Deallocate the cursor
							DEALLOCATE db_add_cursor; 
/*****				END		*/		

PRINT 'After Main Update';

				UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties 
					ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @SystemGUID
				and t_object.Stereotype = 'System'
				and Value = '';

PRINT 'After NULL Update';

		END;

	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Maintainer Error';
		-- Log the error
		--EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;







