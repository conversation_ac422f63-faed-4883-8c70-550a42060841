










/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemDataExchange_json
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/28/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemDataExchange attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemDataExchange_json] 
 			 @jsonInput,
			 @jsonOutput 
 *
 *
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemDataExchange_json
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 12/06/2023	Aditya Sharma				Add Exchange Name to the Update
 * 08/13/2024	Aditya Sharma				Modify Fields for FY25 Census
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SystemDataExchange_json] 
--Add the parameters for the stored procedure here
	@jsonInput nvarchar(max),
	@jsonOutput nvarchar(max) OUT 

AS

DECLARE 

	@DataExchangeFormat nvarchar(255) = NULL, 
	@DataExchangeFormatOther nvarchar(255) = NULL, 
	@DataSharedviaAPI nvarchar(255) = NULL, 
	@ExchangeContainsPHI nvarchar(255) = NULL, 
	@ExchangeContainsPII nvarchar(255) = NULL, 
	@ExchangeFrequency nvarchar(max) = NULL, 
	@ExchangeIncludesBankingData nvarchar(255) = NULL, 
	@ExchangeincludesBenefitiaryAddressData nvarchar(255) = NULL, 
	@ExchangeSupportsMailingtoBenefitiaries nvarchar(255) = NULL, 
	@ExchangeBenefitiaryAddressPurpose nvarchar(max) = NULL, 
	@Exchange_ID nvarchar(255) = NULL, 
	@IEAgreement nvarchar(255) = NULL, 
	@NumberofRecordsExchanged nvarchar(255) = NULL, 
	@RelationshipImpact nvarchar(255) = NULL, 
	@RelationshipStatus nvarchar(255) = NULL, 
	@Exchange nvarchar(255) = NULL, 
	@ExchangeDescription nvarchar(max) = NULL, 
	@ExchangeHealthDisparityData nvarchar(255) = NULL, 
	@ExchangeEndDate nvarchar(255) = NULL, 
	@Sender nvarchar(255) = NULL, 
	@ExchangeTechName nvarchar(255) = NULL, 
	@ObjectState nvarchar(255) = NULL, 
	@ExchangeStartDate nvarchar(255) = NULL, 
	@Receiver nvarchar(255) = NULL, 
	@ExchangeDataArea nvarchar(255) = NULL, 
	@ExchangeApiOwner nvarchar(255) = NULL, 
	@ExchangeBeneficiaryMailing nvarchar(255) = NULL, 
	@ExchangeRetireDate nvarchar(255) = NULL, 

	@ExchangeConnectionAuthenticated nvarchar(255) = NULL, 
	@ExchangeContainsCUI nvarchar(255) = NULL, 
	--@ExchangeCUIDescription nvarchar(max) = NULL, 
	@ExchangeNetworkProtocol nvarchar(max) = NULL, 
	@ExchangeNetworkProtocolOther nvarchar(255) = NULL, 
	@ExchangeCUIType nvarchar(max) = NULL, 

	@RowNum INT = 0,
	@RC INT = 0,
	@NumRec INT = 0,
	@GUID nvarchar(60)

PRINT 'Inside Update SystemDataExchange json processing';

DECLARE @New_ObjectID INT = 0;

IF len(@jsonInput) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

-- Do the System DataExchange addition 

	
		DECLARE db_add_cursor CURSOR FOR
		SELECT t.*, s.ToRef as DRM
		FROM  
			OPENJSON (@jsonInput,'$.Objects')  
		WITH (   
						Id	int,  
						exchange_guid nvarchar(60) '$.RefStr',
						cms_exchange nvarchar(255) '$.Values.cms_exchange',
						--CMS_REST_UPDATED_USER	nvarchar(255) '$.Values.CMS_REST_UPDATED_USER',  
						--cms_adress_data_edits	nvarchar(255) '$.Values.cms_adress_data_edits',
						cms_api_owner nvarchar(255) '$.Values.cms_api_owner',
						cms_beneficiary_address_pur nvarchar(max) '$.Values.cms_beneficiary_address_pur',
						cms_data_exch_api_data_share nvarchar(255) '$.Values.cms_data_exch_api_data_share',
						cms_data_exch_benef_mailing nvarchar(255) '$.Values.cms_data_exch_benef_mailing',
						cms_data_exch_beneficiary_add nvarchar(255) '$.Values.cms_data_exch_beneficiary_add',
						cms_data_exch_format nvarchar(255) '$.Values.cms_data_exch_format',
						cms_data_exch_format_other nvarchar(255) '$.Values.cms_data_exch_format_other',
						cms_data_exch_num_recs nvarchar(255) '$.Values.cms_data_exch_num_recs',
						cms_data_exchange_phi nvarchar(255) '$.Values.cms_data_exchange_phi',
						cms_data_exchange_pii nvarchar(255) '$.Values.cms_data_exchange_pii',
						cms_exchange nvarchar(255) '$.Values.cms_exchange',
						cms_exchange_frequency nvarchar(max) '$.Values.cms_exchange_frequency',
						--cms_exchange_retire_date nvarchar(255) '$.Values.cms_exchange_retire_date',
						cms_health_disparity_data nvarchar(255) '$.Values.cms_health_disparity_data',
						cms_ie_agreement nvarchar(255) '$.Values.cms_ie_agreement',
						description nvarchar(max) '$.Values.description',
						enddate nvarchar(255) '$.Values.cms_exchange_retire_date',
						objectstate nvarchar(255) '$.Values.objectstate',
						startdate nvarchar(255) '$.Values.startdate',
						cms_exchange_contains_cui nvarchar(255) '$.Values.cms_exchange_contains_cui',
						--cms_exchange_cui_description nvarchar(max) '$.Values.cms_exchange_cui_description',
						cms_exchange_connection_authenticated nvarchar(255) '$.Values.cms_exchange_connection_authenticated',
						cms_exchange_network_protocol nvarchar(max) '$.Values.cms_exchange_network_protocol',
						cms_exchange_network_protocol_other nvarchar(255) '$.Values.cms_exchange_network_protocol_other',
						cms_exchange_cui_type nvarchar(max) '$.Values.cms_exchange_cui_type'

			) t
		LEFT JOIN 
			OPENJSON (@jsonInput,'$.Relations')  
		WITH (   
						FromRef		nvarchar(255),  
						Property	nvarchar(255),
						ToRef		nvarchar(255)
			) s on t.exchange_guid = s.FromRef and Property = 'cms_data_exch_type_of_data'; 


PRINT 'Before Open';

		OPEN db_add_cursor;

PRINT 'After Open';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_cursor INTO
			 @RowNum
			,@GUID
			,@Exchange
			,@ExchangeApiOwner
			,@ExchangeBenefitiaryAddressPurpose
			,@DataSharedviaAPI
			,@ExchangeBeneficiaryMailing
			,@ExchangeincludesBenefitiaryAddressData
			,@DataExchangeFormat
			,@DataExchangeFormatOther
			,@NumberofRecordsExchanged
			,@ExchangeContainsPHI
			,@ExchangeContainsPII
			,@Exchange
			,@ExchangeFrequency
			--,@ExchangeRetireDate
			,@ExchangeHealthDisparityData
			,@IEAgreement
			,@ExchangeDescription
			,@ExchangeEndDate
			,@ObjectState
			,@ExchangeStartDate
			,@ExchangeContainsCUI
			--,@ExchangeCUIDescription
			,@ExchangeConnectionAuthenticated
			,@ExchangeNetworkProtocol
			,@ExchangeNetworkProtocolOther
			,@ExchangeCUIType
			,@ExchangeDataArea
			--,@ExchangeIncludesBankingData
			--,@ExchangeSupportsMailingtoBenefitiaries
			--,@Exchange_ID
			--,@RelationshipImpact
			--,@RelationshipStatus

		SET @jsonOutput = '{"NewObjects": {';
 
PRINT '1st Fetch';
PRINT @GUID
PRINT @ObjectState
PRINT @ExchangeEndDate

		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			
			-- Call Update SP
			EXECUTE @RC = [dbo].[SP_Update_SystemExchange] 
				 @DataExchangeFormat
				,@DataExchangeFormatOther
				,@DataSharedviaAPI
				,@ExchangeContainsPHI
				,@ExchangeContainsPII
				,@ExchangeFrequency
				,@ExchangeIncludesBankingData
				,@ExchangeincludesBenefitiaryAddressData
				,@ExchangeBeneficiaryMailing
				,@Exchange_ID
				,@IEAgreement
				,@NumberofRecordsExchanged
				,@ExchangeDescription
				,@ExchangeDataArea
				,@ExchangeApiOwner
				,@ExchangeBenefitiaryAddressPurpose
				--,@ExchangeRetireDate
				,@ExchangeHealthDisparityData
				,@ExchangeEndDate
				,@ExchangeStartDate
				,@ExchangeConnectionAuthenticated
				,@ExchangeContainsCUI
				--,@ExchangeCUIDescription
				,@ExchangeNetworkProtocol
				,@ExchangeNetworkProtocolOther
				,@ExchangeCUIType
				,@ObjectState
				,@Exchange

				,@GUID


				SET @NumRec = @NumRec+1;
				SET @jsonOutput = @jsonOutput +'"GUID": "'+ @GUID+'",' 

			-- Fetch next record
			FETCH NEXT FROM db_add_cursor INTO
				 @RowNum
				,@GUID
				,@Exchange
				,@ExchangeApiOwner
				,@ExchangeBenefitiaryAddressPurpose
				,@DataSharedviaAPI
				,@ExchangeBeneficiaryMailing
				,@ExchangeincludesBenefitiaryAddressData
				,@DataExchangeFormat
				,@DataExchangeFormatOther
				,@NumberofRecordsExchanged
				,@ExchangeContainsPHI
				,@ExchangeContainsPII
				,@Exchange
				,@ExchangeFrequency
				--,@ExchangeRetireDate
				,@ExchangeHealthDisparityData
				,@IEAgreement
				,@ExchangeDescription
				,@ExchangeEndDate
				,@ObjectState
				,@ExchangeStartDate
				,@ExchangeContainsCUI
				--,@ExchangeCUIDescription
				,@ExchangeConnectionAuthenticated
				,@ExchangeNetworkProtocol
				,@ExchangeNetworkProtocolOther
				,@ExchangeCUIType
				,@ExchangeDataArea
				--,@ExchangeIncludesBankingData
				--,@ExchangeSupportsMailingtoBenefitiaries
				--,@Exchange_ID
				--,@RelationshipImpact
				--,@RelationshipStatus

PRINT 'Next Fetch';

		END 

		-- 6 - Close the cursor
		CLOSE db_add_cursor;  

		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_cursor; 
PRINT 'Close and exit System Data Center json processing'

PRINT 'Before final output creation';

Select @jsonOutput

		SET @jsonoutput = LEFT(@jsonoutput, LEN(@jsonoutput)-1)+'},"Count":'+CAST(@NumRec AS nvarchar(3))+'}';

PRINT @jsonOutput;

	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Exchange json exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;
















