





/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_Person
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/10/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the Person attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_Person] 
			 @Email
			,@ExternalSource
			,@FirstName
			,@LastName
			,@Phone
			,@TechName
			,@UserName
			,@GUID


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_Person
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_Person] 
--Add the parameters for the stored procedure here
	@Email nvarchar(255) = '', 
	--@ExternalID nvarchar(255) = '', 
	@ExternalSource nvarchar(255) = '', 
	@FirstName nvarchar(255) = '', 
	@LastName nvarchar(255) = '', 
	--@OrgUnitName nvarchar(255) = '', 
	--@PersonID nvarchar(255) = '', 
	@Phone nvarchar(255) = '', 
	@TechName nvarchar(255) = '', 
	@UserName nvarchar(255) = '', 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Insert Person';
PRINT 'GUID = '+@GUID;

DECLARE @New_ObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		--BEGIN TRAN
		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=Person;FQName=CMS EA::Person;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'Person'
		WHERE t_object.ea_guid = @GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
PRINT @New_ObjectID;
		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'Email' as Property, @Email as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			--Select @New_ObjectID as ObjectID, 'External ID' as Property, @ExternalID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'External Source' as Property, @ExternalSource as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'First Name' as Property, @FirstName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Last Name' as Property, @LastName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			--Select @New_ObjectID as ObjectID, 'Org Unit Name' as Property, @OrgUnitName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			--Select @New_ObjectID as ObjectID, 'Person ID' as Property, @PersonID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Phone' as Property, @Phone as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Tech Name' as Property, @TechName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'User Name' as Property, @UserName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
		) t;

		Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

PRINT 'At End of Insert Person';

	END TRY
	BEGIN CATCH

PRINT 'In Insert Person Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;











