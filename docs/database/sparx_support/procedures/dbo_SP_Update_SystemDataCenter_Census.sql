

/*********************************************************************************************
* PROCEDURE_NAME: SP_Update_SystemDataCenter_Census
*
* AUTHOR: <PERSON>
* DATE:   07/25/2023
*
* DESCRIPTION/PURPOSE:
*             Update System attributes in SparxDB 
 *
* DEPENDENCY MATRIX:
* ------------------------------------------------------------------------------------------
* Item                                                                  Location                                                                                                                               Purpose
* 
 * ------------------------------------------------------------------------------------------
*
* EXAMPLE EXECUTION:
*             EXECUTE   @RC = [dbo].[SP_Update_SystemDataCenter_Census] 
						@Environment ,
						@ContractorName ,
						@ProductionDataUseFlag ,
						@DataReplicated ,
						@WANType ,
						@CloudMigrationPlan,
						@CloudMigratedDate,
						WANTypeOther,
						@SparxSystemGUID
*
* REMOVE PROC:
*             DROP PROCEDURE dbo.SP_Update_SystemDataCenter_Census
*
* UPDATE HISTORY:
* ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 08/12/2024	Aditya Sharma				Add FY25 new field: WAN Type - Other                                                                  
*                                                                                                                                                                              
*
* ------------------------------------------------------------------------------------------

[Environment] nvarchar(255)
[Contractor Name] nvarchar(255)
[Production Data Use Flag] nvarchar(255)
[Data Replicated] nvarchar(255)
--System Elements Replicated
[WAN Type] nvarchar(255)
********************************************************************************************/
CREATE       PROCEDURE .[dbo].[SP_Update_SystemDataCenter_Census] 
--Add the parameters for the stored procedure here
    @Environment nvarchar(255) = NULL, 
	@ContractorName nvarchar(255) = NULL, 
	@ProductionDataUseFlag nvarchar(255) = NULL, 
	@DataReplicated nvarchar(255) = NULL, 
	@WANType nvarchar(max) = NULL, 
	@CloudMigrationPlan nvarchar(255) = NULL,  
    @CloudMigratedDate nvarchar(255) = NULL, 
	@WANTypeOther nvarchar(max) = NULL, 
	@SparxSystemGUID nvarchar(40) = NULL
                     
AS

PRINT 'Inside SP_Update_SystemDataCenter_Census';
DECLARE @RC INT = 0;

BEGIN

	BEGIN TRY

		IF len(ISNULL(@SparxSystemGUID,'')) = 0 
                                THROW 50001,'Input @SparxSystemGUID is empty',1;

		IF len(@SparxSystemGUID) > 0 
			BEGIN
				-- SET NOCOUNT ON added to prevent extra result sets from
				-- interfering with SELECT statements.
                SET NOCOUNT ON;

				PRINT 'Begin Update';
	
				PRINT '@SparxSystemGUID = '+@SparxSystemGUID;

                                                                
                UPDATE SparxDB.dbo.t_objectproperties 
				SET Value = 
								(CASE      
									WHEN Property = 'Environment' THEN  @Environment
									WHEN Property = 'Contractor Name' THEN  @CloudMigrationPlan
									WHEN Property = 'Production Data Use Flag' THEN  @ProductionDataUseFlag
									WHEN Property = 'Data Replicated' THEN  @DataReplicated
									WHEN Property = 'Cloud Migration Plan' THEN  @CloudMigrationPlan
									WHEN Property = 'Cloud Migrated Date' THEN @CloudMigratedDate
									ELSE Value
								END)
                FROM            SparxDB.dbo.t_object 
                INNER JOIN      SparxDB.dbo.t_objectproperties 
                                ON t_object.Object_ID = t_objectproperties.Object_ID
                WHERE           t_object.ea_guid = @SparxSystemGUID
                 and            t_object.Stereotype = 'System' ;

				UPDATE ct 
				SET NOTES = ISNULL(@WANType,ct.Notes)
				FROM		SparxDB.dbo.t_connector c
				INNER JOIN	SparxDB.dbo.t_connectortag ct
							ON c.Connector_ID = ct.ElementID
				WHERE c.ea_guid = @SparxSystemGUID 
				  and c.Stereotype = 'System'
				  and ct.Property = 'WAN Type';

				UPDATE ct 
				SET NOTES = ISNULL(@WANTypeOther,ct.Notes)
				FROM		SparxDB.dbo.t_connector c
				INNER JOIN	SparxDB.dbo.t_connectortag ct
							ON c.Connector_ID = ct.ElementID
				WHERE c.ea_guid = @SparxSystemGUID 
				  and c.Stereotype = 'System'
				  and ct.Property = 'WAN Type - Other';


				PRINT 'End of update';

			END;

	END  TRY


	BEGIN CATCH

		PRINT 'Inside Update System Data Center Error';
		
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
			BEGIN	
				PRINT ' XACT_STATE() = -1'
				ROLLBACK TRANSACTION;
			END
                                
		--Report stored procedure failed
		RETURN (1); 
	END CATCH

END;




