

/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemExchange
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/23/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemSupportContact attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemExchange] 
			 	 @senderGUID
				,@receiverGUID
				,@DataExchangeFormat
				,@DataExchangeFormatOther
				,@DataSharedviaAPI
				,@ExchangeContainsPHI
				,@ExchangeContainsPII
				,@ExchangeFrequency
				,@ExchangeIncludesBankingData
				,@ExchangeincludesBenefitiaryAddressData
				,@ExchangeSupportsMailingtoBenefitiaries
				,@Exchange_ID
				,@IEAgreement
				,@NumberofRecordsExchanged
				,@Name
				,@Description
				,@DataArea

				,@ExchangeApiOwner
				,@ExchangeBenefitiaryAddressPurpose
				,@ExchangeHealthDisparityData
				,@ExchangeEndDate
				,@ExchangeStartDate
				,@ExchangeConnectionAuthenticated
				,@ExchangeContainsCUI
				,@ExchangeNetworkProtocol
				,@ExchangeNetworkProtocolOther
				,@ExchangeCUIType

				,@GUID OUT


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemExchange
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 10/13/2023	Aditya Sharma				Fixed column names to match repository config setup for 3 fields
 * 08/13/2024	Aditya Sharma				Modify Fields for FY25 Census
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemExchange] 
--Add the parameters for the stored procedure here
	--@exchangeDirection nvarchar(255) = NULL, 
	@senderGUID nvarchar(255) = NULL, 
	@receiverGUID nvarchar(255) = NULL, 
	@DataExchangeFormat nvarchar(255) = NULL, 
	@DataExchangeFormatOther nvarchar(255) = NULL, 
	@DataSharedviaAPI nvarchar(255) = NULL, 
	@ExchangeContainsPHI nvarchar(255) = NULL, 
	@ExchangeContainsPII nvarchar(255) = NULL, 
	@ExchangeFrequency nvarchar(255) = NULL, 
	@ExchangeIncludesBankingData nvarchar(255) = NULL, 
	@ExchangeincludesBenefitiaryAddressData nvarchar(255) = NULL, 
	@ExchangeSupportsMailingtoBenefitiaries nvarchar(255) = NULL, 
	@Exchange_ID nvarchar(255) = NULL, 
	@IEAgreement nvarchar(255) = NULL, 
	@NumberofRecordsExchanged nvarchar(255) = NULL,
	@Name nvarchar(255) = NULL,
	@Description nvarchar(max) = NULL,
	@DataArea nvarchar(255) = NULL,

	@ExchangeApiOwner nvarchar(255) = NULL, 
	@ExchangeBenefitiaryAddressPurpose nvarchar(max) = NULL, 
	@ExchangeHealthDisparityData nvarchar(255) = NULL, 
	@ExchangeEndDate nvarchar(255) = NULL, 
	@ExchangeStartDate nvarchar(255) = NULL, 
	@ExchangeConnectionAuthenticated nvarchar(255) = NULL, 
	@ExchangeContainsCUI nvarchar(255) = NULL, 
	--@ExchangeCUIDescription nvarchar(max) = NULL, 
	@ExchangeNetworkProtocol nvarchar(max) = NULL, 
	@ExchangeNetworkProtocolOther nvarchar(255) = NULL, 
	@ExchangeCUIType nvarchar(max) = NULL,

	@GUID nvarchar(60) OUT


AS

DECLARE
	@Sender_ObjectId nvarchar(255) = NULL, 
	@Receiver_ObjectId nvarchar(255) = NULL;


PRINT 'Inside Insert SystemExchange';

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @RelStatusNotes nvarchar(max) = 'Values: Future,Active,Retired,Deleted,Never Implemented  Default: Active  ';
DECLARE @RelStatusDefault nvarchar(255);
DECLARE @RelImpactNotes nvarchar(max) = 'Values: Critical,Major,Moderate,Minor,None,Unknown  Default: Moderate  ';
DECLARE @RelImpactDefault nvarchar(255);

IF len(@senderGUID) > 0 AND len(@receiverGUID) > 0
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY
		Select @Sender_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @senderGUID;
		Select @Receiver_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @receiverGUID;

		Select @RelStatusDefault = LTRIM(RTRIM(SUBSTRING(@RelStatusNotes,CHARINDEX('Default:',@RelStatusNotes,1)+8,LEN(@RelStatusNotes)-CHARINDEX('Default:',@RelStatusNotes,1) -7)));
		Select @RelImpactDefault = LTRIM(RTRIM(SUBSTRING(@RelImpactNotes,CHARINDEX('Default:',@RelImpactNotes,1)+8,LEN(@RelImpactNotes)-CHARINDEX('Default:',@RelImpactNotes,1) -7)));

		Select @New_ConnectorGUID = CONCAT('{',NEWID(),'}');

PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

PRINT 'Start Reln processing'
		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction, Start_Object_ID, End_Object_ID, ea_guid)
		Values (@Sender_ObjectId+' : '+@Receiver_ObjectId+' : '+@Name, 'InformationFlow', 'System Interface','Source -> Destination', @Sender_ObjectId, @Receiver_ObjectId, @New_ConnectorGUID)

PRINT 'Connector created'

		Select @New_ConnectorID = Connector_ID from SparxDB.dbo.t_connector where ea_guid = @New_ConnectorGUID;

		UPDATE SparxDB.dbo.t_connector 
		SET Name = @Name, Notes = @Description
		where ea_guid = @New_ConnectorGUID;

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=System Interface;FQName=CMS EA::System Interface;@ENDSTEREO;', @New_ConnectorGUID, '<none>');

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Behavior, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'MOFProps', 'connector property', 'Public', 'conveyed', @DataArea, @New_ConnectorGUID, '<none>');
PRINT 'XREFs created'

		---insert the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Status', @RelStatusDefault, @RelStatusNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Impact', @RelImpactDefault, @RelImpactNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Data Exchange Format', @DataExchangeFormat, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Data Exchange Format - Other', @DataExchangeFormatOther, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Data Shared via API', @DataSharedviaAPI, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Contains PHI', @ExchangeContainsPHI, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Contains PII', @ExchangeContainsPII, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Frequency', NULL, @ExchangeFrequency, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Includes Banking Data', @ExchangeIncludesBankingData, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange includes Beneficiary Address Data', @ExchangeincludesBenefitiaryAddressData, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Supports Mailing to Beneficiaries', @ExchangeSupportsMailingtoBenefitiaries, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange_ID', @Exchange_ID, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'IE Agreement', @IEAgreement, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Number of Records Exchanged', @NumberofRecordsExchanged, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'API Owner', @ExchangeApiOwner, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Beneficiary Address Purpose', '', @ExchangeBenefitiaryAddressPurpose, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Health Disparity Data', @ExchangeHealthDisparityData, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'End Date', @ExchangeEndDate, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Start Date', ISNULL(@ExchangeStartDate,GETDATE()), '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Connection Authenticated', @ExchangeConnectionAuthenticated, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Contains CUI', @ExchangeContainsCUI, '', CONCAT('{',NEWID(),'}'))

		--Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		--Values (@New_ConnectorID, 'Exchange CUI Description', '', @ExchangeCUIDescription, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Network Protocol', '', @ExchangeNetworkProtocol, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange Network Protocol Other', @ExchangeNetworkProtocolOther, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Exchange CUI Type', '', @ExchangeCUIType, CONCAT('{',NEWID(),'}'))


PRINT 'Properties inserted'

		-- Check the inserts

		Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records

		SET @GUID = @New_ConnectorGUID;

SELECT '@New_ConnectorID set as:'+@New_ConnectorGUID;

	END TRY
	BEGIN CATCH

PRINT 'Inside Add SystemExchange Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;


















