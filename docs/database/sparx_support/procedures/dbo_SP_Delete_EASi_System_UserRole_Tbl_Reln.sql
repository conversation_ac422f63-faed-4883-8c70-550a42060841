


/*********************************************************************************************
 * PROCEDURE_NAME: SP_Delete_EASi_System_UserRole_Tbl_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   08/15/2024
 *
 * DESCRIPTION/PURPOSE:
 *	Update the Sparx_EASi_System_UserRole_Tbl in Sparx_Support DB after an update to underlying data. A Relationship GUID is required as a mandatory parameter to be passed.
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Delete_EASi_System_UserRole_Tbl_Reln] 
			@GUID_List
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Delete_EASi_System_UserRole_Tbl_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 
 * 
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Delete_EASi_System_UserRole_Tbl_Reln] 
--Add the parameters for the stored procedure here
	@GUID_List nvarchar(max) = ''

AS

PRINT 'Inside Update EASi_System_UserRole_Tbl_Reln';

IF len(@GUID_List) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		declare @List table (ID nvarchar(40))
        while charindex(',',@GUID_List) > 0
        begin
            insert into @List (ID) values(left(@GUID_List,charindex(',',@GUID_List)-1))
            set @GUID_List = right(@GUID_List,len(@GUID_List)-charindex(',',@GUID_List));
        end;
        insert into @List (ID) values(@GUID_List);

PRINT 'Before Delete'

		DELETE FROM Sparx_Support.[CEDAR_API].[Sparx_EASi_System_UserRole_Tbl]
		WHERE [RoleAssignmentId] in (select ID from @list)

		
PRINT 'After Row updates'

PRINT 'End of Update EASi_System_UserRole_Tbl_Reln';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update EASi_System_UserRole_Tbl_Reln Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












