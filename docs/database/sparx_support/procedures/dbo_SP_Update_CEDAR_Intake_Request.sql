



/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_CEDAR_Intake_Request
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   05/15/2025
 *
 * DESCRIPTION/PURPOSE:
 *	Update the Intake_Request attributes in CEDAR_Support DB
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_CEDAR_Intake_Request] 
 *	   @Request_CEDAR_Status
 *	  ,@Request_CEDAR_Status_Message
 *	  ,@Intake_Request_ID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_CEDAR_Intake_Request
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_CEDAR_Intake_Request] 
--Add the parameters for the stored procedure here
       @Request_CEDAR_Status nvarchar(255) = '', 
       @Request_CEDAR_Status_Message nvarchar(4000) = '',
       @Intake_Request_ID INT = NULL
AS
IF (@Intake_Request_ID is NOT NULL) 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE CEDAR_Support.CEDAR_API.INTAKE_REQUEST 
		SET  Request_CEDAR_Status = @Request_CEDAR_Status
			,Request_CEDAR_Status_Message = @Request_CEDAR_Status_Message
		WHERE INTAKE_REQUEST_ID = @Intake_Request_ID

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;








