



/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemBudget_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/09/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemBudget attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemBudget_Reln] 
				@funding,
				@GUID




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemBudget_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SystemBudget_Reln] 
--Add the parameters for the stored procedure here
	@funding nvarchar(255) = '',
	@GUID nvarchar(60) = ''

AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@Budget_ObjectId nvarchar(255) = '';


PRINT 'Inside Update SystemBudget';
PRINT 'Funding = '+@funding;

DECLARE @ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
--DECLARE @tagNotes nvarchar(max) = 'Values: Most of this funding is directly and only for this system (over 80%),A large part of this funding is directly and only for this system (between 40%-80%),Only part of this funding is directly for this system (less than 40%)  ';
--DECLARE @RelStatusNotes nvarchar(max) = 'Values: Future,Active,Retired,Deleted,Never Implemented  Default: Active  ';
--DECLARE @RelStatusDefault nvarchar(255);
--DECLARE @RelImpactNotes nvarchar(max) = 'Values: Critical,Major,Moderate,Minor,None,Unknown  Default: Moderate  ';
--DECLARE @RelImpactDefault nvarchar(255);

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		Select @ConnectorID = Connector_ID from SparxDB.dbo.t_connector where ea_guid = @GUID 
			and Stereotype = 'Funds';
PRINT 'Connector = '+CAST(@ConnectorID AS NVARCHAR(10))


		---Update the values into the tagged values spefified in the stereotype using the ID of the connector from above.

		Update SparxDB.dbo.t_connectortag 
		SET Value = @funding
		WHERE Property = 'Funding'
		and ElementID = @ConnectorID;


PRINT 'Properties Updateed'

		-- Check the Updates

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID; -- check connection value records


	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Budget exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;
















