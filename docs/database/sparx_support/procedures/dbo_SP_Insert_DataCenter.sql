



/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_DataCenter
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/09/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the Data Center attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_DataCenter] 
 *	   @AddrL1
 *	  ,@AddrL2
 *	  ,@City
 *	  ,@State
 *	  ,@Zip
 *	  ,@DataCenterID
 *	  ,@DataCenterType
 *	  ,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_DataCenter
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_DataCenter] 
--Add the parameters for the stored procedure here
       @AddrL1 nvarchar(255) = '', 
       @AddrL2 nvarchar(255) = '',
       @City nvarchar(255) = '', 
       @State nvarchar(80) = '', 
       @Zip nvarchar(25) = '', 
       @DataCenterID nvarchar(50) = '', 
       @DataCenterType nvarchar(100) = '', 
       @GUID nvarchar(60) = ''
AS

DECLARE @New_ObjectID INT;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--BEGIN TRAN
		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=Data Center;FQName=CMS EA::Data Center;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'Data Center'
		WHERE t_object.ea_guid = @GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'Address Line 1' as Property, @AddrL1 as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Address Line 2' as Property, @AddrL2 as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'City' as Property, @City as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'State' as Property, @State as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Zip Code' as Property, @Zip as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Data Center ID' as Property, @DataCenterID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Data Center Type' as Property, @DataCenterType as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
		) t;

		Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

		--ROLLBACK;

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;









