






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SubSystem
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   08/07/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theSubSystem attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SubSystem] 
			 @Acronym
			,@RetirementQuarter
			,@RetirementYear
			,@Description
			,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SubSystem
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SubSystem] 
--Add the parameters for the stored procedure here
	@Acronym nvarchar(255) = NULL, 
	@RetirementQuarter nvarchar(255) = NULL, 
	@RetirementYear nvarchar(255) = NULL, 
	@Description nvarchar(max) = NULL, 
	@GUID nvarchar(60) = NULL

AS

PRINT 'Inside Update SubSystem';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_object SET Note = ISNULL(@Description,Note)
		WHERE t_object.ea_guid = @GUID;


		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'Acronym' THEN ISNULL(@Acronym,Value)
				WHEN Property = 'Planned Retirement Quarter' THEN @RetirementQuarter --ISNULL(@RetirementQuarter,Value)
				WHEN Property = 'Retire or Replace Date' THEN @RetirementYear --ISNULL(@RetirementYear,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'System';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'System'
		and Value = '';

PRINT 'End of Update SubSystem';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update SubSystem Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;













