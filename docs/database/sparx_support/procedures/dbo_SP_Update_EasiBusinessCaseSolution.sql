




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_EasiBusinessCaseSolution
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theEasiBusinessCaseSolution attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_EasiBusinessCaseSolution] 
			 @AcquisitionApproach
			,@Cons
			,@CostSavings
			,@CreationDate
			,@EndDate
			,@ExternalID
			,@HostingNeeds
			,@InProcessofCMSITSecurityApproval
			,@ITSecurityApproved
			,@LastUpdate
			,@Pros
			,@SolutionType
			,@StartDate
			,@UserInterface
			,@summary
			,@TargetContractAward
			,@TargetCompletionDate
			,@ZeroTrustAlignment
			,@HostingCloudStrategy
			,@WorkforceTrainingReqs
			,@GUID


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_EasiBusinessCaseSolution
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 04/18/2025	Aditya Sharma				Added New Fields
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_EasiBusinessCaseSolution] 
--Add the parameters for the stored procedure here
	@AcquisitionApproach nvarchar(max) = NULL, 
	@Cons nvarchar(max) = NULL, 
	@CostSavings nvarchar(255) = NULL, 
	@CreationDate nvarchar(255) = NULL, 
	@EndDate nvarchar(255) = NULL, 
	@ExternalID nvarchar(255) = NULL, 
	@HostingNeeds nvarchar(255) = NULL, 
	@InProcessofCMSITSecurityApproval nvarchar(255) = NULL, 
	@ITSecurityApproved nvarchar(255) = NULL, 
	@LastUpdate nvarchar(255) = NULL, 
	@Pros nvarchar(max) = NULL, 
	@SolutionType nvarchar(255) = NULL, 
	@StartDate nvarchar(255) = NULL, 
	@UserInterface nvarchar(255) = NULL,
	@summary nvarchar(max) = NULL,
	@hostingCloudServiceType  nvarchar(255) = NULL,
	@hostingLocation nvarchar(255) = NULL,
	@hostingType nvarchar(255) = NULL,

	@TargetContractAward nvarchar(255),
	@TargetCompletionDate nvarchar(255),
	@ZeroTrustAlignment nvarchar(max),
	@HostingCloudStrategy nvarchar(max),
	@WorkforceTrainingReqs nvarchar(max),

	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update EasiBusinessCaseSolution';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_object SET Note = @summary
		WHERE t_object.ea_guid = @GUID;


		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	
				WHEN Property = 'Cost Savings' THEN @CostSavings
				WHEN Property = 'Creation Date' THEN @CreationDate
				WHEN Property = 'End Date' THEN @EndDate
				WHEN Property = 'External ID' THEN @ExternalID
				WHEN Property = 'Hosting Needs' THEN @HostingNeeds
				WHEN Property = 'In Process of CMS IT Security Approval' THEN @InProcessofCMSITSecurityApproval
				WHEN Property = 'IT Security Approved' THEN @ITSecurityApproved
				WHEN Property = 'Last Update' THEN @LastUpdate
				WHEN Property = 'Solution Type' THEN @SolutionType
				WHEN Property = 'Start Date' THEN @StartDate
				WHEN Property = 'User Interface' THEN @UserInterface
				WHEN Property = 'Hosting Cloud Service Type' THEN @hostingCloudServiceType
				WHEN Property = 'Hosting Location' THEN @hostingLocation
				WHEN Property = 'Hosting Type' THEN @hostingType

				WHEN Property = 'Target Contract Award Date' THEN @TargetContractAward
				WHEN Property = 'Target Completion Date' THEN @TargetCompletionDate

				--WHEN Property = 'Acquisition Approach' THEN ISNULL(@AcquisitionApproach,Value)
				--WHEN Property = 'Cost Savings' THEN ISNULL(@CostSavings,Value)
				--WHEN Property = 'Creation Date' THEN ISNULL(@CreationDate,Value)
				--WHEN Property = 'End Date' THEN ISNULL(@EndDate,Value)
				--WHEN Property = 'External ID' THEN ISNULL(@ExternalID,Value)
				--WHEN Property = 'Hosting Needs' THEN ISNULL(@HostingNeeds,Value)
				--WHEN Property = 'In Process of CMS IT Security Approval' THEN ISNULL(@InProcessofCMSITSecurityApproval,Value)
				--WHEN Property = 'IT Security Approved' THEN ISNULL(@ITSecurityApproved,Value)
				--WHEN Property = 'Last Update' THEN ISNULL(@LastUpdate,Value)
				--WHEN Property = 'Solution Type' THEN ISNULL(@SolutionType,Value)
				--WHEN Property = 'Start Date' THEN ISNULL(@StartDate,Value)
				--WHEN Property = 'User Interface' THEN ISNULL(@UserInterface,Value)
				--WHEN Property = 'Hosting Cloud Service Type' THEN ISNULL(@hostingCloudServiceType,Value)
				--WHEN Property = 'Hosting Location' THEN ISNULL(@hostingLocation,Value)
				--WHEN Property = 'Hosting Type' THEN ISNULL(@hostingType,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Easi Business Case Solution';


		UPDATE SparxDB.dbo.t_objectproperties SET Notes = 
		CASE	
				WHEN Property = 'Cons' THEN @Cons
				WHEN Property = 'Pros' THEN @Pros
				WHEN Property = 'Acquisition Approach' THEN @AcquisitionApproach

				WHEN Property = 'Zero Trust Alignment' THEN @ZeroTrustAlignment
				WHEN Property = 'Hosting Cloud Strategy' THEN @HostingCloudStrategy
				WHEN Property = 'Workforce Training Reqs' THEN @WorkforceTrainingReqs

				ELSE Notes
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Easi Business Case Solution';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Easi Business Case Solution'
		and Value = '';

		UPDATE SparxDB.dbo.t_objectproperties SET Notes = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Easi Business Case Solution'
		and Notes = '';


PRINT 'End of Update EasiBusinessCaseSolution';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update EasiBusinessCaseSolution Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;










