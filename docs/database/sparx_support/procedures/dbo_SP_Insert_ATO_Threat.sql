




/*********************************************************************************************
 * PROCEDURE_NAME: Insert_ATO_Threat
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/23/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the ATO attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_ATO_Threat] 
			 @ControlFamily
			,@DaysOpen
			,@RiskLevel
			,@PoamId
			,@GUID
			,@OverallStatus
			,@ClosedDate
			,@RequiredRemediationDate



 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_ATO_Threat
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 12/17/2024	Aditya Sharma	CSUP-653	Add 3 new fields to CFACTs Threat/POAM data
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_ATO_Threat] 
--Add the parameters for the stored procedure here
	@ControlFamily nvarchar(255) = '', 
	@DaysOpen nvarchar(255) = '', 
	@RiskLevel nvarchar(255) = '', 
	@PoamId nvarchar(255) = '',
	@GUID nvarchar(60) = '',
	@OverallStatus nvarchar(255) = '', 
	@ClosedDate nvarchar(255) = '', 
	@RequiredRemediationDate nvarchar(255) = ''

AS

PRINT 'Inside Insert ATO Threat';
PRINT 'GUID = '+@GUID;

DECLARE @New_ObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		--BEGIN TRAN
		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=Threat;FQName=CMS EA::Threat;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'Threat', t_object.Name  = @PoamId
		WHERE t_object.ea_guid = @GUID;
		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'Control Family' as Property, @ControlFamily as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Days Open' as Property, @DaysOpen as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Risk Level' as Property, @RiskLevel as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Overall Status' as Property, @OverallStatus as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Closed Date' as Property, @ClosedDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Required Remediation Date' as Property, @RequiredRemediationDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
			
		) t;

--		Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;
--		Select ea_guid, Alias, Name, Object_ID from SparxDB.dbo.t_object where Object_ID = @New_ObjectID;

		--ROLLBACK;

	END TRY
	BEGIN CATCH

PRINT 'In Insert ATO Threat Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;










