





/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemSoftware_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/13/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemSoftware attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemSoftware_Reln] 
				@UsedforAICapabilities, 
				@PurchasedUnderEnterpriseLicenseAgreement, 
				@UsedasAPIGateway, 
				@ActualVersion,
				@SoftwareELAOrganization,
				@GUID




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemSoftware_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 07/17/2023	Aditya Sharma				Added v24 attributes
 * 08/13/2024	Aditya Sharma				Remove deprecated field for FY25 Census
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SystemSoftware_Reln] 
--Add the parameters for the stored procedure here
	@UsedforAICapabilities nvarchar(255) = NULL, 
	@PurchasedUnderEnterpriseLicenseAgreement nvarchar(255) = NULL, 
	@UsedasAPIGateway nvarchar(255) = NULL,
	@ActualVersion nvarchar(255) = NULL,
	--@SoftwareCost nvarchar(255) = NULL,
	@SoftwareELAOrganization nvarchar(255) = NULL,
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update SystemSoftware';

DECLARE @ConnectorID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		Select @ConnectorID = Connector_ID from SparxDB.dbo.t_connector where ea_guid = @GUID 
			and Stereotype = 'Uses Software Product';
PRINT 'Connector = '+CAST(@ConnectorID AS NVARCHAR(10))

		---Update the values into the tagged values specified in the stereotype using the ID of the connector from above.
		UPDATE ct SET Value = 
		CASE	
				WHEN Property = 'Used for AI Capabilities' THEN ISNULL(@UsedforAICapabilities,Value)
				WHEN Property = 'Purchased Under Enterprise License Agreement' THEN ISNULL(@PurchasedUnderEnterpriseLicenseAgreement,Value)
				WHEN Property = 'Used as API Gateway' THEN ISNULL(@UsedasAPIGateway,Value)
				WHEN Property = 'Actual Version' THEN ISNULL(@ActualVersion,Value)
				--WHEN Property = 'Software Cost' THEN ISNULL(@SoftwareCost,Value)
				WHEN Property = 'Software ELA Organization' THEN ISNULL(@SoftwareELAOrganization,Value)

				ELSE Value
		END
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'Uses Software Product';


		UPDATE ct 
		SET Value = NULL
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'Uses Software Product'
		and Value = '';


PRINT 'Properties Updated'

		-- Check the Updates

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID; -- check connection value records


	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Software Reln Exception'
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;


















