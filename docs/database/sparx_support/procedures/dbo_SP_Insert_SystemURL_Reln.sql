












/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemURL_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/31/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemURL attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemURL_Reln] 
			 	@SystemId, 
				@URLId




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemURL_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemURL_Reln] 
--Add the parameters for the stored procedure here
	@SystemId nvarchar(255) = NULL, 
	@URLId nvarchar(255) = NULL

AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@URL_ObjectId nvarchar(255) = '';


PRINT 'Inside Insert SystemURL';
PRINT 'SystemId = '+@SystemId;
PRINT 'URLId = '+@URLId;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';


IF len(@SystemId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @SystemId;
		Select @URL_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @URLId;


PRINT 'Start Reln processing'
		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
		Values (@System_ObjectId+' : '+@URL_ObjectId, 'Association', 'System has URL','Source -> Destination', @System_ObjectId, @URL_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

		Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where [Start_Object_ID] = @System_ObjectId and [End_Object_ID] = @URL_ObjectId
			and Stereotype = 'System has URL';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=System has URL;FQName=CMS EA::System has URL;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

		-- Check the inserts

		Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record


	END TRY
	BEGIN CATCH

PRINT 'Inside Insert System URL Reln Exception';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;


















