


/*********************************************************************************************
 * PROCEDURE_NAME: [SP_Update_SystemProperty_Census]
 *
 * AUTHOR: Sita Paturi
 * DATE:   07/11/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update System Flag attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemProperty_Census]
					@SystemGUID,
					@PropertyName,
					@PropertyValue

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.[SP_Update_SystemProperty_Census]
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE .[dbo].[SP_Update_SystemProperty_Census] 
--Add the parameters for the stored procedure here
	@SystemGUID nvarchar(60) = '',
	@PropertyName nvarchar(255) = NULL,
	@PropertyValue nvarchar(255) = NULL

AS

PRINT 'Inside Update SystemFlag';
DECLARE @RC INT = 0;

BEGIN
	BEGIN TRY

		IF len(ISNULL(@SystemGUID,'')) = 0 
			THROW 50001,'Input GUID is empty',1;


		IF len(@SystemGUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;


PRINT 'Before Update';
PRINT 'GUID = '+@SystemGUID;

				
				UPDATE		SparxDB.dbo.t_objectproperties SET Value = ISNULL(@PropertyValue,Value)
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @SystemGUID
					AND t_object.Stereotype = 'System'
					AND t_objectproperties.Property = @PropertyName;


PRINT 'After NULL Update';

	END
	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Maintainer Error';
		-- Log the error
		--EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;






