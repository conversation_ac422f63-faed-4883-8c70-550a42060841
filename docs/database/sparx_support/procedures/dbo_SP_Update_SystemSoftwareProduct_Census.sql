


/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemSoftwareProduct_Census
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   07/17/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update System attributes in SparxDB that come from Software Product Page
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemSoftwareProduct_Census] 
			@frontendAccessType,
			@APIDeveloped,
			@APIDescriptionPublished,
			@APIDescriptionLocation,
			@APIAccessibility,
			@DoestheAPIuseFHIR,
			@DoestheAPIuseFHIROther,
			@SystemhasAPIGateway,
			@APIHasPortal,
			@UsesAITechnology,
			@AIProjectLifeCycleStage,
			@AISolutionCategoryOther,
			@AISolutionCategory,
			@APICategory,
			@SystemGUID

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemSoftwareProduct_Census
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 
 *	
 *	
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE .[dbo].[SP_Update_SystemSoftwareProduct_Census] 
--Add the parameters for the stored procedure here
	@frontendAccessType nvarchar(255) = NULL,
	@APIDeveloped nvarchar(255) = NULL,
	@APIDescriptionPublished nvarchar(255) = NULL,
	@APIDescriptionLocation nvarchar(255) = NULL,
	@APIAccessibility nvarchar(255) = NULL,
	@DoestheAPIuseFHIR nvarchar(255) = NULL,
	@DoestheAPIuseFHIROther nvarchar(255) = NULL,
	@SystemhasAPIGateway nvarchar(255) = NULL,
	@APIHasPortal nvarchar(255) = NULL,
	@UsesAITechnology nvarchar(255) = NULL,
	@AIProjectLifeCycleStage nvarchar(255) = NULL,
	@AISolutionCategoryOther nvarchar(255) = NULL,
	@AISolutionCategory nvarchar(max) = NULL,
	@APICategory nvarchar(max) = NULL,
	@SystemGUID nvarchar(60) = ''

AS

PRINT 'Inside Update System SoftwareProduct';
DECLARE @RC INT = 0;

BEGIN
	BEGIN TRY

		IF len(ISNULL(@SystemGUID,'')) = 0 
			THROW 50001,'Input GUID is empty',1;


		IF len(@SystemGUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;


PRINT 'Before Update';
PRINT 'GUID = '+@SystemGUID;

				
				UPDATE SparxDB.dbo.t_objectproperties SET Value = 
				CASE	
						WHEN Property = 'API Developed' THEN @APIDeveloped
						WHEN Property = 'API Description Published' THEN @APIDescriptionPublished
						WHEN Property = 'API Description Location' THEN @APIDescriptionLocation
						WHEN Property = 'API Accessibility' THEN @APIAccessibility
						WHEN Property = 'Does the API use FHIR' THEN @DoestheAPIuseFHIR
						WHEN Property = 'Does the API use FHIR Other' THEN @DoestheAPIuseFHIROther
						WHEN Property = 'System has API Gateway' THEN @SystemhasAPIGateway
						WHEN Property = 'API Has Portal' THEN @APIHasPortal
						WHEN Property = 'Uses AI Technology' THEN @UsesAITechnology
						WHEN Property = 'AI Project Life Cycle Stage' THEN @AIProjectLifeCycleStage
						WHEN Property = 'AI Solution Category Other' THEN @AISolutionCategoryOther

						ELSE Value
/*****************************/		
				END
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties 
					ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @SystemGUID
				and t_object.Stereotype = 'System';

				UPDATE SparxDB.dbo.t_ObjectProperties SET Notes = @AISolutionCategory
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @SystemGUID
				and t_object.Stereotype = 'System'
				and t_objectproperties.[Property] = 'AI Solution Category'
	


PRINT 'Delete existing System->API Category relationships';

				EXECUTE @RC = [dbo].[SP_Delete_SystemAPICategory_Reln_Census] 
								@SystemGUID;
PRINT 'Create System->API Category relationships from input @APICategory string values';
				
/*****/				IF len(@APICategory) > 0
						DECLARE @APICategoryGUID nvarchar(100) = NULL,
								@APICategoryItem nvarchar(100) = NULL;

							DECLARE db_add_cursor CURSOR FOR
								Select * from  string_split(@APICategory, '|');
							OPEN db_add_cursor;
								FETCH NEXT FROM db_add_cursor INTO @APICategoryItem;
		PRINT '1st Fetch';
								WHILE @@FETCH_STATUS = 0
									BEGIN
									Select @APICategoryGUID = ea_guid from SparxDb.dbo.t_Object where Name = @APICategoryItem and Stereotype = 'DRM Element'
									EXECUTE @RC = [dbo].[SP_Insert_SystemAPICategory_Reln_Census] 
														@SystemGUID,
														@APICategoryGUID;
									-- Fetch next record
									FETCH NEXT FROM db_add_cursor INTO @APICategoryItem;
		PRINT 'Next Fetch';
								END
							-- Close the cursor
							CLOSE db_add_cursor;
							-- Deallocate the cursor
							DEALLOCATE db_add_cursor; 
/*****				END		*/		



PRINT 'After Main Update';

				UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties 
					ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @SystemGUID
				and t_object.Stereotype = 'System'
				and Value = ''
				and Property IN ('');

PRINT 'After NULL Update';

		END;

	END TRY
	BEGIN CATCH

PRINT 'Inside Update System SoftwareProduct Error';
		-- Log the error
		--EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;







