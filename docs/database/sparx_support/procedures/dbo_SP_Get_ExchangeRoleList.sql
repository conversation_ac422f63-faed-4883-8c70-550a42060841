






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Get_ExchangeRoleList
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Get the ExchangeRoleList attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Get_ExchangeRoleList] 
			 @id
			,@name
			,@version
			,@objectState
			,@status
			,@Outputjson


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Get_ExchangeRoleList
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Get_ExchangeRoleList] 
--Add the parameters for the stored procedure here
	@objectState nvarchar(255) = '',
	@status nvarchar(255) = '', 
	@version nvarchar(255) = '', 
	@id nvarchar(255) = '', 
	@name nvarchar(255) = '',
	@Outputjson nvarchar(max) OUT

AS

BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		SET @Outputjson = 
		(SELECT       
            a.[Sparx Exchange Role GUID] AS [refstr1],
            a.[Sparx Exchange Role GUID] AS [refstr],
            a.[Name] as name,
            a.[Description] AS [description],
            a.[Version] AS [version], 
            a.[State] as [state] ,
            a.[Status] AS [status]
		FROM [Sparx_Support].[dbo].[Sparx_Exchange_Role] a
		WHERE	(@objectState is null or (@objectState is not null and @objectState = a.[State]))
			AND (@status is null or (@status is not null and @status = a.STATUS))
			AND (@version is null or (@version is not null and @version = a.VERSION))
			AND (@id is null or (@id is not null and @id = a.[Sparx Exchange Role GUID]))
			AND (@name is null or (@name is not null and @name = a.[Name]))
		FOR JSON PATH, ROOT('ResultSet')
		);


	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












