




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_ATO_Threat
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/02/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theATO attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_ATO_Threat] 
			 @ControlFamily
			,@DaysOpen
			,@RiskLevel
			,@PoamId
			,@GUID
			,@OverallStatus
			,@ClosedDate
			,@RequiredRemediationDate

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_ATO_Threat
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 12/17/2024	Aditya Sharma	CSUP-653	Add 3 new fields to CFACTs Threat/POAM data
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_ATO_Threat] 
--Add the parameters for the stored procedure here
	@ControlFamily nvarchar(255) = '', 
	@DaysOpen nvarchar(255) = '', 
	@RiskLevel nvarchar(255) = '', 
	@PoamId nvarchar(255) = '', 
	@GUID nvarchar(60) = '',
	@OverallStatus nvarchar(255) = '', 
	@ClosedDate nvarchar(255) = '', 
	@RequiredRemediationDate nvarchar(255) = ''

AS

PRINT 'Inside Update ATO Threat';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	
				WHEN Property = 'Control Family' THEN ISNULL(@ControlFamily,Value)
				WHEN Property = 'Days Open' THEN ISNULL(@DaysOpen,Value)
				WHEN Property = 'Risk Level' THEN ISNULL(@RiskLevel,Value)
				WHEN Property = 'Overall Status' THEN ISNULL(@OverallStatus,Value)
				WHEN Property = 'Closed Date' THEN ISNULL(@ClosedDate,Value)
				WHEN Property = 'Required Remediation Date' THEN ISNULL(@RequiredRemediationDate,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Threat';


		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Threat'
		and Value = '';

PRINT 'End of Update ATO Threat';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update ATO Threat Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;










