





/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_EasiIntake
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/01/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the EasiIntake attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_EasiIntake] 
			 @AdminLead
			,@ArchiveDate
			,@BusinessOwner
			,@BusinessOwnerComponent
			,@ContractVehicle
			,@ContractNumber
			,@Contractor
			,@DecidedAt
			,@DecisionNextSteps
			,@eaCollaborator
			,@EASupportRequested
			,@EASiIntakeUUID
			,@ExistingContract
			,@ExistingFunding
			,@ExpectedIncreaseAmount
			,@ExpectingCostIncrease
			,@ExternalStatus
			,@FundingNumber
			,@FundingSource
			,@GRBMeetingDate
			,@GRTMeetingDate
			,@InformationSystemSecurityOfficer
			,@LifeCycleCostBaseline
			,@LifeCycleID
			,@LifeCycleExpiresAt
			,@LifeCycleScope
			,@OITCollaborator
			,@POPEndDate
			,@POPStartDate
			,@ProcessStatus
			,@ProductManager
			,@ProductManagerComponent
			,@ProjectAcronym
			,@RejectionReason
			,@RequestorEmail
			,@RequestorComponent
			,@RequestorName
			,@RequestorEUAID
			,@Solution
			,@ProjectStatus
			,@SubmittedAt
			,@trbCollaborator
			,@description
			,@GUID
			,@hasUIChanges
			,@CreationDate
			,@LastUpdateDate
			,@UsingSoftware
			,@AcquisitionMethods
			,@UsesAiTech
			,@CurrentAnnualSpending
			,@CurrentAnnualSpendingITPortion
			,@PlannedYearOneSpending
			,@PlannedYearOneSpendingITPortion
			,@ScheduledProductionDate


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_EasiIntake
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 11/03/2023	Aditya Sharma				Added New Field hasUIChanges to the processing
 * 11/21/2024	Aditya Sharma				Added Client Creation and Last Update Date to the fields
 * 12/13/2024	Aditya Sharma				Changed lengths of longer fields
 * 04/18/2025	Aditya Sharma				Added New fields
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_EasiIntake] 
--Add the parameters for the stored procedure here
	@AdminLead nvarchar(255) = NULL,
	@ArchiveDate nvarchar(255) = NULL,
	@BusinessOwner nvarchar(255) = NULL,
	@BusinessOwnerComponent nvarchar(255) = NULL,
	@ContractVehicle nvarchar(255) = NULL,
	@ContractNumber nvarchar(255) = NULL,
	@Contractor nvarchar(255) = NULL,
	@DecidedAt nvarchar(255) = NULL,
	@DecisionNextSteps nvarchar(max) = NULL,
	@EACollaborator nvarchar(255) = NULL,
	@EASupportRequested nvarchar(255) = NULL,
	@EASiIntakeUUID nvarchar(255) = NULL,
	@ExistingContract nvarchar(255) = NULL,
	@ExistingFunding nvarchar(255) = NULL,
	@ExpectedIncreaseAmount nvarchar(255) = NULL,
	@ExpectingCostIncrease nvarchar(255) = NULL,
	@ExternalStatus nvarchar(255) = NULL,
	@FundingNumber nvarchar(255) = NULL,
	@FundingSource nvarchar(255) = NULL,
	@GRBMeetingDate nvarchar(255) = NULL,
	@GRTMeetingDate nvarchar(255) = NULL,
	@InformationSystemSecurityOfficer nvarchar(255) = NULL,
	@LifeCycleCostBaseline nvarchar(255) = NULL,
	@LifeCycleID nvarchar(255) = NULL,
	@LifeCycleExpiresAt nvarchar(255) = NULL,
	@LifeCycleScope nvarchar(max) = NULL,
	@OITCollaborator nvarchar(255) = NULL,
	@POPEndDate nvarchar(255) = NULL,
	@POPStartDate nvarchar(255) = NULL,
	@ProcessStatus nvarchar(255) = NULL,
	@ProductManager nvarchar(255) = NULL,
	@ProductManagerComponent nvarchar(255) = NULL,
	@ProjectAcronym nvarchar(255) = NULL,
	@RejectionReason nvarchar(255) = NULL,
	@RequestorEmail nvarchar(255) = NULL,
	@RequestorComponent nvarchar(255) = NULL,
	@RequestorName nvarchar(255) = NULL,
	@RequestorEUAID nvarchar(255) = NULL,
	@Solution nvarchar(max) = NULL,
	@ProjectStatus nvarchar(255) = NULL,
	@SubmittedAt nvarchar(255) = NULL,
	@TRBCollaborator nvarchar(255) = NULL,
	@description nvarchar(max) = NULL,
	@GUID nvarchar(60) = '',
	@hasUIChanges nvarchar(255) = NULL,
	@CreationDate nvarchar(255) = NULL,
	@LastUpdateDate nvarchar(255) = NULL,
	@UsingSoftware nvarchar(255) = NULL,
	@AcquisitionMethods nvarchar(max) = NULL,
	@UsesAiTech nvarchar(255) = NULL,
	@CurrentAnnualSpending nvarchar(255) = NULL,
	@CurrentAnnualSpendingITPortion nvarchar(255) = NULL,
	@PlannedYearOneSpending nvarchar(255) = NULL,
	@PlannedYearOneSpendingITPortion nvarchar(255) = NULL,
	@ScheduledProductionDate nvarchar(255) = NULL
	


AS

PRINT 'Inside Insert EasiIntake';
PRINT 'GUID = '+@GUID;

BEGIN

	BEGIN TRY
		DECLARE @New_ObjectID INT = 0;

		IF len(ISNULL(@GUID,'')) = 0 
			THROW 50001,@AcquisitionMethods,1;

		IF len(@GUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;

PRINT @GUID;
PRINT 'ContractNumber :'+@ContractNumber
				--BEGIN TRAN
				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=EASi Intake;FQName=CMS EA::EASi Intake;@ENDSTEREO;', @GUID, '<none>');
PRINT 'Before Update of t_object fields';
				UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'EASi Intake', t_object.Alias  = @EASiIntakeUUID, Note = @description
				WHERE t_object.ea_guid = @GUID;
PRINT 'GUID = '+@GUID;
				SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
PRINT @New_ObjectID
PRINT 'Before Insert'
				INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
				Select ObjectID, Property, Val, Notes, ea_guid from
				(
					Select @New_ObjectID as ObjectID, 'Admin Lead' as Property, @AdminLead as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Archive Date' as Property, @ArchiveDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Business Owner' as Property, @BusinessOwner as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Business Owner Component' as Property, @BusinessOwnerComponent as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Contract Vehicle' as Property, @ContractVehicle as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Contract Number' as Property, @ContractNumber as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Contractor' as Property, @Contractor as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Decided At' as Property, @DecidedAt as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Decision Next Steps' as Property, '' as Val, @DecisionNextSteps as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'EA Collaborator' as Property, @EACollaborator as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'EA Support Requested' as Property, @EASupportRequested as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'EASi Intake UUID' as Property, @EASiIntakeUUID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Existing Contract' as Property, @ExistingContract as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Existing Funding' as Property, @ExistingFunding as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Expected Increase Amount' as Property, @ExpectedIncreaseAmount as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Expecting Cost Increase' as Property, @ExpectingCostIncrease as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'External Status' as Property, @ExternalStatus as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Funding Number' as Property, @FundingNumber as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Funding Source' as Property, @FundingSource as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'GRB Meeting Date' as Property, @GRBMeetingDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'GRT Meeting Date' as Property, @GRTMeetingDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Information System Security Officer' as Property, @InformationSystemSecurityOfficer as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Life Cycle Cost Baseline' as Property, @LifeCycleCostBaseline as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Life Cycle ID' as Property, @LifeCycleID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Life Cycle Expires At' as Property, @LifeCycleExpiresAt as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Life Cycle Scope' as Property, '' as Val, @LifeCycleScope as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'OIT Collaborator' as Property, @OITCollaborator as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'POP End Date' as Property, @POPEndDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'POP Start Date' as Property, @POPStartDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Process Status' as Property, @ProcessStatus as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Product Manager' as Property, @ProductManager as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Product Manager Component' as Property, @ProductManagerComponent as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Project Acronym' as Property, @ProjectAcronym as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Rejection Reason' as Property, @RejectionReason as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Requestor Email' as Property, @RequestorEmail as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Requestor Component' as Property, @RequestorComponent as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Requestor Name' as Property, @RequestorName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Requestor EUA ID' as Property, @RequestorEUAID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Solution' as Property,'<memo>' as Val, @Solution as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Project Status' as Property, @ProjectStatus as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Submitted At' as Property, @SubmittedAt as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'TRB Collaborator' as Property, @TRBCollaborator as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Has UI Changes' as Property, @hasUIChanges as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Creation Date' as Property, @CreationDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Last Update' as Property, @LastUpdateDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Using Software' as Property, @UsingSoftware as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Acquisition Methods' as Property, '' as Val, @AcquisitionMethods as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Uses AI Tech' as Property,@UsesAiTech  as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Current Annual Spending' as Property, @CurrentAnnualSpending as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Current Annual Spending IT Portion' as Property, @CurrentAnnualSpendingITPortion as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Planned Year One Spending' as Property,@PlannedYearOneSpending  as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Planned Year One Spending IT Portion' as Property, @PlannedYearOneSpendingITPortion as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Scheduled Production Date' as Property, @ScheduledProductionDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
					
				) t;

				--Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

				--ROLLBACK;
		END
	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;





