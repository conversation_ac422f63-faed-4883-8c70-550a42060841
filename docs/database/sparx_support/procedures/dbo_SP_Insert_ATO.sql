




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_ATO
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/23/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the ATO attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_ATO] 
			 @AccessedbyNonOrgUsers
			,@Acronym
			,@ActualDispositionDate
			,@ATOID
			,@CMS_UUID
			,@CollectMaintainSharePII
			,@CountofOpenPOAMs
			,@EAuthenticationLevel
			,@EffectiveDate
			,@ExpirationDate
			,@FIPS199OverallImpactRating
			,@HasPHI
			,@LastActDate
			,@LastAssessmentDate
			,@LastContingencyPlanCompletionDate
			,@LastPentestDate
			,@NonPrivilegedUserPopulation
			,@PIACompletionDate
			,@PIIisLimitedtoUsernameandPassword
			,@PrivacyPOC
			,@PrivilegedUserPopulation
			,@RecoveryPointObjective
			,@RecoveryTimeObjective
			,@TLCPhase
			,@primaryCyberRiskAdvisor
			,@OAStatus
			,@GUID

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_ATO
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 06/10/2025	Aditya Sharma				Add OA Status field
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_ATO] 
--Add the parameters for the stored procedure here
	@AccessedbyNonOrgUsers nvarchar(255) = NULL, 
	@Acronym nvarchar(255) = NULL, 
	@ActualDispositionDate nvarchar(255) = NULL, 
	@ATOID nvarchar(255) = NULL, 
	@CMS_UUID nvarchar(255) = NULL, 
	@CollectMaintainSharePII nvarchar(255) = NULL, 
	@CountofOpenPOAMs nvarchar(255) = NULL, 
	@EAuthenticationLevel nvarchar(255) = NULL, 
	@EffectiveDate nvarchar(255) = NULL, 
	@ExpirationDate nvarchar(255) = NULL, 
	@FIPS199OverallImpactRating nvarchar(255) = NULL, 
	@HasPHI nvarchar(255) = NULL, 
	@informationSystemProgramName nvarchar(255) = NULL, 
	@LastActDate nvarchar(255) = NULL, 
	@LastAssessmentDate nvarchar(255) = NULL, 
	@LastContingencyPlanCompletionDate nvarchar(255) = NULL, 
	@LastPentestDate nvarchar(255) = NULL, 
	@NonPrivilegedUserPopulation nvarchar(255) = NULL, 
	@PIACompletionDate nvarchar(255) = NULL, 
	@PIIisLimitedtoUsernameandPassword nvarchar(255) = NULL, 
	@PrivacyPOC nvarchar(255) = NULL, 
	@PrivilegedUserPopulation nvarchar(255) = NULL, 
	@RecoveryPointObjective nvarchar(255) = NULL, 
	@RecoveryTimeObjective nvarchar(255) = NULL, 
	@TLCPhase nvarchar(255) = NULL, 
	@primaryCyberRiskAdvisor nvarchar(255) = NULL, 
	@OAStatus nvarchar(255) = NULL,
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Insert ATO';
PRINT 'GUID = '+@GUID;

DECLARE @New_ObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		--BEGIN TRAN
		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=ATO Security Boundary;FQName=CMS EA::ATO Security Boundary;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'ATO Security Boundary', t_object.Alias  = @CMS_UUID, Name = @informationSystemProgramName
		WHERE t_object.ea_guid = @GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'Accessed by Non-Org Users' as Property, @AccessedbyNonOrgUsers as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Acronym' as Property, @Acronym as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Actual Disposition Date' as Property, @ActualDispositionDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'ATO ID' as Property, @ATOID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'CMS_UUID' as Property, @CMS_UUID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Collect Maintain Share PII' as Property, @CollectMaintainSharePII as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Count of Open POAMs' as Property, @CountofOpenPOAMs as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'EAuthentication Level' as Property, @EAuthenticationLevel as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Effective Date' as Property, @EffectiveDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Expiration Date' as Property, @ExpirationDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'FIPS 199 Overall Impact Rating' as Property, @FIPS199OverallImpactRating as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Has PHI' as Property, @HasPHI as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Last Act Date' as Property, @LastActDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Last Assessment Date' as Property, @LastAssessmentDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Last Contingency Plan Completion Date' as Property, @LastContingencyPlanCompletionDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Last Pentest Date' as Property, @LastPentestDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'NonPrivileged User Population' as Property, @NonPrivilegedUserPopulation as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'PIA Completion Date' as Property, @PIACompletionDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'PII is Limited to Username and Password' as Property, @PIIisLimitedtoUsernameandPassword as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Privacy POC' as Property, @PrivacyPOC as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Privileged User Population' as Property, @PrivilegedUserPopulation as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Recovery Point Objective' as Property, @RecoveryPointObjective as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Recovery Time Objective' as Property, @RecoveryTimeObjective as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'TLC Phase' as Property, @TLCPhase as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Primary Cyber Risk Advisor' as Property, @primaryCyberRiskAdvisor as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'OA Status' as Property, @OAStatus as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid
		) t;

		--Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

PRINT 'At End of Insert ATO';

	END TRY
	BEGIN CATCH

PRINT 'In Insert ATO Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;


