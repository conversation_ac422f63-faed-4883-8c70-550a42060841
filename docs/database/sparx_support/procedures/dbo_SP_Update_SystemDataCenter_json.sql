








/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemDataCenter_json
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/14/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemDataCenter attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemDataCenter_json] 
 			 @jsonInput
 *
 *
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemDataCenter_json
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 08/07/2023	Aditya Sharma				Add FY 2024 new fields
 * 08/12/2024	Aditya Sharma				Add FY25 new field: WAN Type - Other 
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SystemDataCenter_json] 
--Add the parameters for the stored procedure here
	@jsonInput nvarchar(max)

AS

DECLARE 

	@GUID nvarchar(60) = '',
	@systemId nvarchar(255) = '', 
	@DataCenterId nvarchar(255) = '', 
	@ApplicationSoftwareReplicated nvarchar(255) = NULL, 
	--@CCICIntegrationFlag nvarchar(255) = NULL, 
	@ContractorName nvarchar(255) = NULL, 
	@DataReplicated nvarchar(255) = NULL, 
	@Environment nvarchar(255) = NULL, 
	--@FedContactOrg nvarchar(255) = NULL, 
	@HotSite nvarchar(255) = NULL, 
	--@ImportLookup nvarchar(255) = NULL, 
	--@IncidentResponseContact nvarchar(255) = NULL, 
	--@MultiFactorAuthentication nvarchar(255) = NULL, 
	@ProductionDataUseFlag nvarchar(255) = NULL, 
	--@RelationshipImpact nvarchar(255) = NULL, 
	@RelationshipStatus nvarchar(255) = NULL, 
	@SystemServerSoftwareReplicated nvarchar(255) = NULL, 
	--@UtilizesVPN nvarchar(255) = NULL, 
	@WANType nvarchar(max) = NULL, 
	@UsersRequiringMultifactorAuthentication nvarchar(max) = NULL, 
	@OtherSpecialUsers nvarchar(255) = NULL, 
	@NetworkEncryption nvarchar(max) = NULL, 
	@WANTypeOther nvarchar(max) = NULL, 
	@RowNum INT = 0,
	@RC INT = 0

PRINT 'Inside Update SystemDataCenter json';

DECLARE @New_ObjectID INT = 0;

IF len(@jsonInput) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

-- Do the System DataCenter addition 

PRINT 'Declare Cursor';
		
	
		DECLARE db_add_cursor CURSOR FOR
		SELECT s.cms_app_software_replicated,s.cms_data_center_wan_type,s.cms_data_replicated,s.cms_dc_contractor_name
			,s.cms_hot_site,s.cms_production_data_use_flag,s.cms_sys_server_sw_replicated,s.sag_deploymenttype
			,s.UsersRequiringMultifactorAuthentication, s.OtherSpecialUsers, s.NetworkEncryption, s.cms_data_center_wan_type_other
			,s.ConnectionGUID
			FROM
				OPENJSON (@jsonInput,'$.Objects')  
			WITH (   
							 cms_app_software_replicated nvarchar(255) '$.Values.cms_app_software_replicated'	
							,cms_data_center_wan_type nvarchar(255) '$.Values.cms_data_center_wan_type'	
							,cms_data_replicated nvarchar(255) '$.Values.cms_data_replicated'	
							,cms_dc_contractor_name nvarchar(255) '$.Values.cms_dc_contractor_name'	
							,cms_hot_site nvarchar(255) '$.Values.cms_hot_site'	
							,cms_production_data_use_flag nvarchar(255) '$.Values.cms_production_data_use_flag'	
							,cms_sys_server_sw_replicated nvarchar(255) '$.Values.cms_sys_server_sw_replicated'	
							,name nvarchar(255) '$.Values.name'	
							,objectstate nvarchar(255) '$.Values.objectstate'	
							,sag_deploymenttype nvarchar(255) '$.Values.sag_deploymenttype'	
							,Id nvarchar(255) '$.Id'	
							,ClassName nvarchar(255) '$.ClassName'	
							,ConnectionGUID nvarchar(255) '$.RefStr'
							,UsersRequiringMultifactorAuthentication nvarchar(max) '$.Values.cms_users_requiring_multifactor_authentication'	
							,OtherSpecialUsers nvarchar(255) '$.Values.cms_other_special_users'	
							,NetworkEncryption nvarchar(max) '$.Values.cms_network_encryption'	
							,cms_data_center_wan_type_other nvarchar(max) '$.Values.cms_data_center_wan_type_other'	
							
				) s
			WHERE ClassName = 'Deployment';

PRINT 'Before Open';

		OPEN db_add_cursor;

PRINT 'After Open';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_cursor INTO 
			 @ApplicationSoftwareReplicated,@WANType,@DataReplicated,@ContractorName
			,@HotSite,@ProductionDataUseFlag,@SystemServerSoftwareReplicated,@Environment
			,@UsersRequiringMultifactorAuthentication, @OtherSpecialUsers, @NetworkEncryption, @WANTypeOther
			,@GUID;

PRINT '1st Fetch';
PRINT @GUID;


		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			
			-- Call DataCenter Reln Update

			EXECUTE @RC = [dbo].[SP_Update_SystemDataCenter_Reln] 
					@ApplicationSoftwareReplicated, 
					NULL, 
					@ContractorName, 
					@DataReplicated, 
					@Environment, 
					NULL, 
					@HotSite, 
					NULL, 
					NULL, 
					NULL, 
					@ProductionDataUseFlag, 
					NULL, 
					NULL, 
					@SystemServerSoftwareReplicated, 
					NULL, 
					@WANType,
					@UsersRequiringMultifactorAuthentication,
					@OtherSpecialUsers,
					@NetworkEncryption,
					@WANTypeOther,
					@GUID

			-- Fetch next record
			FETCH NEXT FROM db_add_cursor INTO 
				 @ApplicationSoftwareReplicated,@WANType,@DataReplicated,@ContractorName
				,@HotSite,@ProductionDataUseFlag,@SystemServerSoftwareReplicated,@Environment
			    ,@UsersRequiringMultifactorAuthentication, @OtherSpecialUsers, @NetworkEncryption, @WANTypeOther
				,@GUID;

PRINT 'Next Fetch';

		END 

		-- 6 - Close the cursor
		CLOSE db_add_cursor;  

		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_cursor; 

-- Update the Reporting Table for the System

		DELETE FROM Sparx_Support.CEDAR_API.Sparx_System_DataCenter_Full_Tbl where [Sparx System GUID] = @systemId;

		INSERT INTO Sparx_Support.CEDAR_API.Sparx_System_DataCenter_Full_Tbl
		SELECT * FROM Sparx_Support.dbo.Sparx_System_DataCenter_Full where [Sparx System GUID] = @systemId;


PRINT 'End of Update SystemDataCenter json';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update System DataCenter json exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;














