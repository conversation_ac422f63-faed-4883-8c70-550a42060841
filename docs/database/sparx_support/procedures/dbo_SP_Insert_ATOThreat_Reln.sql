











/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_ATOThreat_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/08/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the ATOThreat attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_ATOThreat_Reln] 
			 	@ATOId, 
				@ThreatId
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_ATOThreat_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_ATOThreat_Reln] 
--Add the parameters for the stored procedure here
	@ATOId nvarchar(255) = '', 
	@ThreatId nvarchar(255) = ''

AS

DECLARE
	@ATO_ObjectId nvarchar(255) = '', 
	@Threat_ObjectId nvarchar(255) = '';


PRINT 'Inside Insert ATOThreat Reln';
PRINT 'ATOId = '+@ATOId;
PRINT 'Threat = '+@ThreatId;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @RelStatusNotes nvarchar(max) = 'Values: Future,Active,Retired,Deleted,Never Implemented  Default: Active  ';
DECLARE @RelStatusDefault nvarchar(255);
DECLARE @RelImpactNotes nvarchar(max) = 'Values: Critical,Major,Moderate,Minor,None,Unknown  Default: Moderate  ';
DECLARE @RelImpactDefault nvarchar(255);

IF len(@ATOId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--BEGIN TRAN

		Select @ATO_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @ATOId;
		Select @Threat_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @ThreatId;
		Select @RelStatusDefault = LTRIM(RTRIM(SUBSTRING(@RelStatusNotes,CHARINDEX('Default:',@RelStatusNotes,1)+8,LEN(@RelStatusNotes)-CHARINDEX('Default:',@RelStatusNotes,1) -7)));
		Select @RelImpactDefault = LTRIM(RTRIM(SUBSTRING(@RelImpactNotes,CHARINDEX('Default:',@RelImpactNotes,1)+8,LEN(@RelImpactNotes)-CHARINDEX('Default:',@RelImpactNotes,1) -7)));


		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
		Values (@ATO_ObjectId+' : '+@Threat_ObjectId, 'Abstraction', 'trace','Source -> Destination', @ATO_ObjectId, @Threat_ObjectId , CONCAT('{',NEWID(),'}'))

		Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where Start_Object_ID = @ATO_ObjectId and End_Object_ID = @Threat_ObjectId 
			and Stereotype = 'trace';

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=trace;FQName=CMS EA::trace;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
/*
		---insert the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Status', @RelStatusDefault, @RelStatusNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Impact', @RelImpactDefault, @RelImpactNotes, CONCAT('{',NEWID(),'}'));
*/


		-- Check the inserts

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records

PRINT 'End of Insert ATOThreat Reln';

	END TRY
	BEGIN CATCH

PRINT 'Inside Insert ATOThreat Reln Exception';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;

















