






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_EASiBCSCostLine
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the EASiBCSCostLine attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_EASiBCSCostLine] 
			 @Budget
			,@CostofCapital
			,@MonetaryCodeID
			,@MonetaryType
			,@MonetaryUnit
			,@Year
			,@BusinessCaseSolutionID
			,@phase
			,@solution
			,@GUID



 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_EASiBCSCostLine
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_EASiBCSCostLine] 
--Add the parameters for the stored procedure here
	@Budget nvarchar(255) = NULL, 
	@CostofCapital nvarchar(255) = NULL, 
	@MonetaryCodeID nvarchar(255) = NULL, 
	@MonetaryType nvarchar(255) = NULL, 
	@MonetaryUnit nvarchar(255) = NULL, 
	@Year nvarchar(255) = NULL, 
	@BusinessCaseSolutionID nvarchar(255) = NULL, 
	@GUID nvarchar(60) = '',
	@solution nvarchar(max) = NULL,
	@phase nvarchar(255) = NULL

AS

PRINT 'Inside Insert EASiBCSCostLine';
PRINT 'GUID = '+@GUID;


BEGIN

	BEGIN TRY
		DECLARE @Parent_ObjectID INT = 0;
		DECLARE @New_ObjectID INT = 0;

		IF len(ISNULL(@GUID,'')) = 0 
			THROW 50001,'Input GUID is empty',1;

		IF len(@GUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;


PRINT 'BusinessCaseSolutionID :'+@BusinessCaseSolutionID

				SELECT @Parent_ObjectID = Object_ID from SparxDB.dbo.t_object where ea_guid = @BusinessCaseSolutionID;
PRINT @Parent_ObjectID

				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=EASi BCS Cost Line;FQName=CMS EA::EASi BCS Cost Line;@ENDSTEREO;', @GUID, '<none>');
PRINT 'Before Update';
				UPDATE SparxDB.dbo.t_object set Stereotype = 'EASi BCS Cost Line', ParentID = @Parent_ObjectID
				WHERE t_object.ea_guid = @GUID;
PRINT 'GUID = '+@GUID;
				SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
PRINT @New_ObjectID
PRINT 'Before Insert'
				INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
				Select ObjectID, Property, Val, Notes, ea_guid from
				(
					Select @New_ObjectID as ObjectID, 'Budget' as Property, @Budget as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Cost of Capital' as Property, @CostofCapital as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Monetary Code ID' as Property, @MonetaryCodeID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Monetary Type' as Property, @MonetaryType as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Monetary Unit' as Property, @MonetaryUnit as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Year' as Property, @Year as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'phase' as Property, @phase as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'solution' as Property, '' as Val, @solution as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
				) t;

				--Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

		END
	END TRY
	BEGIN CATCH
PRINT 'Inside SP_Insert_EASiBCSCostLine exception'
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;


