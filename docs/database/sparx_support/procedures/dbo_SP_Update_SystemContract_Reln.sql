





/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemContract_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/09/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemContract attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemContract_Reln] 
				@IsDeliveryOrg,
				@GUID




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemContract_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SystemContract_Reln] 
--Add the parameters for the stored procedure here
	@IsDeliveryOrg nvarchar(255) = NULL,
	@GUID nvarchar(60) = NULL

AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@Contract_ObjectId nvarchar(255) = '',
	@Cnt INT = 0;


PRINT 'Inside Update SystemContract';
PRINT 'Is Delivery Org = '+@IsDeliveryOrg;

DECLARE @ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @systemId nvarchar(60) = '';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		Select @ConnectorID = Connector_ID from SparxDB.dbo.t_connector where ea_guid = @GUID 
			and Stereotype = 'Develops';
PRINT 'Connector = '+CAST(@ConnectorID AS NVARCHAR(10))


		---Update the values into the tagged values spefified in the stereotype using the ID of the connector from above.


		Select @Cnt = Count(1) 
		FROM SparxDB.dbo.t_connectortag 
		WHERE Property = 'Application Delivery Org'
		and ElementID = @ConnectorID;

		If (@Cnt = 0)
			Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
			Values (@ConnectorID, 'Application Delivery Org', @IsDeliveryOrg, '', CONCAT('{',NEWID(),'}'));
		ELSE
			Update SparxDB.dbo.t_connectortag 
			SET Value = @IsDeliveryOrg
			WHERE Property = 'Application Delivery Org'
			and ElementID = @ConnectorID;


		--Select @Cnt = Count(1) 
		--FROM SparxDB.dbo.t_connectortag 
		--WHERE Property = 'Delivery Org'
		--and ElementID = @ConnectorID;

		--If (@Cnt = 0)
		--	Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		--	Values (@ConnectorID, 'Delivery Org', @IsDeliveryOrg, '', CONCAT('{',NEWID(),'}'));
		--ELSE
		--	Update SparxDB.dbo.t_connectortag 
		--	SET Value = @IsDeliveryOrg
		--	WHERE Property = 'Delivery Org'
		--	and ElementID = @ConnectorID;


-- Update the Reporting Table for the System

		Select @systemId = o.ea_guid
		from SparxDB.dbo.t_connector c
		join SparxDB.dbo.t_object o on o.Object_ID = c.End_Object_ID and c.ea_guid = @GUID; 

		DELETE FROM Sparx_Support.CEDAR_API.Sparx_System_Contract_Full_Tbl where [Sparx System GUID] = @systemId;

		INSERT INTO Sparx_Support.CEDAR_API.Sparx_System_Contract_Full_Tbl
		SELECT * FROM Sparx_Support.dbo.Sparx_System_Contract_Full where [Sparx System GUID] = @systemId;



PRINT 'Properties Updated'

		-- Check the Updates

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID; -- check connection value records


	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Contract exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;

















