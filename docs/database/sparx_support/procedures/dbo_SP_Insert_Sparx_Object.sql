








/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_Sparx_Object
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/31/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the Sparx_Object attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_Sparx_Object] 
			 @Alias
			,@Name
			,@Package
			,@GUID OUT
			,@ObjectId OUT


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_Sparx_Object
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE   PROCEDURE [dbo].[SP_Insert_Sparx_Object] 
--Add the parameters for the stored procedure here
	@Alias nvarchar(255) = NULL, 
	@Name nvarchar(255) = NULL, 
	@Package nvarchar(255) = NULL, 

	@GUID nvarchar(60) OUT,
	@ObjectId INT OUT

AS

DECLARE @PackageId INT = NULL;

PRINT 'Inside Insert Sparx_Object';
PRINT 'Alias = '+@Alias;


IF len(@Alias) > 0 OR len(@Name) > 0
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


Select @GUID = CONCAT('{',NEWID(),'}');
PRINT  '@GUID =  '+@GUID; 
PRINT 'Before Object Insert';

		Select @PackageId = Package_Id from SparxDB.dbo.t_package where Name = @Package

		INSERT INTO SparxDB.dbo.t_object(ea_guid, Name, Alias, Object_Type, Diagram_ID, Author, Version, Package_ID, NType, Complexity, Effort, Backcolor, BorderStyle, BorderWidth, Fontcolor, Bordercolor, CreatedDate, ModifiedDate, Status, Abstract, Tagged, GenType, Phase, Scope, Classifier, TPos, IsRoot, IsLeaf, IsSpec, IsActive)
		VALUES (@GUID, @Name, @Alias, IIF(@Package = 'ATO','Class','Artifact'), 0, 'SP_Insert_Sparx_Object', '1.0', @PackageId, 0, 1, 0, -1, 0, -1, -1, -1, GETDATE(), GETDATE(), 'Proposed' , 0, 0, 'Java', '1.0', 'Public', 0, 0, 0, 0, 0, 0);

PRINT 'Before Object retrival';


		SELECT @ObjectId = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;

PRINT @ObjectId;


PRINT 'At End of Insert Sparx_Object';


	END TRY
	BEGIN CATCH
PRINT 'In Insert Sparx_Object Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












