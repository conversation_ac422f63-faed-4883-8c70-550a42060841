










/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_URL
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/31/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the URL attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_URL] 
			 @ConfidenceLevel
			,@HostingEnvironment
			,@IsIntranetOnly
			,@PortalServicesUsed
			,@ProvidesVersionCodeRepositoryAccess
			,@URLAPIAWF
			,@URLAPIEndpoint
			,@URLLink
			,@UsedforBeneficiary
			,@UsesHTTPS
			,@SystemGUID
			,@GUID


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_URL
 *
 * UPDATE HISTORY:
 * ---------------------------------------------------------------------------------------------------------------------------
 * Date			Changed By			Issue/Bug								Description
 * 08/07/2023	David Scarborough	ProvidesVersionCodeRepositoryAccess		Attribute/property dropped for FY24 Census
 * 01/22/2024	Aditya Sharma		Wrong stereotype set for the reln		Correct the Stereotype on t_xref to "System has URL"
 *
 * ----------------------------------------------------------------------------------------------------------------------------
 ******************************************************************************************************************************/
CREATE   PROCEDURE [dbo].[SP_Insert_URL] 
--Add the parameters for the stored procedure here
	@ConfidenceLevel nvarchar(255) = NULL, 
	@HostingEnvironment nvarchar(255) = NULL, 
	@IsIntranetOnly nvarchar(255) = NULL, 
	@PortalServicesUsed nvarchar(255) = NULL, 
	@ProvidesVersionCodeRepositoryAccess nvarchar(255) = NULL, 
	@URLAPIAWF nvarchar(255) = NULL, 
	@URLAPIEndpoint nvarchar(255) = NULL, 
	@URLLink nvarchar(255) = NULL, 
	@UsedforBeneficiary nvarchar(255) = NULL, 
	@UsesHTTPS nvarchar(255) = NULL, 
	@SystemGUID nvarchar(60) = '', --t_object.eaguid for t_object.Stereotype = 'System'
	@GUID nvarchar(60) = '' --t_object.eaguid for t_object.Stereotype = 'System URL'

AS

	Print 'Inside Insert URL';
	Print 'GUID = '+@GUID;

	DECLARE @New_ObjectID INT = 0;
	DECLARE @System_ObjectId INT = 0;
	DECLARE @New_ConnectorGUID nvarchar(60) = NULL;
	DECLARE @System_Alias nvarchar(255) = NULL;
	DECLARE @URL_Alias nvarchar(255) = NULL;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--ALTER a t_object record with minimum info usinf dbo.SP_Insert_Sparx_object
		--
		--Select @GUID;
		
		Print 'Insert t-xref Section';
		Print 'GUID = '+@GUID;

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=System URL;FQName=CMS EA::System URL;@ENDSTEREO;', @GUID, '<none>');
		
		Print 'Begin Update t_object Section';
		Print 'GUID = '+@GUID;
		Print 'URLName : '+@URLLink;

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'System URL', Name = @URLLink
		WHERE t_object.ea_guid = @GUID;
		 

		SELECT	@New_ObjectID = OBJECT_ID,
				@URL_Alias = Alias
				from SparxDB.dbo.t_object where ea_guid = @GUID;
		
		Print '@New_ObjectID from t_object Object_ID: ' +CAST (@New_ObjectID AS nvarchar (255) );

		PRINT 'Before Propertry Insert';

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'Confidence Level' as Property, @ConfidenceLevel as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Hosting Environment' as Property, @HostingEnvironment as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Is Intranet Only' as Property, @IsIntranetOnly as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Portal Services Used' as Property, @PortalServicesUsed as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Provides Version Code Repository Access' as Property, @ProvidesVersionCodeRepositoryAccess as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'URL API AWF' as Property, @URLAPIAWF as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'URL API Endpoint' as Property, @URLAPIEndpoint as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'URLLink' as Property, @URLLink as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Used for Beneficiary' as Property, @UsedforBeneficiary as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Uses HTTPS' as Property, @UsesHTTPS as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
		) t;

		PRINT 'After Propertry Insert';

		/**Select t_objectproperties.* 
		from SparxDB.dbo.t_objectproperties 
		where Object_ID = @New_ObjectID;
		**/

		Select		 t_object.* 
					,t_objectproperties.* 
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON		t_object.Object_ID = t_objectproperties.Object_ID
		where		t_object.Object_ID = @New_ObjectID;
		

		IF len(@SystemGUID) > 0 
		BEGIN
			-- Put a relationship between the System and the URL objects
				Select @System_ObjectId = Object_ID
				  from SparxDB.dbo.t_object 
				 where ea_guid = @SystemGUID;

			PRINT 'Select @New_ObjectId: ' +  +CAST (@New_ObjectId AS nvarchar (255) );
			
			PRINT 'Start Connector Reln processing';

				Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction, Start_Object_ID, End_Object_ID, ea_guid)
				Values (CAST(@System_ObjectId  AS nvarchar (255))+' : '+CAST(@New_ObjectId AS nvarchar (255)), 'Association', 'System has URL','Source -> Destination', @System_ObjectId, @New_ObjectId , CONCAT('{',NEWID(),'}'))
		
			PRINT 'Connector created'

			PRINT 'Start t_xref with Select @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector'

				Select @New_ConnectorGUID = ea_guid 
				  from SparxDB.dbo.t_connector 
				 where Start_Object_ID = @System_ObjectId 
				   and End_Object_ID = @New_ObjectId
				   and Stereotype = 'System has URL';

			PRINT 'INSERT INTO SparxDB.dbo.t_xref'
		
				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=System has URL;FQName=CMS EA::System has URL;@ENDSTEREO;', @New_ConnectorGUID, '<test2>');

			PRINT 'XREF created'
		
			PRINT 'At End of Insert URL';

		END
		

	END TRY

	BEGIN CATCH
		PRINT 'In Insert URL Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH

END;















