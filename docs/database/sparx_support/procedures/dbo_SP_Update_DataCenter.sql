


/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_DataCenter
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/02/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the Data Center attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_DataCenter] 
 *	   @AddrL1
 *	  ,@AddrL2
 *	  ,@City
 *	  ,@State
 *	  ,@Zip
 *	  ,@DataCenterID
 *	  ,@DataCenterType
 *	  ,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_DataCenter
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_DataCenter] 
--Add the parameters for the stored procedure here
       @AddrL1 nvarchar(255) = '', 
       @AddrL2 nvarchar(255) = '',
       @City nvarchar(255) = '', 
       @State nvarchar(80) = '', 
       @Zip nvarchar(25) = '', 
       @DataCenterID nvarchar(50) = '', 
       @DataCenterType nvarchar(100) = '', 
       @GUID nvarchar(60) = ''
AS
IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'Address Line 1' THEN ISNULL(@AddrL1,Value) 
				WHEN Property = 'Address Line 2' THEN ISNULL(@AddrL2,Value)
				WHEN Property = 'City' THEN ISNULL(@City,Value)
				WHEN Property = 'State' THEN ISNULL(@State,Value)
				WHEN Property = 'Zip Code' THEN ISNULL(@Zip,Value)
				WHEN Property = 'Data Center Type' THEN ISNULL(@DataCenterType,Value)
				WHEN Property = 'Data Center ID' THEN ISNULL(@DataCenterID,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Data Center';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Data Center'
		and Value = '';


	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;








