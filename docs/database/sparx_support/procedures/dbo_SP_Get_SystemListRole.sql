







/*********************************************************************************************
 * PROCEDURE_NAME: SP_Get_SystemListRole
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   09/06/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Get the SystemList attributes in SparxDB alongwith Person Roles
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Get_SystemListRole] 
			 @objectState
			,@status
			,@version
			,@belongsTo
			,@includeInSurvey
			,@personEUA
			,@personRole
			,@Outputjson


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Get_SystemListRole
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 11/28/2023	Aditya Sharma				Remove Duplicates based on Role/Person using a DISTINCT and remove redundant column
 * 08/08/2024	Aditya Sharma				Add ATO Dates to the Output
 * 08/16/2024	Aditya Sharma				Use Reporting table instead of View
 * 08/21/2024	Aditya Sharma				Use [Current System Survey] flag instead of [Next System Survey]
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Get_SystemListRole] 
--Add the parameters for the stored procedure here
	@objectState nvarchar(255) = NULL,
	@status nvarchar(255) = NULL, 
	@version nvarchar(255) = NULL, 
	@belongsTo nvarchar(255) = NULL, 
	@includeInSurvey nvarchar(255) = NULL,
	@personEUA nvarchar(255) = NULL,
	@personRole nvarchar(255) = NULL,
	@Outputjson nvarchar(max) OUT

AS

BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		SET @Outputjson = 
		(Select DISTINCT
            a.[Sparx System GUID] AS [id],
            ISNULL(CAST(a.[nextVersionID] AS NVARCHAR(30)),'') as nextVersionId,
            ISNULL(CAST(a.[previousVersionID] AS NVARCHAR(30)),'') as previousVersionId,
            a.[Sparx System GUID] as ictObjectId,
            ISNULL(CAST(a.[CMS UUID] AS NVARCHAR(40)),'') as uuid,
            a.[System Name] AS [name],
            a.Description AS [description],
            ISNULL(CAST(a.VERSION AS NVARCHAR(30)),'') AS [version], 
            ISNULL(a.[Acronym],'') AS [acronym], 
            ISNULL(CAST(a.[Object State] AS NVARCHAR(30)),'') as [objectState] ,
            ISNULL(CAST(a.STATUS AS NVARCHAR(30)),'') AS [status],
            ISNULL(CAST(a.[belongsTo] AS NVARCHAR(40)),'') AS [belongsTo],
            ISNULL(a.[businessOwnerOrg],'') AS [businessOwnerOrg],
            ISNULL(a.[Business Owner Organization Component],'') AS [businessOwnerComp],
            ISNULL(a.[System Maintainer Organization],'') AS [systemMaintainerOrg], 
            ISNULL(a.[System Maintainer Organization Component],'') AS [systemMaintainerComp], 
            --ISNULL(a.[PersonEUA],'') AS [Person EUA],
            --ISNULL(a.[PersonFirstName],'') AS [Person First Name],
            --ISNULL(a.[PersonLastName],'') AS [Person Last Name],
            --ISNULL(a.[Role],'') AS [Person Role],
            --ISNULL(a.[RoleId],'') AS [Person Role GUID]
			ISNULL(a.[ATO Effective Date],'') AS [ATO Effective Date],
			ISNULL(a.[ATO Expiration Date],'') AS [ATO Expiration Date]

		FROM [Sparx_Support].[CEDAR_API].[Sparx_EASi_System_UserRole_Tbl] a
		WHERE	(@objectState is null or (@objectState is not null and @objectState = a.[Object State]))
			AND (@status is null or (@status is not null and @status = a.STATUS))
			AND (@version is null or (@version is not null and @version = a.VERSION))
			AND (@belongsTo is null or (@belongsTo is not null and @belongsTo = a.[belongsTo]))
			AND (@includeInSurvey is null or ((@includeInSurvey = 'TRUE' and @includeInSurvey = a.[Current System Survey]) or (@includeInSurvey = 'FALSE' and @includeInSurvey = a.[Current System Survey])))
			AND (@personEUA is null or (@personEUA is not null and @personEUA = a.[PersonEUA]))
			AND (@personRole is null or (@personRole is not null and @personRole = a.[RoleId]))
		FOR JSON PATH, ROOT('ResultSet')
		)


	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;











