






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Get_SystemList
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/08/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Get the SystemList attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 * DECLARE @RC INT;
 * DECLARE @Outputjson nvarchar(max);
 * 
 * EXECUTE @RC = [dbo].[SP_Get_SystemCensusList] 
 * 			@Outputjson
 * 
 * 
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Get_SystemList
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 10/31/2023	Aditya Sharma	1312		Fixed Status logic when some pages are passed and others are Not Started
 * 08/13/2024	Aditya Sharma				Change the Survey Indicator
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Get_SystemCensusList] 
--Add the parameters for the stored procedure here

	@Outputjson nvarchar(max) OUT

AS

BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		SET @Outputjson = 
		(Select
            o.[ea_guid] AS [id],
            '' as nextVersionId,
            '' as previousVersionId,
            o.[ea_guid] as ictObjectId,
            '' as uuid,
            o.[Name] AS [name],
            o.Note AS [description],
            '25' AS [version], 
            ISNULL(opa.Value,'') AS [acronym], 
            'Active' as [objectState] ,
            '' AS [status],
            '' AS [belongsTo],
            ISNULL(bo.[Organization Name],'') AS [businessOwnerOrg],
            ISNULL(bo.[Organization Component],'') AS [businessOwnerOrgComp],
            ISNULL(sm.[Organization Name],'') AS [systemMaintainerOrg], 
            ISNULL(sm.[Organization Component],'') AS [systemMaintainerOrgComp], 
			ISNULL(scq.[Connection GUID],'') AS qaReviewerAssignmentId,
			ISNULL(pq.[First Name],'') AS qaReviewerFirstName,
			ISNULL(pq.[Last Name],'') AS qaReviewerLastName,
			ISNULL(pq.[User Name],'') AS qaReviewerUserName,
			ISNULL(scd.[Connection GUID],'') AS daReviewerAssignmentId,
			ISNULL(pd.[First Name],'') AS daReviewerFirstName,
			ISNULL(pd.[Last Name],'') AS daReviewerLastName,
			ISNULL(pd.[User Name],'') AS daReviewerUserName,
			censusStatus = Cen.SystemStatus,
			percentComplete = Cen.Complete

		FROM [SparxDB].[dbo].[t_object] o
		--join [SparxDB].[dbo].[t_objectproperties] op on o.Object_ID = op.Object_ID
		join [SparxDB].[dbo].[t_objectproperties] opc on o.Object_ID = opc.Object_ID and opc.Property = 'Current System Survey' and opc.Value = 'TRUE'
		join [SparxDB].[dbo].[t_objectproperties] opa on o.Object_ID = opa.Object_ID and opa.Property = 'Acronym'
		left join dbo.Sparx_System_BusinessOwner_Organization bo on bo.[Sparx System GUID] = o.[ea_guid]
		left join dbo.Sparx_System_Maintainer_Organization sm on sm.[Sparx System GUID] = o.[ea_guid]
		left join dbo.Sparx_System_Contact scq on scq.[Sparx System GUID] = o.[ea_guid] and scq.[Role Name] IN ('QA Reviewer')
		left join dbo.Sparx_Person pq on pq.[Sparx Person GUID] = scq.[Sparx Person GUID]
		left join dbo.Sparx_System_Contact scd on scd.[Sparx System GUID] = o.[ea_guid] and scd.[Role Name] IN ('DA Reviewer')
		left join dbo.Sparx_Person pd on pd.[Sparx Person GUID] = scd.[Sparx Person GUID]

		left join (SELECT [System_ID]
						  ,SystemStatus =	CASE 
												WHEN	MAX(
														CASE	WHEN [STATUS] = 'Issues Found' THEN 5
																WHEN [STATUS] = 'Ready to Submit' THEN 4
																WHEN [STATUS] = 'Started' THEN 3
																WHEN [STATUS] = 'Not Started' THEN 2
																WHEN [STATUS] = 'Passed' THEN 1
														END
														) = 2
														AND
														MIN(
														CASE	WHEN [STATUS] = 'Issues Found' THEN 5
																WHEN [STATUS] = 'Ready to Submit' THEN 4
																WHEN [STATUS] = 'Started' THEN 3
																WHEN [STATUS] = 'Not Started' THEN 2
																WHEN [STATUS] = 'Passed' THEN 1
														END
														) = 1 
														THEN 'Started'
												ELSE														
													CASE	MAX(
														CASE	WHEN [STATUS] = 'Issues Found' THEN 5
																WHEN [STATUS] = 'Ready to Submit' THEN 4
																WHEN [STATUS] = 'Started' THEN 3
																WHEN [STATUS] = 'Not Started' THEN 2
																WHEN [STATUS] = 'Passed' THEN 1
														END
														)
													WHEN 5 THEN 'Issues Found'
													WHEN 4 THEN 'Ready to Submit'
													WHEN 3 THEN 'Started'
													WHEN 2 THEN 'Not Started'
													WHEN 1 THEN 'Passed'
												END
											END
						  ,Complete = CAST(CASE WHEN (11 * SUM(CASE WHEN [STATUS] = 'Passed' THEN 1 ELSE 0 END)) = 99 THEN 100 ELSE CAST(ROUND(11.11 * SUM(CASE WHEN [STATUS] = 'Passed' THEN 1 ELSE 0 END),0) AS INT) END AS NVARCHAR(3))+'%'
					FROM [CEDAR_Support].[System_Census].[SYSTEM_SURVEY_PAGE_STATUS]					
					GROUP BY [System_ID]
					) Cen on o.[ea_guid] = Cen.System_ID
					WHERE o.Package_ID  IN (491,698,1121)

		FOR JSON PATH, ROOT('ResultSet')
		)

--Select @Outputjson;

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;










