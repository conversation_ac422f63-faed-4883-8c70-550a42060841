












/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_CEDAR_Intake_Request
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   05/15/2025
 *
 * DESCRIPTION/PURPOSE:
 *	Insert a Intake Request in CEDAR_Support DB
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_CEDAR_Intake_Request] 
			 	 @CLIENT_ID
				,@CLIENT_NAME
				,@REQUEST_TYPE
				,@REQUEST_TYPE_SCHEMA
				,@REQUEST_CLIENT_STATUS
				,@REQUEST_CEDAR_STATUS
				,@REQUEST_DATA_FORMAT
				,@REQUEST_DATA
				,@CLIENT_CREATED_DATE
				,@CLIENT_LAST_UPDATED_DATE
				,@CLIENT_VERSION
				,@ID OUT

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_CEDAR_Intake_Request
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_CEDAR_Intake_Request] 
--Add the parameters for the stored procedure here
				 @CLIENT_ID nvarchar(255) = NULL
				,@CLIENT_NAME nvarchar(255) = NULL
				,@REQUEST_TYPE nvarchar(50) = NULL
				,@REQUEST_TYPE_SCHEMA nvarchar(255) = NULL
				,@REQUEST_CLIENT_STATUS nvarchar(255) = NULL
				,@REQUEST_CEDAR_STATUS nvarchar(50) = NULL
				,@REQUEST_DATA_FORMAT nvarchar(50) = NULL
				,@REQUEST_DATA nvarchar(max) = NULL
				,@CLIENT_CREATED_DATE datetime = NULL
				,@CLIENT_LAST_UPDATED_DATE datetime = NULL
				,@CLIENT_VERSION nvarchar(50) = NULL
				,@ID INT OUT



AS

DECLARE @Inserted TABLE (New_Intake_Request_ID INT);

PRINT 'Inside Insert CEDAR_Intake_Request';

IF len(@CLIENT_ID) > 0 
BEGIN

PRINT '@CLIENT_ID = '+@CLIENT_ID

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

PRINT 'Before Insert Statement';

		INSERT INTO [CEDAR_Support].[CEDAR_API].[INTAKE_REQUEST]
				   (
				    [CLIENT_ID]
				   ,[CLIENT_NAME]
				   ,[REQUEST_TYPE]
				   ,[REQUEST_TYPE_SCHEMA]
				   ,[REQUEST_CLIENT_STATUS]
				   ,[REQUEST_CEDAR_STATUS]
				   ,[REQUEST_DATA_FORMAT]
				   ,[REQUEST_DATA]
				   ,[CLIENT_CREATED_DATE]
				   ,[CLIENT_LAST_UPDATED_DATE]
				   ,[CREATED_DATE]
				   ,[LAST_UPDATED_DATE]
				   ,[CLIENT_VERSION]
				   )
			OUTPUT INSERTED.INTAKE_REQUEST_ID INTO @Inserted
			VALUES
				(
				   @CLIENT_ID
				  ,@CLIENT_NAME
				  ,@REQUEST_TYPE
				  ,@REQUEST_TYPE_SCHEMA
				  ,@REQUEST_CLIENT_STATUS
				  ,@REQUEST_CEDAR_STATUS
				  ,@REQUEST_DATA_FORMAT
				  ,@REQUEST_DATA
				  ,@CLIENT_CREATED_DATE
				  ,@CLIENT_LAST_UPDATED_DATE
				  ,GETDATE()
				  ,GETDATE()
				  ,@CLIENT_VERSION
				);

PRINT 'After Insert Statement';

--Select * from @Inserted;

PRINT 'After Selecting @Inserted Statement';

Select @ID = New_Intake_Request_ID from (SELECT TOP 1 * from @Inserted) t;
		
PRINT '@New_ID set as:'+ CAST(@ID as nvarchar);		


	END TRY
	BEGIN CATCH

PRINT 'Inside Insert CEDAR_Intake_Request Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;
















