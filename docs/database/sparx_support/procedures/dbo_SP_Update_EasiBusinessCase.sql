




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_EasiBusinessCase
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theEasiBusinessCase attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_EasiBusinessCase] 
			 @Name
			,@Description
			,@A_Number
			,@ArchivedAt
			,@BenefitofEffort
			,@BUCKETEQUALALLOCATION
			,@BusinessCaseID
			,@BusinessOwner
			,@CreationDate
			,@CreationUser
			,@CurrentSolutionSummary
			,@EndDate
			,@ExternalID
			,@EXTERNAL_STATUS
			,@LastUpdate
			,@LastUpdateUser
			,@OrganizationPriorityAlignment
			,@RequestorID
			,@RequestorName
			,@RequestorPhoneNumber
			,@StartDate
			,@SuccessIndicators
			,@CollaborationNeeded
			,@ResponseToGRTFeedback
			,@GUID

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_EasiBusinessCase
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 02/11/2025	Aditya Sharma				Adjust lengths of inputs for Notes fields
 * 04/18/2025	Aditya Sharma				Added New Fields
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_EasiBusinessCase] 
--Add the parameters for the stored procedure here
	@Name nvarchar(255) = NULL, 
	@Description nvarchar(max) = NULL, 
	@A_Number nvarchar(255) = NULL, 
	@ArchivedAt nvarchar(255) = NULL, 
	@BenefitofEffort nvarchar(max) = NULL, 
	@BUCKETEQUALALLOCATION nvarchar(255) = NULL, 
	@BusinessCaseID nvarchar(255) = NULL, 
	@BusinessOwner nvarchar(255) = NULL, 
	@CreationDate nvarchar(255) = NULL, 
	@CreationUser nvarchar(255) = NULL, 
	@CurrentSolutionSummary nvarchar(max) = NULL, 
	@EndDate nvarchar(255) = NULL, 
	@ExternalID nvarchar(255) = NULL, 
	@EXTERNAL_STATUS nvarchar(255) = NULL, 
	@LastUpdate nvarchar(255) = NULL, 
	@LastUpdateUser nvarchar(255) = NULL, 
	@OrganizationPriorityAlignment nvarchar(max) = NULL, 
	@RequestorID nvarchar(255) = NULL, 
	@RequestorName nvarchar(255) = NULL, 
	@RequestorPhoneNumber nvarchar(255) = NULL, 
	@StartDate nvarchar(255) = NULL, 
	@SuccessIndicators nvarchar(max) = NULL, 
	@CollaborationNeeded nvarchar(max) = NULL, 
	@ResponseToGRTFeedback nvarchar(max) = NULL,
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update EasiBusinessCase';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_object SET Name = @Name, Note = @Description
		WHERE t_object.ea_guid = @GUID;


		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'A_Number' THEN ISNULL(@A_Number,Value)
				WHEN Property = 'Archived At' THEN ISNULL(@ArchivedAt,Value)				
				WHEN Property = 'BUCKETEQUALALLOCATION' THEN ISNULL(@BUCKETEQUALALLOCATION,Value)
				WHEN Property = 'Business Case ID' THEN ISNULL(@BusinessCaseID,Value)
				WHEN Property = 'Business Owner' THEN ISNULL(@BusinessOwner,Value)
				WHEN Property = 'Creation Date' THEN ISNULL(@CreationDate,Value)
				WHEN Property = 'Creation User' THEN ISNULL(@CreationUser,Value)
				WHEN Property = 'End Date' THEN ISNULL(@EndDate,Value)
				WHEN Property = 'External ID' THEN ISNULL(@ExternalID,Value)
				WHEN Property = 'EXTERNAL_STATUS' THEN ISNULL(@EXTERNAL_STATUS,Value)
				WHEN Property = 'Last Update' THEN ISNULL(@LastUpdate,Value)
				WHEN Property = 'Last Update User' THEN ISNULL(@LastUpdateUser,Value)
				WHEN Property = 'Requestor ID' THEN ISNULL(@RequestorID,Value)
				WHEN Property = 'Requestor Name' THEN ISNULL(@RequestorName,Value)
				WHEN Property = 'Requestor Phone Number' THEN ISNULL(@RequestorPhoneNumber,Value)
				WHEN Property = 'Start Date' THEN ISNULL(@StartDate,Value)

				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Easi Business Case';


		UPDATE SparxDB.dbo.t_objectproperties SET Notes = 
		CASE	WHEN Property = 'Benefit of Effort' THEN ISNULL(@BenefitofEffort,Notes)
				WHEN Property = 'Current Solution Summary' THEN ISNULL(@CurrentSolutionSummary,Notes)
				WHEN Property = 'Organization Priority Alignment' THEN ISNULL(@OrganizationPriorityAlignment,Notes)
				WHEN Property = 'Success Indicators' THEN ISNULL(@SuccessIndicators,Notes)
				WHEN Property = 'Collaboration Needed' THEN ISNULL(@CollaborationNeeded,Notes)
				WHEN Property = 'Response To GRT Feedback' THEN ISNULL(@ResponseToGRTFeedback,Notes)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Easi Business Case';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Easi Business Case'
		and Value = '';

PRINT 'End of Update EasiBusinessCase';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update EasiBusinessCase Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;










