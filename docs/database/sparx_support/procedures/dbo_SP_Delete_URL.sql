










/*********************************************************************************************
 * PROCEDURE_NAME: SP_Delete_URL
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   09/28/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theURL attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *  DECLARE @RC INT;
 *	EXECUTE @RC = [dbo].[SP_Delete_URL] 
			 @GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Delete_URL
 *
 * UPDATE HISTORY:
 * ---------------------------------------------------------------------------------------------------------------------------
 * Date			Changed By			Issue/Bug								Description
 *
 *
 * ----------------------------------------------------------------------------------------------------------------------------
 ******************************************************************************************************************************/
CREATE    PROCEDURE [dbo].[SP_Delete_URL] 
--Add the parameters for the stored procedure here	 
	@GUID nvarchar(60) = ''  --Use [dbo].[Sparx_System_URL].[Sparx URL GUID] 

AS

DECLARE @URL_ObjectId INT;
DECLARE @URL_ConnectionGUID nvarchar(60) = '';

PRINT 'Inside Update URL';
PRINT 'GUID: ' +@GUID ;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		PRINT 'Before Update'

		Select @URL_ObjectId = Object_ID
		  from SparxDB.dbo.t_object
		 where ea_guid = @GUID
		 and Stereotype = 'System URL';

		Select @URL_ConnectionGUID = ea_guid
		  from SparxDB.dbo.t_connector
		 where End_Object_ID = @URL_ObjectId;

		DELETE FROM SparxDB.dbo.t_xref 
		  WHERE Client = @URL_ConnectionGUID;

		DELETE FROM SparxDB.dbo.t_connector
		  WHERE ea_guid = @URL_ConnectionGUID;


		PRINT 'End of Delete URL';


	END TRY


	BEGIN CATCH
	
		PRINT 'Inside Delete URL Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);

	END CATCH

END;














