








/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemContract_json
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/09/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemContract attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemContract_json] 
 			 @jsonInput,
			 @jsonOutput 
 *
 *
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemContract_json
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemContract_json] 
--Add the parameters for the stored procedure here
	@jsonInput nvarchar(max),
	@jsonOutput nvarchar(max) OUT 

AS

DECLARE 

	@GUID nvarchar(60) = '',
	@systemId nvarchar(255) = '', 
	@ContractId nvarchar(255) = '', 
	@IsDeliveryOrg nvarchar(255) = '', 
	@RowNum INT = 0,
	@RC INT = 0

PRINT 'Inside Insert SystemContract';

DECLARE @New_ObjectID INT = 0;

IF len(@jsonInput) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

-- Do the System Contract addition 

--Select 'Input :'+@jsonInput;

PRINT 'Declare Cursor';
		
	
		DECLARE db_add_cursor CURSOR FOR
		SELECT s.[Id], s.[SystemGUID], s.[ContractID], s.[IsDeliveryOrg]
		FROM  OPENJSON (@jsonInput,'$.Objects')  
		WITH (   
						 [Id]	int  
						,[SystemGUID]	nvarchar(255) '$.Values.architectureelement'	
						,[ContractID]	nvarchar(255) '$.Values.contract'	
						,[IsDeliveryOrg] nvarchar(255) '$.Values.cms_application_delivery_org'	
			) s; 

PRINT 'Before Open';

		OPEN db_add_cursor;

PRINT 'After Open';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_cursor INTO 
			@RowNum, @systemId, @ContractId, @IsDeliveryOrg;

		SET @jsonOutput = '{"NewObjects": {';

PRINT '1st Fetch';

		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			
			-- Call role insert
			EXECUTE @RC = [dbo].[SP_Insert_SystemContract_Reln] 
			 	  @systemId
				 ,@ContractId
				 ,@IsDeliveryOrg
				 ,@GUID OUT

				SET @jsonOutput = @jsonOutput +'"GUID": "'+ @GUID+'",' 

			-- Fetch next record
			FETCH NEXT FROM db_add_cursor INTO 
				@RowNum, @systemId, @ContractId, @IsDeliveryOrg;

PRINT 'Next Fetch';

		END 

		-- 6 - Close the cursor
		CLOSE db_add_cursor;  

		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_cursor; 

PRINT 'End of System Contract json processing';

PRINT 'Before final output creation';

PRINT @jsonOutput;

		SET @jsonoutput = LEFT(@jsonoutput, LEN(@jsonoutput)-1)+'} }';

PRINT @jsonOutput;

-- Update the Reporting Table for the System

		DELETE FROM Sparx_Support.CEDAR_API.Sparx_System_Contract_Full_Tbl where [Sparx System GUID] = @systemId;

		INSERT INTO Sparx_Support.CEDAR_API.Sparx_System_Contract_Full_Tbl
		SELECT * FROM Sparx_Support.dbo.Sparx_System_Contract_Full where [Sparx System GUID] = @systemId;


	END TRY
	BEGIN CATCH

PRINT 'In System Contract json processing exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;













