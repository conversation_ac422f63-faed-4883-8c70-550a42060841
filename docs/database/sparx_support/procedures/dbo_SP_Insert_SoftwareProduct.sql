






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SoftwareProduct
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/31/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SoftwareProduct attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SoftwareProduct] 
			 @BuildVersion
			,@Component
			,@Edition
			,@EndDate
			,@EndofSupportDate
			,@FormattedVersion
			,@Licensable
			,@Release
			,@SoftwareID
			,@StartDate
			,@TechnopediaReleaseID
			,@Version
			,@VersionGroup
			,@Name
			,@Description
			,@GUID



 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SoftwareProduct
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SoftwareProduct] 
--Add the parameters for the stored procedure here
	@BuildVersion nvarchar(255) = NULL, 
	@Component nvarchar(255) = NULL, 
	@Edition nvarchar(255) = NULL, 
	@EndDate nvarchar(255) = NULL, 
	@EndofSupportDate nvarchar(255) = NULL, 
	@FormattedVersion nvarchar(255) = NULL, 
	@Licensable nvarchar(255) = NULL, 
	@Release nvarchar(255) = NULL, 
	@SoftwareID nvarchar(255) = NULL, 
	@StartDate nvarchar(255) = NULL, 
	@TechnopediaReleaseID nvarchar(255) = NULL, 
	@Version nvarchar(255) = NULL, 
	@VersionGroup nvarchar(255) = NULL, 
	@Name nvarchar(255) = NULL, 
	@Description nvarchar(255) = NULL, 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Insert SoftwareProduct';
PRINT 'GUID = '+@GUID;

DECLARE @New_ObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		--BEGIN TRAN
		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=Software Product;FQName=CMS EA::Software Product;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'Software Product', Name = @Name, Note = @Description
		WHERE t_object.ea_guid = @GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
PRINT @New_ObjectID;

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'Build Version' as Property, @BuildVersion as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Component' as Property, @Component as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Edition' as Property, @Edition as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'End Date' as Property, @EndDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'End of Support Date' as Property, @EndofSupportDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Formatted Version' as Property, @FormattedVersion as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Licensable' as Property, @Licensable as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Release' as Property, @Release as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Software ID' as Property, @SoftwareID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Start Date' as Property, @StartDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Technopedia Release ID' as Property, @TechnopediaReleaseID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Version' as Property, @Version as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Version Group' as Property, @VersionGroup as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid  
		) t;

		Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

PRINT 'At End of Insert SoftwareProduct';

	END TRY
	BEGIN CATCH

PRINT 'In Insert SoftwareProduct Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












