


/*********************************************************************************************
 * PROCEDURE_NAME: SP_LogError
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   02/13/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SQL error encountered to Error Log 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE SP_LogError
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_LogError
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_LogError] 
AS

DECLARE @New_ObjectID INT;

BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from interfering with SELECT statements.
	SET NOCOUNT ON;


	INSERT INTO [dbo].[ErrorLog]
           ([Error_Line]
           ,[Error_Message]
           ,[Error_Number]
           ,[Error_Source]
           ,[Error_Severity]
           ,[Error_State]
           ,[Error_Date])
     VALUES
           (Error_Line()
           ,Error_Message()
           ,Error_Number()
           ,Error_Procedure()
           ,Error_Severity()
           ,Error_State()
           ,GETDATE())

END;








