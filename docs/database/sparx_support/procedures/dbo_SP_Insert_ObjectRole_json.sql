







/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_ObjectRole_json
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/03/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the ObjectRole attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_ObjectRole_json] 
 			 @jsonInput
 *
 *
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_ObjectRole_json
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 06/26/2024	Aditya Sharma				Update of reporting table and fetch from the same incorporated into the SP
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_ObjectRole_json] 
--Add the parameters for the stored procedure here
	@jsonInput nvarchar(max), 
	@jsonOutput nvarchar(max) OUTPUT

AS

DECLARE 

	@GUID nvarchar(60) = 'xxx-xxx-xxx-xxx',
	@objectId nvarchar(255) = '', 
	@roleId nvarchar(255) = '', 
	@roleTypeId nvarchar(255) = '', 
	@roleTypeName nvarchar(255) = '',
	@roleTypeDesc nvarchar(255) = '',
	@assigneeId nvarchar(255) = '',
	@assigneeUserName nvarchar(255) = '',
	@assigneeFirstName nvarchar(255) = '',
	@assigneeLastName nvarchar(255) = '',
	@assigneeEmail nvarchar(255) = '',
	@assigneePhone nvarchar(255) = '',
	@assigneeOrgId nvarchar(255) = '',
	@assigneeOrgName nvarchar(255) = '',
	@assigneeDesc nvarchar(255) = '',
	@assigneeType nvarchar(255) = '',
	@RowNum INT = 0,
	@RC INT = 0,
	@NumRec INT = 0

PRINT 'Inside Insert ObjectRole';

DECLARE @New_ObjectID INT = 0;

IF len(@jsonInput) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

-- Do the System Person Role addition 

PRINT 'Input :'+@jsonInput;

PRINT 'Declare Cursor';
		
	
		DECLARE db_add_cursor CURSOR FOR
		Select 
		[fromid], 
		System = MAX(case when property = 'object' THEN toref ELSE NULL end),
		Person = MAX(case when property = 'responsible' THEN toref ELSE NULL end),
		RoleType = MAX(case when property = 'roletype' THEN toref ELSE NULL end)
		from 
			(
			SELECT [fromid], [property], toref, assigneeType FROM  
				OPENJSON (@jsonInput,'$.Relations')  
			WITH (   
							[fromid]	int,  
							[property]	nvarchar(255),  
							toref		nvarchar(255),
							assigneeType nvarchar(255)
				) --where assigneeType = 'person'
			) t
		group by [fromid];

PRINT 'Before Open';

		OPEN db_add_cursor;

PRINT 'After Open';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_cursor INTO 
			@RowNum, @objectId, @assigneeId, @roleTypeId;

PRINT '1st Fetch';

		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			
			-- Call role insert
			EXECUTE @RC = [dbo].[SP_Insert_SystemPersonRole_Reln] 
			 	@objectId, 
				@roleTypeId, 
				@assigneeId;

			-- Fetch next record
			FETCH NEXT FROM db_add_cursor INTO 
				@RowNum, @objectId, @assigneeId, @roleTypeId;  

PRINT 'Next Fetch';

		END 

		SELECT @NumRec = Count(1)
		FROM [dbo].[Sparx_System_Contact] sc
		WHERE sc.[Sparx System GUID] = @objectId;

PRINT @NumRec;

		-- 6 - Close the cursor
		CLOSE db_add_cursor;  

		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_cursor; 


-- ===================================================================================

-- Do the Reporting table update

		EXECUTE @RC = [dbo].[SP_Update_System_Contact_Tbl] 
			 	@objectId



-- ===================================================================================
/*
-- Do the System Org Role addition 

Select 'Input :'+@jsonInput

		DECLARE db_orgadd_cursor CURSOR FOR 
		SELECT 
			objectId, roleId, roleTypeId, roleTypeName,	roleTypeDesc,
			assigneeId,assigneeUserName,assigneeFirstName,assigneeLastName,assigneeEmail,assigneePhone,
			assigneeOrgId,assigneeOrgName,assigneeDesc,assigneeType
		FROM  
		 OPENJSON ( @jsonInput,'$.Roles' )  
		WITH (   
					objectId nvarchar(255),  
					roleId nvarchar(255),  
					roleTypeId nvarchar(255),  
					roleTypeName nvarchar(255),
					roleTypeDesc nvarchar(255),
					assigneeId nvarchar(255),
					assigneeUserName nvarchar(255),
					assigneeFirstName nvarchar(255),
					assigneeLastName nvarchar(255),
					assigneeEmail nvarchar(255),
					assigneePhone nvarchar(255),
					assigneeOrgId nvarchar(255),
					assigneeOrgName nvarchar(255),
					assigneeDesc nvarchar(255),
					assigneeType nvarchar(255)
		 ) 
		 where assigneeType = 'organization';

		OPEN db_orgadd_cursor;

Select 'Open';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_orgadd_cursor INTO 
			@objectId, @roleId, @roleTypeId, @roleTypeName,	@roleTypeDesc,
			@assigneeId,@assigneeUserName,@assigneeFirstName,@assigneeLastName,@assigneeEmail,@assigneePhone,
			@assigneeOrgId,@assigneeOrgName,@assigneeDesc,@assigneeType;

Select '1st Fetch';

		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			
			--SET @RoleCount = @RoleCount+1;

			-- Call role insert
			EXECUTE @RC = [dbo].[SP_Insert_SystemOrgRole_Reln] 
			 	@objectId, 
				@roleTypeId, 
				@assigneeOrgId,
				@roleTypeName;

			-- Fetch next record
			FETCH NEXT FROM db_orgadd_cursor INTO 
				@objectId, @roleId, @roleTypeId, @roleTypeName,	@roleTypeDesc,
				@assigneeId,@assigneeUserName,@assigneeFirstName,@assigneeLastName,@assigneeEmail,@assigneePhone,
				@assigneeOrgId,@assigneeOrgName,@assigneeDesc,@assigneeType;  

Select 'Next Fetch';

		END 

		-- 6 - Close the cursor
		CLOSE db_orgadd_cursor  

		-- 7 - Deallocate the cursor
		DEALLOCATE db_orgadd_cursor 

*/
PRINT 'Before output creation';


		SET @jsonOutput = '{"NewObjects": {';

		--Select @jsonOutput = @jsonOutput +'"'+CAST(i.fromid AS NVARCHAR(3))+'": "'+ o.roleId+'",' 
		Select @jsonOutput = @jsonOutput +'"GUID": "'+ o.roleId+'",' 
		from
		(
		Select 
				[fromid], 
				System = MAX(case when property = 'object' THEN toref ELSE NULL end),
				Person = MAX(case when property = 'responsible' THEN toref ELSE NULL end),
				RoleType = MAX(case when property = 'roletype' THEN toref ELSE NULL end)
				from 
					(
					SELECT [fromid], [property], toref, assigneeType FROM  
						OPENJSON (@jsonInput,'$.Relations')  
					WITH (   
									[fromid]	int,  
									[property]	nvarchar(255),  
									toref		nvarchar(255),
									assigneeType nvarchar(255)
						) --where assigneeType = 'person'
					) t
				group by [fromid]
		) i
		JOIN
		(
			SELECT 
				 objectId = sc.[Sparx System GUID]
				,roleId = sc.[Connection GUID]
				,roleTypeId = sc.[Sparx Role GUID]
				,assigneeId = sc.[Sparx Person GUID]
-- Replace View by Reporting table
			--FROM [dbo].[Sparx_System_Contact] sc
			FROM [CEDAR_API].[Sparx_System_Contact_Tbl] sc
		) o  ON o.objectId = i.System and o.assigneeId = i.Person and o.roleTypeId = i.RoleType
		order by i.fromid;

		SET @jsonoutput = LEFT(@jsonoutput, LEN(@jsonoutput)-1)+'},"Count":'+CAST(@NumRec AS nvarchar(3))+'}';

PRINT @jsonOutput;
				
	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












