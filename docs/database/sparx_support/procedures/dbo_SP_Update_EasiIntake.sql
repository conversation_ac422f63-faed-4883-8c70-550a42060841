




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_EasiIntake
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/01/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the EasiIntake attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_EasiIntake] 
			 @AdminLead
			,@ArchiveDate
			,@BusinessOwner
			,@BusinessOwnerComponent
			,@ContractVehicle
			,@ContractNumber
			,@Contractor
			,@DecidedAt
			,@DecisionNextSteps
			,@EACollaborator
			,@EASupportRequested
			,@EASiIntakeUUID
			,@ExistingContract
			,@ExistingFunding
			,@ExpectedIncreaseAmount
			,@ExpectingCostIncrease
			,@ExternalStatus
			,@FundingNumber
			,@FundingSource
			,@GRBMeetingDate
			,@GRTMeetingDate
			,@InformationSystemSecurityOfficer
			,@LifeCycleCostBaseline
			,@LifeCycleID
			,@LifeCycleExpiresAt
			,@LifeCycleScope
			,@OITCollaborator
			,@POPEndDate
			,@POPStartDate
			,@ProcessStatus
			,@ProductManager
			,@ProductManagerComponent
			,@ProjectAcronym
			,@RejectionReason
			,@RequestorEmail
			,@RequestorComponent
			,@RequestorName
			,@RequestorEUAID
			,@Solution
			,@ProjectStatus
			,@SubmittedAt
			,@TRBCollaborator
			,@description
			,@hasUIChanges
			,@Name
			,@CreationDate
			,@LastUpdateDate
			,@UsingSoftware
			,@AcquisitionMethods
			,@UsesAiTech
			,@CurrentAnnualSpending
			,@CurrentAnnualSpendingITPortion
			,@PlannedYearOneSpending
			,@PlannedYearOneSpendingITPortion
			,@ScheduledProductionDate
			,@GUID
 
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_EasiIntake
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 11/03/2023	Aditya Sharma				Added New Field hasUIChanges to the processing
 * 10/23/2024	Aditya Sharma				Added Name to the list of fields to Update in t_object.Name
 * 11/21/2024	Aditya Sharma				Added Client Creation and Last Update Date to the fields
 * 12/13/2024	Aditya Sharma				Changed lengths of longer fields
 * 04/18/2025	Aditya Sharma				Added New fields
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_EasiIntake] 
--Add the parameters for the stored procedure here
	@AdminLead nvarchar(255) = NULL,
	@ArchiveDate nvarchar(255) = NULL,
	@BusinessOwner nvarchar(255) = NULL,
	@BusinessOwnerComponent nvarchar(255) = NULL,
	@ContractVehicle nvarchar(255) = NULL,
	@ContractNumber nvarchar(255) = NULL,
	@Contractor nvarchar(255) = NULL,
	@DecidedAt nvarchar(255) = NULL,
	@DecisionNextSteps nvarchar(max) = NULL,
	@EACollaborator nvarchar(255) = NULL,
	@EASupportRequested nvarchar(255) = NULL,
	@EASiIntakeUUID nvarchar(255) = NULL,
	@ExistingContract nvarchar(255) = NULL,
	@ExistingFunding nvarchar(255) = NULL,
	@ExpectedIncreaseAmount nvarchar(255) = NULL,
	@ExpectingCostIncrease nvarchar(255) = NULL,
	@ExternalStatus nvarchar(255) = NULL,
	@FundingNumber nvarchar(255) = NULL,
	@FundingSource nvarchar(255) = NULL,
	@GRBMeetingDate nvarchar(255) = NULL,
	@GRTMeetingDate nvarchar(255) = NULL,
	@InformationSystemSecurityOfficer nvarchar(255) = NULL,
	@LifeCycleCostBaseline nvarchar(255) = NULL,
	@LifeCycleID nvarchar(255) = NULL,
	@LifeCycleExpiresAt nvarchar(255) = NULL,
	@LifeCycleScope nvarchar(max) = NULL,
	@OITCollaborator nvarchar(255) = NULL,
	@POPEndDate nvarchar(255) = NULL,
	@POPStartDate nvarchar(255) = NULL,
	@ProcessStatus nvarchar(255) = NULL,
	@ProductManager nvarchar(255) = NULL,
	@ProductManagerComponent nvarchar(255) = NULL,
	@ProjectAcronym nvarchar(255) = NULL,
	@RejectionReason nvarchar(255) = NULL,
	@RequestorEmail nvarchar(255) = NULL,
	@RequestorComponent nvarchar(255) = NULL,
	@RequestorName nvarchar(255) = NULL,
	@RequestorEUAID nvarchar(255) = NULL,
	@Solution nvarchar(max) = NULL,
	@ProjectStatus nvarchar(255) = NULL,
	@SubmittedAt nvarchar(255) = NULL,
	@TRBCollaborator nvarchar(255) = NULL,
	@description  nvarchar(max) = NULL,
	@hasUIChanges nvarchar(255) = NULL,
	@Name  nvarchar(255) = NULL,
	@CreationDate nvarchar(255) = NULL,
	@LastUpdateDate nvarchar(255) = NULL,

	@UsingSoftware nvarchar(255) = NULL,
	@AcquisitionMethods nvarchar(max) = NULL,
	@UsesAiTech nvarchar(255) = NULL,
	@CurrentAnnualSpending nvarchar(255) = NULL,
	@CurrentAnnualSpendingITPortion nvarchar(255) = NULL,
	@PlannedYearOneSpending nvarchar(255) = NULL,
	@PlannedYearOneSpendingITPortion nvarchar(255) = NULL,
	@ScheduledProductionDate nvarchar(255) = NULL,

	@GUID nvarchar(60) = ''


AS

PRINT 'Inside Update EasiIntake';

BEGIN
	BEGIN TRY

		IF len(ISNULL(@GUID,'')) = 0 
			THROW 50001,'Input GUID is empty',1;


		IF len(@GUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;


PRINT 'Before Update';
PRINT 'GUID = '+@GUID;

				UPDATE SparxDB.dbo.t_object set Note = ISNULL(@description, Note), Name = ISNULL(@Name, Name)
				WHERE t_object.ea_guid = @GUID;

				UPDATE SparxDB.dbo.t_objectproperties SET Value = 
				CASE	WHEN Property = 'Admin Lead' THEN @AdminLead
						WHEN Property = 'Archive Date' THEN @ArchiveDate
						WHEN Property = 'Business Owner' THEN @BusinessOwner
						WHEN Property = 'Business Owner Component' THEN @BusinessOwnerComponent
						WHEN Property = 'Contract Vehicle' THEN @ContractVehicle
						WHEN Property = 'Contract Number' THEN @ContractNumber
						WHEN Property = 'Contractor' THEN @Contractor
						WHEN Property = 'Decided At' THEN @DecidedAt
						WHEN Property = 'EA Collaborator' THEN @EACollaborator
						WHEN Property = 'EA Support Requested' THEN @EASupportRequested
						WHEN Property = 'EASi Intake UUID' THEN @EASiIntakeUUID
						WHEN Property = 'Existing Contract' THEN @ExistingContract
						WHEN Property = 'Existing Funding' THEN @ExistingFunding
						WHEN Property = 'Expected Increase Amount' THEN @ExpectedIncreaseAmount
						WHEN Property = 'Expecting Cost Increase' THEN @ExpectingCostIncrease
						WHEN Property = 'External Status' THEN @ExternalStatus
						WHEN Property = 'Funding Number' THEN @FundingNumber
						WHEN Property = 'Funding Source' THEN @FundingSource
						WHEN Property = 'GRB Meeting Date' THEN @GRBMeetingDate
						WHEN Property = 'GRT Meeting Date' THEN @GRTMeetingDate
						WHEN Property = 'Information System Security Officer' THEN @InformationSystemSecurityOfficer
						WHEN Property = 'Life Cycle Cost Baseline' THEN @LifeCycleCostBaseline
						WHEN Property = 'Life Cycle ID' THEN @LifeCycleID
						WHEN Property = 'Life Cycle Expires At' THEN @LifeCycleExpiresAt
						WHEN Property = 'OIT Collaborator' THEN @OITCollaborator
						WHEN Property = 'POP End Date' THEN @POPEndDate
						WHEN Property = 'POP Start Date' THEN @POPStartDate
						WHEN Property = 'Process Status' THEN @ProcessStatus
						WHEN Property = 'Product Manager' THEN @ProductManager
						WHEN Property = 'Product Manager Component' THEN @ProductManagerComponent
						WHEN Property = 'Project Acronym' THEN @ProjectAcronym
						WHEN Property = 'Rejection Reason' THEN @RejectionReason
						WHEN Property = 'Requestor Email' THEN @RequestorEmail
						WHEN Property = 'Requestor Component' THEN @RequestorComponent
						WHEN Property = 'Requestor Name' THEN @RequestorName
						WHEN Property = 'Requestor EUA ID' THEN @RequestorEUAID
						WHEN Property = 'Project Status' THEN @ProjectStatus
						WHEN Property = 'Submitted At' THEN @SubmittedAt
						WHEN Property = 'TRB Collaborator' THEN @TRBCollaborator
						WHEN Property = 'Has UI Changes' THEN @hasUIChanges
						WHEN Property = 'Creation Date' THEN @CreationDate
						WHEN Property = 'Last Update' THEN @LastUpdateDate
						WHEN Property = 'Using Software' THEN @UsingSoftware
						WHEN Property = 'Uses AI Tech' THEN @UsesAiTech
						WHEN Property = 'Current Annual Spending' THEN @CurrentAnnualSpending
						WHEN Property = 'Current Annual Spending IT Portion' THEN @CurrentAnnualSpendingITPortion
						WHEN Property = 'Planned Year One Spending' THEN @PlannedYearOneSpending
						WHEN Property = 'Planned Year One Spending IT Portion' THEN @PlannedYearOneSpendingITPortion
						WHEN Property = 'Scheduled Production Date' THEN @ScheduledProductionDate
						ELSE Value
				END
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties 
					ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @GUID
				and t_object.Stereotype = 'EASi Intake';

				UPDATE SparxDB.dbo.t_objectproperties SET Notes = 
				CASE	WHEN Property = 'Solution' THEN @Solution
						WHEN Property = 'Life Cycle Scope' THEN @LifeCycleScope
						WHEN Property = 'Decision Next Steps' THEN @DecisionNextSteps
						WHEN Property = 'Acquisition Methods' THEN @AcquisitionMethods
						ELSE Notes
				END
				FROM		SparxDB.dbo.t_object 
				INNER JOIN	SparxDB.dbo.t_objectproperties 
					ON t_object.Object_ID = t_objectproperties.Object_ID
				WHERE t_object.ea_guid = @GUID
				and t_object.Stereotype = 'EASi Intake';


				--EXEC dbo.SP_LogError;

PRINT 'After Main Update';
				--UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
				--FROM		SparxDB.dbo.t_object 
				--INNER JOIN	SparxDB.dbo.t_objectproperties 
				--	ON t_object.Object_ID = t_objectproperties.Object_ID
				--WHERE t_object.ea_guid = @GUID
				--and t_object.Stereotype = 'EASi Intake'
				--and Value = '';

				--UPDATE SparxDB.dbo.t_objectproperties SET Notes = NULL
				--FROM		SparxDB.dbo.t_object 
				--INNER JOIN	SparxDB.dbo.t_objectproperties 
				--	ON t_object.Object_ID = t_objectproperties.Object_ID
				--WHERE t_object.ea_guid = @GUID
				--and t_object.Stereotype = 'EASi Intake'
				--and t_objectproperties.Property = 'Solution'
				--and t_objectproperties.Notes = '';
PRINT 'After NULL Update';

		END;

	END TRY
	BEGIN CATCH
PRINT 'Inside Update EASiIntake Error';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;






