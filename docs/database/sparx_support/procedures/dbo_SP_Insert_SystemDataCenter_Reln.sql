










/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemDataCenter_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/06/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemDataCenter attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemDataCenter_Reln] 
			 	@systemId, 
				@dataCenterType, 
				@dataCenterId,
				@ApplicationSoftwareReplicated, 
				@CCICIntegrationFlag, 
				@ContractorName, 
				@DataReplicated, 
				@Environment, 
				@FedContactOrg, 
				@HotSite, 
				@ImportLookup, 
				@IncidentResponseContact, 
				@MultiFactorAuthentication, 
				@ProductionDataUseFlag, 
				@RelationshipImpact, 
				@RelationshipStatus, 
				@SystemServerSoftwareReplicated, 
				@UtilizesVPN, 
				@WANType,
				@UsersRequiringMultifactorAuthentication,
				@OtherSpecialUsers,
				@NetworkEncryption,
				@WANTypeOther

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemDataCenter_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 08/12/2024	Aditya Sharma				Add FY25 new field: WAN Type - Other
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE   PROCEDURE [dbo].[SP_Insert_SystemDataCenter_Reln] 
--Add the parameters for the stored procedure here
	@SystemId nvarchar(255) = '', 
	@DataCenterType nvarchar(255) = '', 
	@DataCenterId nvarchar(255) = '',
	@ApplicationSoftwareReplicated nvarchar(255) = '', 
	@CCICIntegrationFlag nvarchar(255) = '', 
	@ContractorName nvarchar(255) = '', 
	@DataReplicated nvarchar(255) = '', 
	@Environment nvarchar(255) = '', 
	@FedContactOrg nvarchar(255) = '', 
	@HotSite nvarchar(255) = '', 
	@ImportLookup nvarchar(255) = '', 
	@IncidentResponseContact nvarchar(255) = '', 
	@MultiFactorAuthentication nvarchar(255) = '', 
	@ProductionDataUseFlag nvarchar(255) = '', 
	@RelationshipImpact nvarchar(255) = '', 
	@RelationshipStatus nvarchar(255) = '', 
	@SystemServerSoftwareReplicated nvarchar(255) = '', 
	@UtilizesVPN nvarchar(255) = '', 
	@WANType nvarchar(max) = '',
	@UsersRequiringMultifactorAuthentication nvarchar(max) = '',
	@OtherSpecialUsers nvarchar(255) = '',
	@NetworkEncryption nvarchar(max) = '',
	@WANTypeOther nvarchar(max) = ''


AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@DataCenter_ObjectId nvarchar(255) = '';


PRINT 'Inside Insert SystemDataCenter';
PRINT 'SystemId = '+@systemId;
PRINT 'dataCenterType = '+@dataCenterType;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @RelStatusNotes nvarchar(max) = 'Values: Future,Active,Retired,Deleted,Never Implemented  Default: Active  ';
DECLARE @RelStatusDefault nvarchar(255);
DECLARE @RelImpactNotes nvarchar(max) = 'Values: Critical,Major,Moderate,Minor,None,Unknown  Default: Moderate  ';
DECLARE @RelImpactDefault nvarchar(255);


IF len(@systemId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--BEGIN TRAN

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @SystemId;
		Select @DataCenter_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @DataCenterId;
		Select @RelStatusDefault = LTRIM(RTRIM(SUBSTRING(@RelStatusNotes,CHARINDEX('Default:',@RelStatusNotes,1)+8,LEN(@RelStatusNotes)-CHARINDEX('Default:',@RelStatusNotes,1) -7)));
		Select @RelImpactDefault = LTRIM(RTRIM(SUBSTRING(@RelImpactNotes,CHARINDEX('Default:',@RelImpactNotes,1)+8,LEN(@RelImpactNotes)-CHARINDEX('Default:',@RelImpactNotes,1) -7)));


PRINT 'Start Reln processing'
		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
		Values (@System_ObjectId+' : '+@DataCenter_ObjectId, 'Association', 'Host','Source -> Destination', @DataCenter_ObjectId, @System_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

		Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where End_Object_ID = @System_ObjectId and Start_Object_ID = @DataCenter_ObjectId 
			and Stereotype = 'Host';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Host;FQName=CMS EA::Host;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

		---insert the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Status', @RelStatusDefault, @RelStatusNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Impact', @RelImpactDefault, @RelImpactNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Application Software Replicated', @ApplicationSoftwareReplicated, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'CCIC Integration Flag', @CCICIntegrationFlag, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Contractor Name', @ContractorName, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Data Replicated', @DataReplicated, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Environment', @Environment, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Fed Contact Org', @FedContactOrg, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Hot Site', @HotSite, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'ImportLookup', @ImportLookup, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Incident Response Contact', @IncidentResponseContact, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Multi Factor Authentication', @MultiFactorAuthentication, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Production Data Use Flag', @ProductionDataUseFlag, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'System Server Software Replicated', @SystemServerSoftwareReplicated, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Utilizes VPN', @UtilizesVPN, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'WAN Type', '', @WANType, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Users Requiring Multifactor Authentication', '', @UsersRequiringMultifactorAuthentication, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Other Special Users', @OtherSpecialUsers, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Network Encryption', '', @NetworkEncryption, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'WAN Type - Other', '', @WANTypeOther, CONCAT('{',NEWID(),'}'))

		--Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		--Values (@New_ConnectorID, 'AWS Enclave', @AWSEnclave, NULL, CONCAT('{',NEWID(),'}'))

		--Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		--Values (@New_ConnectorID, 'AWS Enclave Other', @AWSEnclaveOther, NULL, CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Fed Contact Name', NULL, NULL, CONCAT('{',NEWID(),'}'))

PRINT 'Properties inserted'

		-- Check the inserts

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records

		--ROLLBACK;

	END TRY
	BEGIN CATCH

PRINT 'Inside Insert System DataCenter Reln Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;















