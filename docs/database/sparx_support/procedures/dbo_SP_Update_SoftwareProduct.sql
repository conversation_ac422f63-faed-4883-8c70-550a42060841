






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SoftwareProduct
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/10/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theSoftwareProduct attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SoftwareProduct] 
			 @BuildVersion
			,@Component
			,@Edition
			,@EndDate
			,@EndofSupportDate
			,@FormattedVersion
			,@Licensable
			,@Release
			,@SoftwareID
			,@StartDate
			,@TechnopediaReleaseID
			,@Version
			,@VersionGroup
			,@Name
			,@Description
			,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SoftwareProduct
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SoftwareProduct] 
--Add the parameters for the stored procedure here
	@BuildVersion nvarchar(255) = NULL, 
	@Component nvarchar(255) = NULL, 
	@Edition nvarchar(255) = NULL, 
	@EndDate nvarchar(255) = NULL, 
	@EndofSupportDate nvarchar(255) = NULL, 
	@FormattedVersion nvarchar(255) = NULL, 
	@Licensable nvarchar(255) = NULL, 
	@Release nvarchar(255) = NULL, 
	@SoftwareID nvarchar(255) = NULL, 
	@StartDate nvarchar(255) = NULL, 
	@TechnopediaReleaseID nvarchar(255) = NULL, 
	@Version nvarchar(255) = NULL, 
	@VersionGroup nvarchar(255) = NULL, 
	@Name nvarchar(255) = NULL,
	@Description nvarchar(255) = NULL,
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update SoftwareProduct';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_object SET Name = ISNULL(@Name,Name), Note = ISNULL(@Description,Note)
		WHERE t_object.ea_guid = @GUID;


		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'Build Version' THEN ISNULL(@BuildVersion,Value)
				WHEN Property = 'Component' THEN ISNULL(@Component,Value)
				WHEN Property = 'Edition' THEN ISNULL(@Edition,Value)
				WHEN Property = 'End Date' THEN ISNULL(@EndDate,Value)
				WHEN Property = 'End of Support Date' THEN ISNULL(@EndofSupportDate,Value)
				WHEN Property = 'Formatted Version' THEN ISNULL(@FormattedVersion,Value)
				WHEN Property = 'Licensable' THEN ISNULL(@Licensable,Value)
				WHEN Property = 'Release' THEN ISNULL(@Release,Value)
				WHEN Property = 'Software ID' THEN ISNULL(@SoftwareID,Value)
				WHEN Property = 'Start Date' THEN ISNULL(@StartDate,Value)
--				WHEN Property = 'Technopedia Release ID' THEN ISNULL(@TechnopediaReleaseID,Value) -- This should not get updated as it's the driver
				WHEN Property = 'Version' THEN ISNULL(@Version,Value)
				WHEN Property = 'Version Group' THEN ISNULL(@VersionGroup,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Software Product';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Software Product'
		and Value = '';

PRINT 'End of Update SoftwareProduct';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update SoftwareProduct Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












