












/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemEASiIntake_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   04/24/2025
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemEASiIntake attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemEASiIntake_Reln] 
			 	@SparxSystemGUID, 
				@EASiIntakeUUID,
				@GUID OUT




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemEASiIntake_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemEASiIntake_Reln] 
--Add the parameters for the stored procedure here
	@SparxSystemGUID nvarchar(255) = NULL, 
	@EASiIntakeUUID nvarchar(255) = NULL,
	@GUID nvarchar(60) OUT



AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@EASiIntake_ObjectId nvarchar(255) = '';

PRINT 'Inside Insert SystemEASiIntake Reln';

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @Existing_ConnectorGUID nvarchar(60) = NULL;
--DECLARE @tagNotes nvarchar(max) = 'Values: Most of this funding is directly and only for this system (over 80%),A large part of this funding is directly and only for this system (between 40%-80%),Only part of this funding is directly for this system (less than 40%)  ';
--DECLARE @RelStatusNotes nvarchar(max) = 'Values: Future,Active,Retired,Deleted,Never Implemented  Default: Active  ';
--DECLARE @RelStatusDefault nvarchar(255);
--DECLARE @RelImpactNotes nvarchar(max) = 'Values: Critical,Major,Moderate,Minor,None,Unknown  Default: Moderate  ';
--DECLARE @RelImpactDefault nvarchar(255);

IF len(@SparxSystemGUID) > 0 and len(@EASiIntakeUUID) > 0
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--BEGIN TRAN

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @SparxSystemGUID;
		Select @EASiIntake_ObjectId = Object_ID from SparxDB.dbo.t_object where alias = @EASiIntakeUUID;

-- Check if the reln already exists 

		Select @Existing_ConnectorGUID = [Connection GUID] from Sparx_Support.dbo.Sparx_System_EASi_Intake 
		where [Sparx Intake ID] = @EASiIntake_ObjectId and [Sparx System ID] = @System_ObjectId;

		If @Existing_ConnectorGUID is null
			BEGIN

PRINT 'Start Reln processing'
				Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction, Start_Object_ID, End_Object_ID, ea_guid)
				Values (@EASiIntake_ObjectId+' : '+@System_ObjectId, 'Association', 'Intake-System','Source -> Destination', @EASiIntake_ObjectId, @System_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

				Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where Start_Object_ID = @EASiIntake_ObjectId and End_Object_ID = @System_ObjectId
					and Stereotype = 'Intake-System';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Intake-System;FQName=CMS EA::Intake-System;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

				SET @GUID = @New_ConnectorGUID;

			END
		ELSE
			BEGIN
				SET @GUID = @Existing_ConnectorGUID;
			END

		-- Check the inserts

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		
PRINT '@Existing_ConnectorID is:'+ISNULL(@Existing_ConnectorGUID,'');		

PRINT '@New_ConnectorID set as:'+ISNULL(@New_ConnectorGUID,'');

		--ROLLBACK;

	END TRY
	BEGIN CATCH

PRINT 'Inside System EASiIntake Reln Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;
















