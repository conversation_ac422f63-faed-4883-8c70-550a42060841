



/*********************************************************************************************
 * PROCEDURE_NAME: [SP_Delete_SystemAPICategory_Reln_Census]
 *
 * AUTHOR: Sita Paturi
 * DATE:   06/13/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Delete the System-APICategory relationship in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Delete_SystemAPICategory_Reln_Census] 
			 	@SystemGUID

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.[SP_Delete_SystemAPICategory_Reln_Census]
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Delete_SystemAPICategory_Reln_Census] 
--Add the parameters for the stored procedure here
	@SystemGUID nvarchar(255) = NULL

AS

DECLARE
	--@System_ObjectId nvarchar(255) = ''
	@Connection_ID nvarchar(100) = ''


PRINT 'Inside Delete SystemAPICategoryReln';
PRINT 'SystemGUID = '+@SystemGUID;

--DECLARE @ConnectorID INT = 0;


IF len(@SystemGUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

			DECLARE db_add_cursor CURSOR FOR
				select [Connection GUID] from [Sparx_Support].[dbo].[Sparx_System_API_Category] where [Sparx System GUID] = @SystemGUID

			OPEN db_add_cursor;

				FETCH NEXT FROM db_add_cursor INTO @Connection_ID;

			PRINT '1st Fetch';

					WHILE @@FETCH_STATUS = 0  
 
					BEGIN  
		
					Delete from [SparxDB].[dbo].[t_xref] where client = @Connection_ID
					Delete from [SparxDB].[dbo].[t_connector] where ea_guid = @Connection_ID

						-- Fetch next record
				FETCH NEXT FROM db_add_cursor INTO @Connection_ID;

			PRINT 'Next Fetch';

					END 
					-- Close the cursor
					CLOSE db_add_cursor;  

					-- Deallocate the cursor
					DEALLOCATE db_add_cursor; 

	END TRY
	BEGIN CATCH

PRINT 'Inside Delete System API Category Reln Exception';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;



