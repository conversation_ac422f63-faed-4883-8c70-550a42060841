











/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemContract_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   04/14/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemContract attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemContract_Reln] 
			 	@systemId, 
				@ContractId,
				@IsDeliveryOrg,
				@GUID OUT




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemContract_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemContract_Reln] 
--Add the parameters for the stored procedure here
	@systemId nvarchar(255) = NULL, 
	@ContractId nvarchar(255) = NULL,
	@IsDeliveryOrg nvarchar(255) = NULL,
	@GUID nvarchar(60) OUT



AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@Contract_ObjectId nvarchar(255) = '';


PRINT 'Inside Insert SystemContract';

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
--DECLARE @tagNotes nvarchar(max) = 'Values: Most of this funding is directly and only for this system (over 80%),A large part of this funding is directly and only for this system (between 40%-80%),Only part of this funding is directly for this system (less than 40%)  ';
--DECLARE @RelStatusNotes nvarchar(max) = 'Values: Future,Active,Retired,Deleted,Never Implemented  Default: Active  ';
--DECLARE @RelStatusDefault nvarchar(255);
--DECLARE @RelImpactNotes nvarchar(max) = 'Values: Critical,Major,Moderate,Minor,None,Unknown  Default: Moderate  ';
--DECLARE @RelImpactDefault nvarchar(255);

IF len(@systemId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--BEGIN TRAN

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @systemId;
		Select @Contract_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @ContractId;
		--Select @RelStatusDefault = LTRIM(RTRIM(SUBSTRING(@RelStatusNotes,CHARINDEX('Default:',@RelStatusNotes,1)+8,LEN(@RelStatusNotes)-CHARINDEX('Default:',@RelStatusNotes,1) -7)));
		--Select @RelImpactDefault = LTRIM(RTRIM(SUBSTRING(@RelImpactNotes,CHARINDEX('Default:',@RelImpactNotes,1)+8,LEN(@RelImpactNotes)-CHARINDEX('Default:',@RelImpactNotes,1) -7)));


PRINT 'Start Reln processing'
		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction, Start_Object_ID, End_Object_ID, ea_guid)
		Values (@System_ObjectId+' : '+@Contract_ObjectId, 'Association', 'Develops','Unspecified', @Contract_ObjectId, @System_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

		Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where End_Object_ID = @System_ObjectId and Start_Object_ID = @Contract_ObjectId
			and Stereotype = 'Develops';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Develops;FQName=CMS EA::Develops;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

		---insert the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		--Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		--Values (@New_ConnectorID, 'Relationship Status', @RelStatusDefault, @RelStatusNotes, CONCAT('{',NEWID(),'}'));

		--Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		--Values (@New_ConnectorID, 'Relationship Impact', @RelImpactDefault, @RelImpactNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Application Delivery Org', @IsDeliveryOrg, '', CONCAT('{',NEWID(),'}'));


PRINT 'Properties inserted'

		-- Check the inserts

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records
		
		SET @GUID = @New_ConnectorGUID;

PRINT '@New_ConnectorID set as:'+@New_ConnectorGUID;		

		--ROLLBACK;

	END TRY
	BEGIN CATCH

PRINT 'Inside System Contract Reln Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;
















