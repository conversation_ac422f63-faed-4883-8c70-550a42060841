






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SubSystem
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   08/07/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SubSystem attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SubSystem] 
			 @SystemGUID
			,@Name
			,@Description
			,@Acronym
			,@GUID


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SubSystem
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SubSystem] 
--Add the parameters for the stored procedure here
	@SystemGUID nvarchar(60) = NULL,
	@Name nvarchar(255) = NULL, 
	@Description nvarchar(Max) = NULL, 
	@Acronym nvarchar(255) = NULL, 
	@GUID nvarchar(60) = NULL

AS

PRINT 'Inside Insert SubSystem';
PRINT 'GUID = '+@GUID;

DECLARE @New_ObjectID INT = 0;
DECLARE @ParentSystem_ObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		Select @ParentSystem_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @SystemGUID;

		--BEGIN TRAN
		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=System;FQName=CMS EA::System;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set Stereotype = 'System', Name = @Name, Note = @Description, ParentID = @ParentSystem_ObjectID
		WHERE t_object.ea_guid = @GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;

PRINT @New_ObjectID;

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select @New_ObjectID, t.Property, NULL, NULL, CONCAT('{',NEWID(),'}') 
		from
		(
		Select distinct op.Property
		from SparxDB.[dbo].[t_objectproperties] op
		join SparxDB.[dbo].[t_object] o on o.Object_ID = op.Object_ID
		where o.Stereotype = 'System' 
		) t;

		UPDATE SparxDB.dbo.t_objectproperties 
		SET Value = ISNULL(@Acronym,Value)
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID and t_objectproperties.Property = 'Acronym'
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'System';

		UPDATE SparxDB.dbo.t_objectproperties 
		SET Value = 'Active'
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID and t_objectproperties.Property = 'Object State'
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'System';

/*
		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		VALUES (@New_ObjectID, 'Acronym', @Acronym, '', CONCAT('{',NEWID(),'}') )

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		VALUES (@New_ObjectID, 'Planned Retirement Quarter', NULL, NULL, CONCAT('{',NEWID(),'}') )

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		VALUES (@New_ObjectID, 'Retire or Replace Date', NULL, NULL, CONCAT('{',NEWID(),'}') )

*/

		Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

PRINT 'At End of Insert SubSystem';

	END TRY
	BEGIN CATCH

PRINT 'In Insert SubSystem Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;













