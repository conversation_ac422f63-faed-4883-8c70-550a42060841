







/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_EasiBusinessCase
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the EasiBusinessCase attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_EasiBusinessCase] 
			 @Name
			,@Description
			,@A_Number
			,@ArchivedAt
			,@BenefitofEffort
			,@BUCKETEQUALALLOCATION
			,@BusinessCaseID
			,@BusinessOwner
			,@CreationDate
			,@CreationUser
			,@CurrentSolutionSummary
			,@EndDate
			,@ExternalID
			,@EXTERNAL_STATUS
			,@LastUpdate
			,@LastUpdateUser
			,@OrganizationPriorityAlignment
			,@RequestorID
			,@RequestorName
			,@RequestorPhoneNumber
			,@StartDate
			,@SuccessIndicators
			,@IntakeId
			,@CollaborationNeeded
			,@ResponseToGRTFeedback
			,@GUID



 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_EasiBusinessCase
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 02/10/2025	Aditya Sharma				Take in the IntakeId and establish reln.
 * 04/18/2025	Aditya Sharma				Added New Fields
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_EasiBusinessCase] 
--Add the parameters for the stored procedure here
	@Name nvarchar(255) = NULL, 
	@Description nvarchar(max) = NULL, 
	@A_Number nvarchar(255) = NULL, 
	@ArchivedAt nvarchar(255) = NULL, 
	@BenefitofEffort nvarchar(max) = NULL, 
	@BUCKETEQUALALLOCATION nvarchar(255) = NULL, 
	@BusinessCaseID nvarchar(255) = NULL, 
	@BusinessOwner nvarchar(255) = NULL, 
	@CreationDate nvarchar(255) = NULL, 
	@CreationUser nvarchar(255) = NULL, 
	@CurrentSolutionSummary nvarchar(max) = NULL, 
	@EndDate nvarchar(255) = NULL, 
	@ExternalID nvarchar(255) = NULL, 
	@EXTERNAL_STATUS nvarchar(255) = NULL, 
	@LastUpdate nvarchar(255) = NULL, 
	@LastUpdateUser nvarchar(255) = NULL, 
	@OrganizationPriorityAlignment nvarchar(max) = NULL, 
	@RequestorID nvarchar(255) = NULL, 
	@RequestorName nvarchar(255) = NULL, 
	@RequestorPhoneNumber nvarchar(255) = NULL, 
	@StartDate nvarchar(255) = NULL, 
	@SuccessIndicators nvarchar(max) = NULL, 
	@IntakeId nvarchar(255) = NULL, 
	@CollaborationNeeded nvarchar(max) = NULL, 
	@ResponseToGRTFeedback nvarchar(max) = NULL,
	@GUID nvarchar(60) = ''


AS


PRINT 'Inside Insert EasiBusinessCase';
PRINT 'GUID = '+@GUID;


DECLARE
	@EASi_Intake_ObjectId nvarchar(255) = '', 
	@EASi_BusinessCase_ObjectId nvarchar(255) = '',
	@New_ConnectorID INT = 0,
	@New_ConnectorGUID nvarchar(60) = '';


BEGIN

	BEGIN TRY

	DECLARE @New_ObjectID INT = 0;
	IF len(ISNULL(@GUID,'')) = 0 
		THROW 50001,'Input GUID is empty',1;

	IF len(@GUID) > 0 
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;



		PRINT @GUID;
		PRINT 'A Number :'+@A_Number
				--BEGIN TRAN
				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=EASi Business Case;FQName=CMS EA::EASi Business Case;@ENDSTEREO;', @GUID, '<none>');
		PRINT 'Before Update';
				UPDATE SparxDB.dbo.t_object set Stereotype = 'EASi Business Case', Name = @Name, Note = @Description
				WHERE t_object.ea_guid = @GUID;
		PRINT 'GUID = '+@GUID;
				SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
		PRINT @New_ObjectID
		PRINT 'Before Insert'
				INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
				Select ObjectID, Property, Val, Notes, ea_guid from
				(
					Select @New_ObjectID as ObjectID, 'A_Number' as Property, @A_Number as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Archived At' as Property, @ArchivedAt as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Benefit of Effort' as Property, '' as Val, @BenefitofEffort as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'BUCKETEQUALALLOCATION' as Property, @BUCKETEQUALALLOCATION as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Business Case ID' as Property, @BusinessCaseID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Business Owner' as Property, @BusinessOwner as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Creation Date' as Property, @CreationDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Creation User' as Property, @CreationUser as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Current Solution Summary' as Property, '' as Val, @CurrentSolutionSummary as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'End Date' as Property, @EndDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'External ID' as Property, @ExternalID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'EXTERNAL_STATUS' as Property, @EXTERNAL_STATUS as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Last Update' as Property, @LastUpdate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Last Update User' as Property, @LastUpdateUser as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Organization Priority Alignment' as Property, '' as Val, @OrganizationPriorityAlignment as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Requestor ID' as Property, @RequestorID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Requestor Name' as Property, @RequestorName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Requestor Phone Number' as Property, @RequestorPhoneNumber as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Start Date' as Property, @StartDate as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Success Indicators' as Property, '' as Val, @SuccessIndicators as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Collaboration Needed' as Property, '' as Val, @CollaborationNeeded as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
					Select @New_ObjectID as ObjectID, 'Response To GRT Feedback' as Property, '' as Val, @ResponseToGRTFeedback as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
				) t;

				--Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

				--ROLLBACK;

				-- Set the relationship to the Intake
				
				Select @EASi_Intake_ObjectId = i.[Sparx Intake ID] from Sparx_Support.dbo.Sparx_EASi_Intake i where i.[EASi Intake UUID] = @IntakeId;
				Select @EASi_BusinessCase_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
				
PRINT @EASi_Intake_ObjectId
PRINT @EASi_BusinessCase_ObjectId

				PRINT 'Start Reln processing'
				Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
				Values (@EASi_Intake_ObjectId+' : '+@EASi_BusinessCase_ObjectId, 'Association', 'Maps To','Source -> Destination', @EASi_Intake_ObjectId, @EASi_BusinessCase_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

				Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where End_Object_ID = @EASi_BusinessCase_ObjectId and Start_Object_ID = @EASi_Intake_ObjectId 
					and Stereotype = 'Maps To';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Maps To;FQName=CMS EA::Maps To;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

		END;


	END TRY
	BEGIN CATCH
PRINT 'Inside SP_Insert_EasiBusinessCase exception'
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;


