





/*********************************************************************************************
* PROCEDURE_NAME: SP_Update_BusinessOwner_Census
*
* AUTHOR: <PERSON>
* DATE:   07/16/2023
*
* DESCRIPTION/PURPOSE:
*             Update System attributes in SparxDB 
 *
* DEPENDENCY MATRIX:
* ------------------------------------------------------------------------------------------
* Item                                                                  Location                                                                                                                               Purpose
* 
 * ------------------------------------------------------------------------------------------
*
* EXAMPLE EXECUTION:
*             EXECUTE @RC = [dbo].[SP_Update_BusinessOwner_Census] 
						@SystemOwnership,
                		@SystemCostPerYear,
                		@NumberofFederalSupportFTEs,
                		@NumberofContractorSupportFTEs,
                		@NumberofDirectSystemUsers,
						@BeneficiaryInformation,
						@EditBeneficiaryInformation,
                		@HealthDisparityData,
                		@HealthDisparityData,
                		@Description,
						@SystemHasUI
						@SystemUIAccessibility
						@SystemGUID
*
* REMOVE PROC:
*             DROP PROCEDURE dbo.SP_Update_BusinessOwner_Census
*
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug		Description
 * 08/17/2023	Sita Paturi		CEDARTEST-1094	"Edit Beneficiary Information" field data should be stored in "Value" column not Notes
 * 08/31/2023	Aditya Sharma					Removed indicators and added System Ownership
 * 07/25/2024	Aditya Sharma					Added FY25 System object New Field
 * ------------------------------------------------------------------------------------------
********************************************************************************************/
CREATE     PROCEDURE .[dbo].[SP_Update_BusinessOwner_Census] 
--Add the parameters for the stored procedure here
--      @CMSOwned nvarchar(255) = NULL,
--		@ContractorOwned nvarchar(255) = NULL,
		@SystemOwnership nvarchar(255) = NULL,
        @SystemCostPerYear nvarchar(255) = NULL,
        @NumberofFederalSupportFTEs nvarchar(255) = NULL,
        @NumberofContractorSupportFTEs nvarchar(255) = NULL,
        @NumberofDirectSystemUsers nvarchar(255) = NULL,
		@BeneficiaryInformation nvarchar(max) = NULL,
		@EditBeneficiaryInformation nvarchar(max) = NULL,
        @HealthDisparityData nvarchar(255) = NULL,
        @Description nvarchar(max) = NULL,
		@SystemHasUI nvarchar(255) = NULL,
		@SystemUIAccessibility nvarchar(max) = NULL,
        @SystemGUID nvarchar(60) = ''
                
AS

PRINT 'Inside Update Business Owner';
DECLARE @RC INT = 0;

BEGIN
BEGIN TRY

                IF len(ISNULL(@SystemGUID,'')) = 0 
                                THROW 50001,'Input GUID is empty',1;


                IF len(@SystemGUID) > 0 
                BEGIN

                -- SET NOCOUNT ON added to prevent extra result sets from
                -- interfering with SELECT statements.
                                SET NOCOUNT ON;


				PRINT 'Before Update';
				--PRINT @CMSOwned;
				--PRINT @ContractorOwned;
				PRINT @SystemOwnership;
				PRINT @SystemCostPerYear ;
				PRINT @NumberofFederalSupportFTEs ;
				PRINT @NumberofContractorSupportFTEs ;
				PRINT @NumberofDirectSystemUsers ;
				PRINT @BeneficiaryInformation ;
				PRINT @EditBeneficiaryInformation ;
				PRINT @HealthDisparityData ;
				PRINT @Description ;
				PRINT @SystemHasUI ;
				PRINT @SystemUIAccessibility;
				PRINT 'GUID = '+@SystemGUID;

                                                                
                UPDATE SparxDB.dbo.t_objectproperties 
				SET Value = 
							(CASE      
								--WHEN Property = 'CMS Owned' THEN @CMSOwned
								--WHEN Property = 'Contractor Owned' THEN @ContractorOwned
								WHEN Property = 'System Ownership' THEN @SystemOwnership
								WHEN Property = 'System Cost Per Year' THEN  @SystemCostPerYear
								WHEN Property = 'Number of Federal Support FTEs' THEN @NumberofFederalSupportFTEs
								WHEN Property = 'Number of Contractor Support FTEs' THEN @NumberofContractorSupportFTEs
								WHEN Property = 'Number of Direct System Users' THEN @NumberofDirectSystemUsers
								WHEN Property = 'Health Disparity Data' THEN @HealthDisparityData
								WHEN Property = 'System Has UI' THEN @SystemHasUI
								WHEN Property = 'Edit Beneficiary Information' THEN @EditBeneficiaryInformation
								ELSE Value
                              END)
                FROM            SparxDB.dbo.t_object 
                INNER JOIN      SparxDB.dbo.t_objectproperties 
                                ON t_object.Object_ID = t_objectproperties.Object_ID
                WHERE           t_object.ea_guid = @SystemGUID
                and           t_object.Stereotype = 'System';

				PRINT 'End of first update';


                UPDATE SparxDB.dbo.t_ObjectProperties 
                SET Notes = 
							(CASE	
								WHEN t_objectproperties.[Property] ='Beneficiary Information' THEN @BeneficiaryInformation
								WHEN t_objectproperties.[Property] ='System UI Accessibility' THEN @SystemUIAccessibility
								ELSE Notes
                             END)
                FROM           SparxDB.dbo.t_object 
                INNER JOIN     SparxDB.dbo.t_objectproperties 
                                ON t_object.Object_ID = t_objectproperties.Object_ID
                WHERE t_object.ea_guid = @SystemGUID
                    and t_object.Stereotype = 'System'
                    and t_objectproperties.[Property] in ('Beneficiary Information','System UI Accessibility');
                         
				UPDATE SparxDB.dbo.t_object
				SET Note = @Description
				WHERE	t_object.ea_guid = @SystemGUID
					and t_object.Stereotype = 'System';

--PRINT 'After Main Update';
--				UPDATE SparxDB.dbo.t_objectproperties 
--				SET Value = NULL
--				FROM SparxDB.dbo.t_object 
--					INNER JOIN    SparxDB.dbo.t_objectproperties 
--						ON t_object.Object_ID = t_objectproperties.Object_ID
--				WHERE t_object.ea_guid = @SystemGUID
--					and 	t_object.Stereotype = 'System'
--					and 	Value = '';


PRINT 'After NULL Update';


       END;

END  TRY


BEGIN CATCH

PRINT 'Inside Update Business Owner Error';
-- Log the error
	EXEC dbo.SP_LogError;

    --If the transaction is in an uncommittable state, rollback the transaction
    IF (XACT_STATE() = -1)
    BEGIN	
                    PRINT ' XACT_STATE() = -1'
					ROLLBACK TRANSACTION;
    END
                                
    --Report stored procedure failed
    RETURN (1); 
END CATCH

END;






--GO



