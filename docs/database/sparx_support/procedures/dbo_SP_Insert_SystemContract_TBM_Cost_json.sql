









/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemContract_TBM_Cost_json
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/09/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemContract_TBM_Cost attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemContract_TBM_Cost_json] 
 			 @jsonInput
 *
 *
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemContract_TBM_Cost_json
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemContract_TBM_Cost_json] 
--Add the parameters for the stored procedure here
	@jsonInput nvarchar(max)

AS

DECLARE 

	@GUID nvarchar(60) = '',
	@propertyId nvarchar(255) = '', 
	@propertyType nvarchar(255) = '', 
	@RowNum INT = 0,
	@RC INT = 0

PRINT 'Inside Insert SystemContract_TBM_Cost';

DECLARE @New_ObjectID INT = 0;

IF len(@jsonInput) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

-- Do the System Contract addition 

--Select 'Input :'+@jsonInput;

PRINT 'Declare Cursor';
		
	
		DECLARE db_add_cursor CURSOR FOR
		SELECT s.fromRef, propertyType = s.property, s.toRef
		FROM  OPENJSON (@jsonInput,'$.Relations')  
		WITH (   
						 fromRef	nvarchar(255) 
						,property	nvarchar(255) 
						,toRef nvarchar(255) 
			) s; 

PRINT 'Before Open';

		OPEN db_add_cursor;

PRINT 'After Open';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_cursor INTO 
			@GUID, @propertyType, @propertyId;
			
PRINT '1st Fetch';

		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			
			-- Call role insert
			EXECUTE @RC = [dbo].[SP_Insert_SystemContract_TBM_Cost_Reln] 
			 	  @propertyType
				 ,@propertyId
				 ,@GUID;

			-- Fetch next record
			FETCH NEXT FROM db_add_cursor INTO 
				@GUID, @propertyType, @propertyId;

PRINT 'Next Fetch';

		END 

		-- 6 - Close the cursor
		CLOSE db_add_cursor;  

		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_cursor; 

PRINT 'End of System Contract json processing';

	END TRY
	BEGIN CATCH

PRINT 'In System Contract json processing exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;














