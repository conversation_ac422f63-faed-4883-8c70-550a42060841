





/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_Person
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/10/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update thePerson attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_Person] 
			 @Email
			,@ExternalSource
			,@FirstName
			,@LastName
			,@Phone
			,@TechName
			,@UserName
			,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_Person
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_Person] 
--Add the parameters for the stored procedure here
	@Email nvarchar(255) = '', 
	--@ExternalID nvarchar(255) = '', 
	@ExternalSource nvarchar(255) = '', 
	@FirstName nvarchar(255) = '', 
	@LastName nvarchar(255) = '', 
	--@OrgUnitName nvarchar(255) = '', 
	--@PersonID nvarchar(255) = '', 
	@Phone nvarchar(255) = '', 
	@TechName nvarchar(255) = '', 
	@UserName nvarchar(255) = '', 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update Person';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		UPDATE SparxDB.dbo.t_object SET Name = @UserName
		WHERE t_object.ea_guid = @GUID;


		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'Email' THEN ISNULL(@Email,Value)
				--WHEN Property = 'External ID' THEN ISNULL(@ExternalID,Value)
				WHEN Property = 'External Source' THEN ISNULL(@ExternalSource,Value)
				WHEN Property = 'First Name' THEN ISNULL(@FirstName,Value)
				WHEN Property = 'Last Name' THEN ISNULL(@LastName,Value)
				--WHEN Property = 'Org Unit Name' THEN ISNULL(@OrgUnitName,Value)
				--WHEN Property = 'Person ID' THEN ISNULL(@PersonID,Value)
				WHEN Property = 'Phone' THEN ISNULL(@Phone,Value)
				WHEN Property = 'Tech Name' THEN ISNULL(@TechName,Value)
				WHEN Property = 'User Name' THEN ISNULL(@UserName,Value)

				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Person';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Person'
		and Value = '';

PRINT 'End of Update Person';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update Person Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;











