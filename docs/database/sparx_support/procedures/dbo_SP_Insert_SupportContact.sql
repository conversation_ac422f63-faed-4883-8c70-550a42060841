


/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SupportContact
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/16/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SupportContact attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SupportContact] 
			 @ContactName
			,@ContactEmail
			,@ContactPhone
			,@ContactTitle
			,@URLLink
			,@GUID


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SupportContact
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 7/11/2023	Sita Paturi					Removed "ContactOrg" Reference
 * 7/12/2023	Aditya Sharma				Removed "ContactID" Reference as that is Legacy and added Contact Name
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SupportContact] 
--Add the parameters for the stored procedure here
	@ContactName nvarchar(255) = NULL, 
	@ContactEmail nvarchar(255) = NULL, 
	--@ContactID nvarchar(255) = NULL, 
	--@ContactOrg nvarchar(255) = NULL, 
	@ContactPhone nvarchar(255) = NULL, 
	@ContactTitle nvarchar(255) = NULL, 
	@URLLink nvarchar(255) = NULL, 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Insert SupportContact';
PRINT 'GUID = '+@GUID;

DECLARE @New_ObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		--BEGIN TRAN
		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=Support Contact;FQName=CMS EA::Support Contact;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'Support Contact'
		WHERE t_object.ea_guid = @GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
PRINT @New_ObjectID;
		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'Support Contact Name' as Property, @ContactName as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Contact Email' as Property, @ContactEmail as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			--Select @New_ObjectID as ObjectID, 'Contact ID' as Property, @ContactID as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			--Select @New_ObjectID as ObjectID, 'Contact Org' as Property, @ContactOrg as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Contact Phone' as Property, @ContactPhone as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'Contact Title' as Property, @ContactTitle as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'URLLink' as Property, @URLLink as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
		) t;

		Select * from SparxDB.dbo.t_objectproperties where Object_ID = @New_ObjectID;

PRINT 'At End of Insert SupportContact';

	END TRY
	BEGIN CATCH

PRINT 'In Insert SupportContact Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












