






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SupportContact
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/16/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theSupportContact attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SupportContact] 
			 @ContactName
			,@ContactEmail
			,@ContactPhone
			,@ContactTitle
			,@URLLink
			,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SupportContact
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 07/11/2023	Sita Paturi					Removed "ContactOrg" reference
 * 7/12/2023	Aditya Sharma				Removed "ContactID" Reference as that is Legacy and added Contact Name
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SupportContact] 
--Add the parameters for the stored procedure here
	@ContactName nvarchar(255) = NULL, 
	@ContactEmail nvarchar(255) = NULL, 
	--@ContactID nvarchar(255) = NULL, 
	@ContactPhone nvarchar(255) = NULL, 
	@ContactTitle nvarchar(255) = NULL, 
	@URLLink nvarchar(255) = NULL, 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update SupportContact';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

PRINT 'Before Update'

		--UPDATE SparxDB.dbo.t_object SET Name = ISNULL(@ContactName,Name)
		--WHERE t_object.ea_guid = @GUID
		--and t_object.Stereotype = 'Support Contact';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'Contact Email' THEN ISNULL(@ContactEmail,Value)
				WHEN Property = 'Support Contact Name' THEN ISNULL(@ContactName,Value)
				--WHEN Property = 'Contact ID' THEN ISNULL(@ContactID,Value)
				WHEN Property = 'Contact Phone' THEN ISNULL(@ContactPhone,Value)
				WHEN Property = 'Contact Title' THEN ISNULL(@ContactTitle,Value)
				WHEN Property = 'URLLink' THEN ISNULL(@URLLink,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Support Contact';

PRINT 'After Property updates'

		--UPDATE SparxDB.dbo.t_object SET Name = NULL
		--WHERE t_object.ea_guid = @GUID
		--and t_object.Stereotype = 'Support Contact'
		--and Name = '';


		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'Support Contact'
		and Value = '';

PRINT 'End of Update SupportContact';
/*
		Select t_objectproperties.* 
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @ContactID;
*/

	END TRY
	BEGIN CATCH

PRINT 'Inside Update SupportContact Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












