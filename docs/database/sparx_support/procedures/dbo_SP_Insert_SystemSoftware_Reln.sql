










/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemSoftware_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/06/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SystemSoftware attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemSoftware_Reln] 
			 	@systemId, 
				@SoftwareId,
				@UsedforAICapabilities,
				@PurchasedUnderEnterpriseLicenseAgreement, 
				@RelationshipImpact, 
				@RelationshipStatus, 
				@UsedasAPIGateway, 
				@ActualVersion,
				@SoftwareELAOrganization

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemSoftware_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 07/17/2023	Aditya Sharma				Added v24 attributes
 * 08/13/2024	Aditya Sharma				Remove deprecated field for FY25 Census
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemSoftware_Reln] 
--Add the parameters for the stored procedure here
	@systemId nvarchar(255) = '', 
	@SoftwareId nvarchar(255) = '',
	@UsedforAICapabilities nvarchar(255) = '', 
	@PurchasedUnderEnterpriseLicenseAgreement nvarchar(255) = '', 
	@RelationshipImpact nvarchar(255) = '', 
	@RelationshipStatus nvarchar(255) = '', 
	@UsedasAPIGateway nvarchar(255) = '', 
	@ActualVersion nvarchar(255) = '',
	--@SoftwareCost nvarchar(255) = '',
	@SoftwareELAOrganization nvarchar(255) = ''


AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@Software_ObjectId nvarchar(255) = '';


PRINT 'Inside Insert SystemSoftware';
PRINT 'SystemId = '+@systemId;
PRINT 'SoftwareId = '+@SoftwareId;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @tagNotes nvarchar(max) = 'Values: Most of this funding is directly and only for this system (over 80%),A large part of this funding is directly and only for this system (between 40%-80%),Only part of this funding is directly for this system (less than 40%)  ';
DECLARE @RelStatusNotes nvarchar(max) = 'Values: Future,Active,Retired,Deleted,Never Implemented  Default: Active  ';
DECLARE @RelStatusDefault nvarchar(255);
DECLARE @RelImpactNotes nvarchar(max) = 'Values: Critical,Major,Moderate,Minor,None,Unknown  Default: Moderate  ';
DECLARE @RelImpactDefault nvarchar(255);


IF len(@systemId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--BEGIN TRAN

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @systemId;
		Select @Software_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @SoftwareId;
		Select @RelStatusDefault = LTRIM(RTRIM(SUBSTRING(@RelStatusNotes,CHARINDEX('Default:',@RelStatusNotes,1)+8,LEN(@RelStatusNotes)-CHARINDEX('Default:',@RelStatusNotes,1) -7)));
		Select @RelImpactDefault = LTRIM(RTRIM(SUBSTRING(@RelImpactNotes,CHARINDEX('Default:',@RelImpactNotes,1)+8,LEN(@RelImpactNotes)-CHARINDEX('Default:',@RelImpactNotes,1) -7)));


PRINT 'Start Reln processing'
		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
		Values (@System_ObjectId+' : '+@Software_ObjectId, 'Association', 'Uses Software Product','Source -> Destination', @System_ObjectId, @Software_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

		Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where [Start_Object_ID] = @System_ObjectId and [End_Object_ID] = @Software_ObjectId
			and Stereotype = 'Uses Software Product';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Uses Software Product;FQName=CMS EA::Uses Software Product;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

		---insert the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Status', @RelStatusDefault, @RelStatusNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Relationship Impact', @RelImpactDefault, @RelImpactNotes, CONCAT('{',NEWID(),'}'));

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Used for AI Capabilities', @UsedforAICapabilities, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Purchased Under Enterprise License Agreement', @PurchasedUnderEnterpriseLicenseAgreement, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Used as API Gateway', @UsedasAPIGateway, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Actual Version', @ActualVersion, '', CONCAT('{',NEWID(),'}'))

-- Applicable on v23: 

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'ELA Vendor ID', NULL, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Closest Version', NULL, '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Import Year', NULL, '', CONCAT('{',NEWID(),'}'))

-- New v24 attributes

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Software Cost', '', '', CONCAT('{',NEWID(),'}'))

		Insert Into SparxDB.dbo.t_connectortag (ElementID, Property, Value, Notes, ea_guid)
		Values (@New_ConnectorID, 'Software ELA Organization', @SoftwareELAOrganization, '', CONCAT('{',NEWID(),'}'))


PRINT 'Properties inserted'

		-- Check the inserts

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records

		--ROLLBACK;

	END TRY
	BEGIN CATCH

PRINT 'Inside Insert System Software Reln Exception';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;
















