






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Get_EASiIntake_By_Status_Paginated
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   05/16/2025
 *
 * DESCRIPTION/PURPOSE:
 *	Get the list of Intake Requests in paginated set based on input parameters 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Get_EASiIntake_By_Status_Paginated] 
			 @request_cedar_status
			,@client_created_date
			,@offset
			,@num_rows
			,@Outputjson


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Get_EASiIntake_By_Status_Paginated
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
 CREATE PROCEDURE [dbo].[SP_Get_EASiIntake_By_Status_Paginated]
			 @request_cedar_status nvarchar(255)
			,@client_created_date datetime
			,@offset int
			,@num_rows int
			,@Outputjson nvarchar(max) OUT

 AS

 BEGIN

 -- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		SET @Outputjson = 
		(
		SELECT	 [INTAKE_REQUEST_ID] as cedarId
				,[CLIENT_ID] as clientId
				--,[CLIENT_NAME]
				--,[REQUEST_TYPE]
				--,[REQUEST_TYPE_SCHEMA]
				--,[REQUEST_CLIENT_STATUS]
				,[REQUEST_CEDAR_STATUS] as cedarStatus
				--,[REQUEST_DATA_FORMAT]
				--,[REQUEST_DATA]
				--,[CLIENT_CREATED_DATE]
				--,[CLIENT_LAST_UPDATED_DATE]
				--,[CREATED_DATE]
				--,[CREATED_BY]
				--,[LAST_UPDATED_DATE]
				--,[LAST_UPDATED_BY]
				,[CLIENT_VERSION] as version
				,ISNULL([REQUEST_CEDAR_STATUS_MESSAGE],'') as cedarStatusMessage
			FROM
				(
				 SELECT [INTAKE_REQUEST_ID]
					  ,[CLIENT_ID]
					  ,[CLIENT_NAME]
					  ,[REQUEST_TYPE]
					  ,[REQUEST_TYPE_SCHEMA]
					  ,[REQUEST_CLIENT_STATUS]
					  ,[REQUEST_CEDAR_STATUS]
					  ,[REQUEST_DATA_FORMAT]
					  ,[REQUEST_DATA]
					  ,[CLIENT_CREATED_DATE]
					  ,[CLIENT_LAST_UPDATED_DATE]
					  ,[CREATED_DATE]
					  ,[CREATED_BY]
					  ,[LAST_UPDATED_DATE]
					  ,[LAST_UPDATED_BY]
					  ,[CLIENT_VERSION]
					  ,[REQUEST_CEDAR_STATUS_MESSAGE]
					  ,DENSE_RANK() OVER (PARTITION BY REQUEST_CEDAR_STATUS ORDER BY [CLIENT_CREATED_DATE] DESC) as rnk
				FROM [CEDAR_Support].[CEDAR_API].[INTAKE_REQUEST]
				WHERE [CLIENT_CREATED_DATE] > @client_created_date
				and [REQUEST_CEDAR_STATUS] = @request_cedar_status
				) t
				WHERE rnk >= @offset
				and rnk < @offset + @num_rows
				ORDER BY CLIENT_CREATED_DATE, INTAKE_REQUEST_ID
				FOR JSON PATH, ROOT('ResultSet')
				);

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;




