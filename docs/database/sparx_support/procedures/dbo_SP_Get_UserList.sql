






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Get_UserList
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   04/11/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Get the UserList attributes in SparxDB
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Get_UserList] 
			 @username
			,@firstname
			,@lastname
			,@phone
			,@email
			,@Outputjson OUT


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Get_UserList
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Get_UserList] 
--Add the parameters for the stored procedure here
	@username nvarchar(255) = NULL,
	@firstname nvarchar(255) = NULL, 
	@lastname nvarchar(255) = NULL, 
	@phone nvarchar(255) = NULL, 
	@email nvarchar(255) = NULL,
	@Outputjson nvarchar(max) OUT

AS

BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		SET @Outputjson = 
		(SELECT       
            'Person' as [ClassName],
			a.[Sparx Person GUID] AS [id],
            a.[User Name] as [Values.userName],
            a.[First Name] AS [Values.firstName],
            a.[Last Name] AS [Values.lastName], 
            a.Phone as [Values.phone] ,
            a.Email AS [Values.email]
		FROM [Sparx_Support].[dbo].[Sparx_Person] a
		WHERE	(@username is null or (@username is not null and a.[User Name] like '%'+@username+'%'))
			AND (@firstname is null or (@firstname is not null and a.[First Name] like '%'+@firstname+'%'))
			AND (@lastname is null or (@lastname is not null and a.[Last Name] like '%'+@lastname+'%'))
			AND (@phone is null or (@phone is not null and a.Phone like '%'+@phone+'%'))
			AND (@email is null or (@email is not null and a.Email like '%'+@email+'%'))
		FOR JSON PATH, ROOT('Objects')
		);

		IF @Outputjson IS NULL
				THROW 50005, N'User not found', 1;

SELECT @Outputjson;

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;










