






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemExchange
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/23/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemExchange attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemExchange] 
				 @DataExchangeFormat
				,@DataExchangeFormatOther
				,@DataSharedviaAPI
				,@ExchangeContainsPHI
				,@ExchangeContainsPII
				,@ExchangeFrequency
				,@ExchangeIncludesBankingData
				,@ExchangeincludesBenefitiaryAddressData
				,@ExchangeSupportsMailingtoBenefitiaries
				,@Exchange_ID
				,@IEAgreement
				,@NumberofRecordsExchanged
				,@Description
				,@DataArea
				,@ExchangeApiOwner
				,@ExchangeBenefitiaryAddressPurpose
				,@ExchangeHealthDisparityData
				,@ExchangeEndDate
				,@ExchangeStartDate
				,@ExchangeConnectionAuthenticated
				,@ExchangeContainsCUI
				,@ExchangeNetworkProtocol
				,@ExchangeNetworkProtocolOther
				,@ExchangeCUIType
				,@ObjectState
				,@Exchange
				,@GUID




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemExchange
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 10/13/2023	Aditya Sharma				Fixed column names to match repository config setup for 3 fields
 * 12/06/2023	Aditya Sharma				Add Exchange Name to the Update
 * 08/13/2024	Aditya Sharma				Modify Fields for FY25 Census
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SystemExchange] 
--Add the parameters for the stored procedure here
	@DataExchangeFormat nvarchar(255) = NULL, 
	@DataExchangeFormatOther nvarchar(255) = NULL, 
	@DataSharedviaAPI nvarchar(255) = NULL, 
	@ExchangeContainsPHI nvarchar(255) = NULL, 
	@ExchangeContainsPII nvarchar(255) = NULL, 
	@ExchangeFrequency nvarchar(max) = NULL, 
	@ExchangeIncludesBankingData nvarchar(255) = NULL, 
	@ExchangeincludesBenefitiaryAddressData nvarchar(255) = NULL, 
	@ExchangeSupportsMailingtoBenefitiaries nvarchar(255) = NULL, 
	@Exchange_ID nvarchar(255) = NULL, 
	@IEAgreement nvarchar(255) = NULL, 
	@NumberofRecordsExchanged nvarchar(255) = NULL, 
	@Description nvarchar(max) = NULL, 
	@DataArea nvarchar(255) = NULL, 
	@ExchangeApiOwner nvarchar(255) = NULL, 
	@ExchangeBenefitiaryAddressPurpose nvarchar(max) = NULL, 
	@ExchangeHealthDisparityData nvarchar(255) = NULL, 
	@ExchangeEndDate nvarchar(255) = NULL, 
	@ExchangeStartDate nvarchar(255) = NULL, 
	@ExchangeConnectionAuthenticated nvarchar(255) = NULL, 
	@ExchangeContainsCUI nvarchar(255) = NULL, 
	--@ExchangeCUIDescription nvarchar(max) = NULL, 
	@ExchangeNetworkProtocol nvarchar(max) = NULL, 
	@ExchangeNetworkProtocolOther nvarchar(255) = NULL, 
	@ExchangeCUIType nvarchar(max) = NULL, 
	
	@RelationshipStatus nvarchar(255) = NULL, 
	@Exchange nvarchar(255) = NULL, 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update SystemExchange';

DECLARE @ConnectorID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		Select @ConnectorID = Connector_ID from SparxDB.dbo.t_connector where ea_guid = @GUID 
			and Stereotype = 'System Interface';
PRINT 'Connector = '+CAST(@ConnectorID AS NVARCHAR(10))

		UPDATE SparxDB.dbo.t_connector 
		SET Notes = ISNULL(@Description,Notes), Name = ISNULL(@Exchange,Name)
		where ea_guid = @GUID; 

		---Update the values into the tagged values spefified in the stereotype using the ID of the connector from above.
		UPDATE ct SET Value = 
			CASE	WHEN Property = 'Data Exchange Format' THEN ISNULL(@DataExchangeFormat,Value)
					WHEN Property = 'Data Exchange Format - Other' THEN ISNULL(@DataExchangeFormatOther,Value)
					WHEN Property = 'Data Shared via API' THEN ISNULL(@DataSharedviaAPI,Value)
					WHEN Property = 'Exchange Contains PHI' THEN ISNULL(@ExchangeContainsPHI,Value)
					WHEN Property = 'Exchange Contains PII' THEN ISNULL(@ExchangeContainsPII,Value)
					WHEN Property = 'Exchange Includes Banking Data' THEN ISNULL(@ExchangeIncludesBankingData,Value)
					WHEN Property = 'Exchange includes Beneficiary Address Data' THEN ISNULL(@ExchangeincludesBenefitiaryAddressData,Value)
					WHEN Property = 'Exchange Supports Mailing to Beneficiaries' THEN ISNULL(@ExchangeSupportsMailingtoBenefitiaries,Value)
					WHEN Property = 'Exchange_ID' THEN ISNULL(@Exchange_ID,Value)
					WHEN Property = 'IE Agreement' THEN ISNULL(@IEAgreement,Value)
					WHEN Property = 'Number of Records Exchanged' THEN ISNULL(@NumberofRecordsExchanged,Value)
					-- Add new FY24 fields here
					WHEN Property = 'Exchange Connection Authenticated' THEN ISNULL(@ExchangeConnectionAuthenticated,Value)
					WHEN Property = 'Exchange Contains CUI' THEN ISNULL(@ExchangeContainsCUI,Value)
					WHEN Property = 'Exchange Network Protocol Other' THEN ISNULL(@ExchangeNetworkProtocolOther,Value)
					WHEN Property = 'Relationship Status' THEN ISNULL(@RelationshipStatus,Value)
					--WHEN Property = 'Start Date' THEN ISNULL(@ExchangeStartDate,Value)
					WHEN Property = 'End Date' THEN @ExchangeEndDate
					WHEN Property = 'Health Disparity Data' THEN ISNULL(@ExchangeHealthDisparityData,Value)
					WHEN Property = 'API Owner' THEN ISNULL(@ExchangeApiOwner,Value)
					

					ELSE Value
			END
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'System Interface';

		UPDATE ct SET Notes = 
			CASE	WHEN Property = 'Exchange Frequency' THEN ISNULL(@ExchangeFrequency,ct.Notes)
					--WHEN Property = 'Exchange CUI Description' THEN ISNULL(@ExchangeCUIDescription,ct.Notes)
					WHEN Property = 'Exchange Network Protocol' THEN ISNULL(@ExchangeNetworkProtocol,ct.Notes)
					WHEN Property = 'Beneficiary Address Purpose' THEN ISNULL(@ExchangeBenefitiaryAddressPurpose,ct.Notes)
					WHEN Property = 'Exchange CUI Type' THEN ISNULL(@ExchangeCUIType,ct.Notes)
					ELSE ct.NOTES
			END
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'System Interface';

		UPDATE SparxDB.dbo.t_xref
		SET Description = ISNULL(@DataArea,Description)
		WHERE Client = @GUID
		and Behavior = 'conveyed';

		UPDATE ct 
		SET Value = NULL
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'System Interface'
		and Value = '';

		UPDATE ct 
		SET Notes = NULL
		FROM		SparxDB.dbo.t_connector c
		INNER JOIN	SparxDB.dbo.t_connectortag ct
			ON c.Connector_ID = ct.ElementID
		WHERE c.ea_guid = @GUID
		and c.Stereotype = 'System Interface'
		and ct.Property IN ('Exchange Frequency','Exchange CUI Type','Exchange Network Protocol','Beneficiary Address Purpose')
		and ct.Notes = '';


PRINT 'Properties Updated'

		-- Check the Updates

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID; -- check connection value records


	END TRY
	BEGIN CATCH

PRINT 'Inside Update SystemExchange Exception'
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;



















