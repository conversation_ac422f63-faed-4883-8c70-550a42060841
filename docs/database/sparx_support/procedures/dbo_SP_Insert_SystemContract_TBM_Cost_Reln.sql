




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SystemContract_TBM_Cost_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   04/14/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemContract attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemContract_TBM_Cost_Reln] 
				@propertyType,
				@property,
				@GUID




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SystemContract_TBM_Cost_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemContract_TBM_Cost_Reln] 
--Add the parameters for the stored procedure here
	@propertyType nvarchar(255) = NULL,
	@property nvarchar(255) = NULL,
	@GUID nvarchar(60) = NULL

AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@Contract_ObjectId nvarchar(255) = '';


PRINT 'Inside SP_Insert_SystemContract_TBM_Cost_Reln';
PRINT 'propertyType = '+@propertyType;

DECLARE @ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';
DECLARE @PropertyName nvarchar(255) = '';


IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		Select @ConnectorID = Connector_ID from SparxDB.dbo.t_connector where ea_guid = @GUID 
			and Stereotype = 'Develops';
PRINT 'Connector = '+CAST(@ConnectorID AS NVARCHAR(10))

		IF @propertyType = 'CMS_TBM_IT_TOWER_CATEGORY' 
			Select @PropertyName = [TBMTower Name] from [dbo].[Sparx_TBMTower]
				where [Sparx TBMTower GUID] = @property;

		IF @propertyType = 'CMS_TBM_COST_POOL' 
			Select @PropertyName = [CostPool Name] from [dbo].[Sparx_CostPool]
				where [Sparx CostPool GUID] = @property;

PRINT 'PropertyName = '+@PropertyName

		---Update the values into the tagged values spefified in the stereotype using the ID of the connector from above.

		Update SparxDB.dbo.t_connectortag 
		SET Value = 'True'
		WHERE Property = @PropertyName
		and ElementID = @ConnectorID;


PRINT 'Properties Updated'

		-- Check the Updates

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID; -- check connection value records


	END TRY
	BEGIN CATCH

PRINT 'Inside SP_Insert_SystemContract_TBM_Cost_Reln exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;

















