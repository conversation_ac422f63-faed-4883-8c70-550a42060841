






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_SORN
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/08/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the SORN attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SORN] 
			 @SORN
			,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_SORN
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SORN] 
--Add the parameters for the stored procedure here
	@SORN nvarchar(255) = NULL, 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Insert SORN';

DECLARE @New_ObjectID INT = 0;
DECLARE @CMSSORNSearch nvarchar(255) = NULL;
DECLARE @HHSSORNSearch nvarchar(255) = NULL; 

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		SET @CMSSORNSearch = 'https://www.federalregister.gov/documents/search?conditions%5Bagencies%5D%5B%5D=centers-for-medicare-medicaid-services&conditions%5Bterm%5D='+@SORN;
		SET @HHSSORNSearch = 'https://www.federalregister.gov/documents/search?conditions%5Bagencies%5D%5B%5D=health-and-human-services-department&conditions%5Bterm%5D=SORN+'+@SORN;

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=SORN;FQName=CMS EA::SORN;@ENDSTEREO;', @GUID, '<none>');

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'SORN', Name = @SORN
		WHERE t_object.ea_guid = @GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select ObjectID, Property, Val, Notes, ea_guid from
		(
			Select @New_ObjectID as ObjectID, 'CMS SORN Search' as Property, @CMSSORNSearch as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid UNION ALL
			Select @New_ObjectID as ObjectID, 'HHS SORN Search' as Property, @HHSSORNSearch as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid 
		) t;

PRINT 'End of Insert SORN';


	END TRY
	BEGIN CATCH

PRINT 'Inside Insert SORN Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












