




/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_EASiBCSCostLine
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/20/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update theEASiBCSCostLine attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_EASiBCSCostLine] 
			 @Budget
			,@CostofCapital
			,@MonetaryCodeID
			,@MonetaryType
			,@MonetaryUnit
			,@Year
			,@phase
			,@solution
			,@GUID
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_EASiBCSCostLine
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_EASiBCSCostLine] 
--Add the parameters for the stored procedure here
	@Budget nvarchar(255) = NULL, 
	@CostofCapital nvarchar(255) = NULL, 
	@MonetaryCodeID nvarchar(255) = NULL, 
	@MonetaryType nvarchar(255) = NULL, 
	@MonetaryUnit nvarchar(255) = NULL, 
	@Year nvarchar(255) = NULL, 
	@phase nvarchar(255) = NULL, 
	@solution nvarchar(max) = NULL, 

	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update EASiBCSCostLine';

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		--UPDATE SparxDB.dbo.t_object SET Name = @Name, Note = @Description
		--WHERE t_object.ea_guid = @GUID;


		UPDATE SparxDB.dbo.t_objectproperties SET Value = 
		CASE	WHEN Property = 'Budget' THEN @Budget
				WHEN Property = 'Cost of Capital' THEN @CostofCapital
				WHEN Property = 'Monetary Code ID' THEN @MonetaryCodeID
				WHEN Property = 'Monetary Type' THEN @MonetaryType
				WHEN Property = 'Monetary Unit' THEN @MonetaryUnit
				WHEN Property = 'Phase' THEN @phase
				--WHEN Property = 'Budget' THEN ISNULL(@Budget,Value)
				--WHEN Property = 'Cost of Capital' THEN ISNULL(@CostofCapital,Value)
				--WHEN Property = 'Monetary Code ID' THEN ISNULL(@MonetaryCodeID,Value)
				--WHEN Property = 'Monetary Type' THEN ISNULL(@MonetaryType,Value)
				--WHEN Property = 'Monetary Unit' THEN ISNULL(@MonetaryUnit,Value)
				--WHEN Property = 'Phase' THEN ISNULL(@phase,Value)
				ELSE Value
		END
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'EASi BCS Cost Line';
		
		UPDATE SparxDB.dbo.t_objectproperties SET Notes = @solution
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'EASi BCS Cost Line'
		and t_objectproperties.Property = 'Solution';

		UPDATE SparxDB.dbo.t_objectproperties SET Value = NULL
		FROM		SparxDB.dbo.t_object 
		INNER JOIN	SparxDB.dbo.t_objectproperties 
			ON t_object.Object_ID = t_objectproperties.Object_ID
		WHERE t_object.ea_guid = @GUID
		and t_object.Stereotype = 'EASi BCS Cost Line'
		and Value = '';

PRINT 'End of Update EASiBCSCostLine';

	END TRY
	BEGIN CATCH

PRINT 'Inside Update EASiBCSCostLine Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;










