











/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_EASiIntakeFundingSource_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/23/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the EASiIntakeFundingSource attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_EASiIntakeFundingSource_Reln] 
			 	@EASiIntakeId, 
				@EASiFundingSourceId


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_EASiIntakeFundingSource_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 * 05/20/2025	Aditya Sharma				Update error message to print the passed IDs
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_EASiIntakeFundingSource_Reln] 
--Add the parameters for the stored procedure here
	@EASiIntakeId nvarchar(255) = NULL, 
	@EASiFundingSourceId nvarchar(255) = NULL

AS

DECLARE
	@EASiIntake_ObjectId nvarchar(255) = '', 
	@EASiFundingSource_ObjectId nvarchar(255) = '',
	@ErrorMessage nvarchar(255) = '';

PRINT 'Inside Insert EASiIntakeFundingSource';

BEGIN

	BEGIN TRY
		DECLARE @New_ConnectorID INT = 0;
		DECLARE @New_ConnectorGUID nvarchar(60) = '';

		SET @ErrorMessage = 'Input GUID is empty, @EASiIntakeId | @EASiFundingSourceId = '+ISNULL(@EASiIntakeId,'')+' | '+ISNULL(@EASiFundingSourceId,'')

		IF len(ISNULL(@EASiIntakeId,'')) = 0 OR len(ISNULL(@EASiFundingSourceId,'')) = 0
			THROW 50001,@ErrorMessage,1;

		ELSE
		BEGIN

		-- SET NOCOUNT ON added to prevent extra result sets from
		-- interfering with SELECT statements.
			SET NOCOUNT ON;


				Select @EASiIntake_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @EASiIntakeId;
				Select @EASiFundingSource_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @EASiFundingSourceId;


PRINT 'Start Reln processing'
				Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction, Start_Object_ID, End_Object_ID, ea_guid)
				Values (@EASiIntake_ObjectId+' : '+@EASiFundingSource_ObjectId, 'Association', 'Intake-FundingSource','Unspecified', @EASiIntake_ObjectId, @EASiFundingSource_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

				Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where Start_Object_ID = @EASiFundingSource_ObjectId and End_Object_ID = @EASiFundingSource_ObjectId
					and Stereotype = 'Intake-FundingSource';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

				INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
				VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Intake-FundingSource;FQName=CMS EA::Intake-FundingSource;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

				-- Check the inserts

				--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record
				--Select * from SparxDB.dbo.t_connectortag where ElementID = @New_ConnectorID; -- check connection value records

		END
	END TRY
	BEGIN CATCH

PRINT 'Inside EASiIntakeFundingSource Exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;




