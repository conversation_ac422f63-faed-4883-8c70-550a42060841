







/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_SystemBudget_json
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/09/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the SystemBudget attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_SystemBudget_json] 
 			 @jsonInput
 *
 *
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_SystemBudget_json
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_SystemBudget_json] 
--Add the parameters for the stored procedure here
	@jsonInput nvarchar(max)

AS

DECLARE 

	@GUID nvarchar(60) = '',
	@systemId nvarchar(255) = '', 
	@budgetId nvarchar(255) = '', 
	@funding nvarchar(255) = '', 
	@RowNum INT = 0,
	@RC INT = 0

PRINT 'Inside Update SystemBudget';

DECLARE @New_ObjectID INT = 0;

IF len(@jsonInput) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

-- Do the System Budget addition 

PRINT 'Declare Cursor';
		
	
		DECLARE db_add_cursor CURSOR FOR
		SELECT RefStr, [funding]
		FROM  
			OPENJSON (@jsonInput,'$.Objects')  
		WITH (   
						 RefStr	nvarchar(255)  
						,[funding]	nvarchar(255) '$.Values.cms_funding'					
			) 

PRINT 'Before Open';

		OPEN db_add_cursor;

PRINT 'After Open';

		-- 3 - Fetch the next record from the cursor
		FETCH NEXT FROM db_add_cursor INTO 
			@GUID, @funding;

PRINT '1st Fetch';

		WHILE @@FETCH_STATUS = 0  
 
		BEGIN  
			
			-- Call Budget Update
			EXECUTE @RC = [dbo].[SP_Update_SystemBudget_Reln] 
				  @funding
				 ,@GUID;

			-- Fetch next record
			FETCH NEXT FROM db_add_cursor INTO 
				@GUID, @funding;

PRINT 'Next Fetch';

		END 

		-- 6 - Close the cursor
		CLOSE db_add_cursor;  

		-- 7 - Deallocate the cursor
		DEALLOCATE db_add_cursor; 


	END TRY
	BEGIN CATCH

PRINT 'Inside Update System Budget json exception';

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;













