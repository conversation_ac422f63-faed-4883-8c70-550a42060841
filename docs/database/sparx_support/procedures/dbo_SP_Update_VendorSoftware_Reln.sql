






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_VendorSoftware_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/32/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Update the VendorSoftware attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_VendorSoftware_Reln] 
				@SystemID, 
				@VendorID, 
				@GUID




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_VendorSoftware_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_VendorSoftware_Reln] 
--Add the parameters for the stored procedure here
	@VendorID nvarchar(255) = NULL, 
	@GUID nvarchar(60) = ''

AS

PRINT 'Inside Update VendorSoftware';

DECLARE @ConnectorID INT = 0;
DECLARE @VendorObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


		Select @VendorObjectID = Object_ID from SparxDB.dbo.t_object where ea_guid = @VendorID;

		---Update the value of the vendor for the given Software Product

		UPDATE SparxDB.dbo.t_connector 
		SET Start_Object_ID = @VendorObjectID, Name = End_Object_ID+' : '+@VendorObjectID
		where ea_guid = @GUID 
		and Stereotype = 'Software Vendor';

PRINT 'Connection Updated'

		-- Check the Updates

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @ConnectorID; -- check connection record
		--Select * from SparxDB.dbo.t_connectortag where ElementID = @ConnectorID; -- check connection value records


	END TRY
	BEGIN CATCH

PRINT 'Inside Update Vendor Software Reln Exception'
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;



















