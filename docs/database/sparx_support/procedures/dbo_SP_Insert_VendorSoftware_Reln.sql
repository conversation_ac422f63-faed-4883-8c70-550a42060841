











/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_VendorSoftware_Reln
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/13/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the VendorSoftware attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_VendorSoftware_Reln] 
			 	@VendorId, 
				@SoftwareId




 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_VendorSoftware_Reln
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_VendorSoftware_Reln] 
--Add the parameters for the stored procedure here
	@VendorId nvarchar(255) = '', 
	@SoftwareId nvarchar(255) = ''

AS

DECLARE
	@Vendor_ObjectId nvarchar(255) = '', 
	@Software_ObjectId nvarchar(255) = '';


PRINT 'Inside Insert VendorSoftware';
PRINT 'VendorId = '+@VendorId;
PRINT 'SoftwareId = '+@SoftwareId;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';


IF len(@VendorId) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		Select @Vendor_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @VendorId;
		Select @Software_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @SoftwareId;


PRINT 'Start Reln processing'
		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
		Values (@Software_ObjectId+' : '+@Vendor_ObjectId, 'Association', 'Software Vendor','Source -> Destination', @Vendor_ObjectId, @Software_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

		Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where [Start_Object_ID] = @Vendor_ObjectId and [End_Object_ID] = @Software_ObjectId
			and Stereotype = 'Software Vendor';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=Software Vendor;FQName=CMS EA::Software Vendor;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

		-- Check the inserts

		Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record


	END TRY
	BEGIN CATCH

PRINT 'Inside Insert Vendor Software Reln Exception';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;

















