






/*********************************************************************************************
 * PROCEDURE_NAME: SP_Insert_Vendor
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   03/08/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the Vendor attributes in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_Vendor] 
			 @VendorName
			,@GUID


 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Insert_Vendor
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_Vendor] 
--Add the parameters for the stored procedure here
	@VendorName nvarchar(255) = '', 
	@GUID nvarchar(60) = ''

AS

Select 'Inside Insert Vendor';
Select 'GUID = '+@GUID;

DECLARE @New_ObjectID INT = 0;

IF len(@GUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY


Select @GUID;
Select 'VendorName : '+@VendorName

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'element property', 'Public', '@STEREO;Name=Vendor;FQName=CMS EA::Vendor;@ENDSTEREO;', @GUID, '<none>');
Select 'Before Update';

		UPDATE SparxDB.dbo.t_object set t_object.Stereotype = 'Vendor', Name = @VendorName
		WHERE t_object.ea_guid = @GUID;
Select 'GUID = '+@GUID;

		SELECT @New_ObjectID = OBJECT_ID from SparxDB.dbo.t_object where ea_guid = @GUID;
Select @New_ObjectID

		INSERT INTO SparxDB.dbo.t_objectproperties (Object_ID, Property, Value, Notes, ea_guid)
		Select @New_ObjectID as ObjectID, 'Legacy ID' as Property, '' as Val, '' as Notes, CONCAT('{',NEWID(),'}') as ea_guid;

	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;












