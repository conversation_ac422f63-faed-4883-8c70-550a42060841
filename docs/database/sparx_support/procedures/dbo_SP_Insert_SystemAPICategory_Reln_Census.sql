





/*********************************************************************************************
 * PROCEDURE_NAME: [SP_Insert_SystemAPICategory_Reln_Census]
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   07/19/2023
 *
 * DESCRIPTION/PURPOSE:
 *	Insert the System-APICategory relationship in SparxDB 
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Insert_SystemAPICategory_Reln_Census] 
			 	@SystemGUID, 
				@APICategoryGUID

 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.[SP_Insert_SystemAPICategory_Reln_Census]
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Insert_SystemAPICategory_Reln_Census] 
--Add the parameters for the stored procedure here
	@SystemGUID nvarchar(255) = NULL, 
	@APICategoryGUID nvarchar(255) = NULL

AS

DECLARE
	@System_ObjectId nvarchar(255) = '', 
	@APICategory_ObjectId nvarchar(255) = '';


PRINT 'Inside Insert SystemAPICategory';
PRINT 'SystemId = '+@SystemGUID;
PRINT 'APICategoryId = '+@APICategoryGUID;

DECLARE @New_ConnectorID INT = 0;
DECLARE @New_ConnectorGUID nvarchar(60) = '';


IF len(@SystemGUID) > 0 
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		Select @System_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @SystemGUID;
		Select @APICategory_ObjectId = Object_ID from SparxDB.dbo.t_object where ea_guid = @APICategoryGUID;


PRINT 'Start Reln processing'
		Insert Into SparxDB.dbo.t_connector (Name, Connector_Type, Stereotype, Direction,Start_Object_ID, End_Object_ID, ea_guid)
		Values (@System_ObjectId+' : '+@APICategory_ObjectId, 'Association', 'API Category','Unspecified', @System_ObjectId, @APICategory_ObjectId , CONCAT('{',NEWID(),'}'))
PRINT 'Connector created'

		Select @New_ConnectorID = Connector_ID, @New_ConnectorGUID = ea_guid from SparxDB.dbo.t_connector where [Start_Object_ID] = @System_ObjectId and [End_Object_ID] = @APICategory_ObjectId
			and Stereotype = 'API Category';
PRINT 'Connector = '+CAST(@New_ConnectorID AS NVARCHAR(10))

		INSERT INTO SparxDB.dbo.t_xref (XrefID, Name, Type, Visibility, Description, Client, Supplier)
		VALUES (CONCAT('{',NEWID(),'}'), 'Stereotypes', 'connector property', 'Public', '@STEREO;Name=API Category;FQName=CMS EA::API Category;@ENDSTEREO;', @New_ConnectorGUID, '<none>');
PRINT 'XREF created'

		-- Check the inserts

		--Select * from SparxDB.dbo.t_connector where Connector_ID = @New_ConnectorID; -- check connection record


	END TRY
	BEGIN CATCH

PRINT 'Inside Insert System APICategory Reln Exception';
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;



