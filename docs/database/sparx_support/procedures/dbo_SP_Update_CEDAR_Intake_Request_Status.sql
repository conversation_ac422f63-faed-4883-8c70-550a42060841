



/*********************************************************************************************
 * PROCEDURE_NAME: SP_Update_CEDAR_Intake_Request_Status
 *
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DATE:   05/15/2025
 *
 * DESCRIPTION/PURPOSE:
 *	Update the Intake_Request attributes in CEDAR_Support DB
 *
 * DEPENDENCY MATRIX:
 * ------------------------------------------------------------------------------------------
 * Item					Location								Purpose
 * 
 * ------------------------------------------------------------------------------------------
 *
 * EXAMPLE EXECUTION:
 *	EXECUTE @RC = [dbo].[SP_Update_CEDAR_Intake_Request_Status] 
 *	   @Intake_Request_IDs
 *	  ,@Status
 *
 * REMOVE PROC:
 *	DROP PROCEDURE dbo.SP_Update_CEDAR_Intake_Request_Status
 *
 * UPDATE HISTORY:
 * ------------------------------------------------------------------------------------------
 * Date			Changed By		Issue/Bug	Description
 *
 *
 *
 * ------------------------------------------------------------------------------------------
 ********************************************************************************************/
CREATE PROCEDURE [dbo].[SP_Update_CEDAR_Intake_Request_Status] 
--Add the parameters for the stored procedure here
       @Intake_Request_IDs nvarchar(4000) = '', 
       @Status nvarchar(255) = ''

AS
IF LEN(@Intake_Request_IDs) > 0
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN TRY

		
		;WITH IDs AS
		(
		SELECT RequestID = LTRIM(RTRIM(vals.node.value('(./text())[1]', 'nvarchar(4000)')))
		FROM (
			SELECT x = CAST('<root><data>' + REPLACE(@Intake_Request_IDs, ',', '</data><data>') + '</data></root>' AS XML).query('.')
			) v
		CROSS APPLY x.nodes('/root/data') vals(node)
		)
		UPDATE ir
		SET REQUEST_CEDAR_STATUS = @Status
		from IDs i
		join CEDAR_Support.CEDAR_API.INTAKE_REQUEST ir on ir.INTAKE_REQUEST_ID = i.RequestID;


	END TRY
	BEGIN CATCH

		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
		BEGIN
			ROLLBACK TRANSACTION;
		END
		
		--Report stored procedure failed
		RETURN (1);
	END CATCH
END;








