
/*********************************************************************************************
* PROCEDURE_NAME: SP_Update_DataCenterHostingEnvironments
*
* AUTHOR: <PERSON>
* DATE:   07/25/2023
*
* DESCRIPTION/PURPOSE:
*             Update System attributes in SparxDB 
 *
* DEPENDENCY MATRIX:
* ------------------------------------------------------------------------------------------
* Item                                                                  Location                                                                                                                               Purpose
* 
 * ------------------------------------------------------------------------------------------
*
* EXAMPLE EXECUTION:
*             EXECUTE   @RC = [dbo].[SP_Update_DataCenterHostingEnvironments] 
                        @DataCenterName,
						@Environment,
						@CloudMigrationPlan,
						@CloudMigratedDate,
						@SparxSystemGUID
*
* REMOVE PROC:
*             DROP PROCEDURE dbo.SP_Update_DataCenterHostingEnvironments
*
* UPDATE HISTORY:
* ------------------------------------------------------------------------------------------
* Date    Changed By            Issue/Bug            Description
*                                                                          
*                                                                                                                                                                              
*
* ------------------------------------------------------------------------------------------
********************************************************************************************/
CREATE       PROCEDURE .[dbo].[SP_Update_DataCenterHostingEnvironments] 
--Add the parameters for the stored procedure here
    @CloudMigrationPlan nvarchar(255) = NULL,  
    @CloudMigratedDate nvarchar(255) = NULL, 
	@SparxSystemGUID nvarchar(40) = NULL
                     
AS

PRINT 'Inside SP_Update_DataCenterHostingEnvironments';
DECLARE @RC INT = 0;

BEGIN

	BEGIN TRY

		IF len(ISNULL(@SparxSystemGUID,'')) = 0 
                                THROW 50001,'Input @SparxSystemGUID is empty',1;

		IF len(@SparxSystemGUID) > 0 
			BEGIN
				-- SET NOCOUNT ON added to prevent extra result sets from
				-- interfering with SELECT statements.
                SET NOCOUNT ON;

				PRINT 'Begin Update';
	
				PRINT '@SparxSystemGUID = '+@SparxSystemGUID;

                                                                
                UPDATE SparxDB.dbo.t_objectproperties 
				SET Value = 
								(CASE      
									WHEN Property = 'Cloud Migration Plan' THEN  @CloudMigrationPlan
									WHEN Property = 'Cloud Migrated Date' THEN @CloudMigratedDate
									ELSE Value
								END)
                FROM            SparxDB.dbo.t_object 
                INNER JOIN      SparxDB.dbo.t_objectproperties 
                                ON t_object.Object_ID = t_objectproperties.Object_ID
                WHERE           t_object.ea_guid = @SparxSystemGUID
                 and            t_object.Stereotype = 'System' --'Sparx_System_DataCenter_Full';

				PRINT 'End of update';

			END;

	END  TRY


	BEGIN CATCH

		PRINT 'Inside Update System Maintainer Error';
		
		-- Log the error
		EXEC dbo.SP_LogError;

		--If the transaction is in an uncommittable state, rollback the transaction
		IF (XACT_STATE() = -1)
			BEGIN	
				PRINT ' XACT_STATE() = -1'
				ROLLBACK TRANSACTION;
			END
                                
		--Report stored procedure failed
		RETURN (1); 
	END CATCH

END;



