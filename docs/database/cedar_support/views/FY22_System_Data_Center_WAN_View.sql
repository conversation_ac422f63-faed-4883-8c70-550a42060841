




CREATE VIEW [FY22].[System_Data_Center_WAN_View] 
AS

SELECT
	 [System Name]
	,[System UUID]
	,[Data Center Name]
	,[Data Center UUID]
	,[Data Center Relationship]
	,LTRIM(RTRIM(m.n.value('.[1]','varchar(8000)'))) AS [WAN Type]
	,ICTOBJECT
	FROM
	(
		SELECT   
				 [System Name] = a.NAME
				,[System UUID] = a.CMS_UUID
				,[Data Center Name] = dev.NAME
				,[Data Center UUID] = dev.CMS_UUID
				,[Data Center Relationship] =  h.SAG_DEPLOYMENTTYPE
				,a.ICTOBJECT
				,CAST('<XMLRoot><RowData>' + REPLACE(REPLACE(REPLACE(h.CMS_DATA_CENTER_WAN_TYPE,CHAR(13),','),CHAR(10),''),',','</RowData><RowData>') + '</RowData></XMLRoot>' AS XML) AS x
		from AlfabetDB.[dbo].[DEPLOYMENT] h
		join AlfabetDB.[dbo].[DEPLOYMENTELEMENT] de on de.DEPLOYMENT = h.REFSTR
		join AlfabetDB.[dbo].[DEVICE] dev on dev.REFSTR = de.DEVICE
		join AlfabetDB.[dbo].[APPLICATION] a on a.REFSTR = de.COMPONENT
		WHERE a.VERSION = '22'
		and a.OBJECTSTATE != 'Retired'
		and a.ICTOBJECT is NOT NULL

		--and a.CMS_NXT_SYS_SURVEY = 1
	) t
	CROSS APPLY x.nodes('/XMLRoot/RowData')m(n)












