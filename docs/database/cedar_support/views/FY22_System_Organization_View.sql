








CREATE VIEW [FY22].[System_Organization_View]
AS
--Select av.Name as [Target object name], av.[System Maintainer (Organization)] as [Source object name]
--from ARIS_Support.dbo.Output_Application_Report_View av
--WHERE av.[System Maintainer (Organization)] is NOT NULL
Select 
	 a.NAME as [System Name]
	,o.NAME as [Org Name]
	,rt.NAME as [Role Name]
	,o.CMS_COMPONENT
	,o.CMS_NAME_FULLPATH
	,a.REFSTR as [System REFSTR]
	,o.REFSTR as [Org REFSTR]
	,a.ICTOBJECT
from AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.ROLE r ON r.OBJECT = a.REFSTR
JOIN AlfabetDB.dbo.ORGAUNIT o on o.REFSTR = r.RESPONSIBLE
JOIN AlfabetDB.dbo.ROLETYPE rt on rt.REFSTR = r.ROLETYPE 
WHERE 
        a.VERSION = '22' 
	and a.OBJECTSTATE != 'Retired'
	and a.ICTOBJECT is NOT NULL


















