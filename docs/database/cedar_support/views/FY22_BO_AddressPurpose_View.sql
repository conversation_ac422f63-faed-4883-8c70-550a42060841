





CREATE VIEW [FY22].[BO_AddressPurpose_View]
AS
SELECT
NAME AS [System Name]
,REFSTR as [System REFSTR]
    ,LTRIM(RTRIM(m.n.value('.[1]','varchar(8000)'))) AS [CMS_BENEFICIARY_ADDRESS_PUR]
    FROM
    (
        SELECT  
                 a.NAME
                ,a.CMS_UUID
				, a.REFSTR 
				,CAST('<XMLRoot><RowData>' + REPLACE(REPLACE(REPLACE(a.[CMS_BENEFICIARY_ADDRESS_PUR],CHAR(13),','),CHAR(10),''),',','</RowData><RowData>') + '</RowData></XMLRoot>' AS XML) AS x
        from AlfabetDB.[dbo].[APPLICATION] a
        WHERE
        a.[CMS_BENEFICIARY_ADDRESS_PUR] IS NOT NULL
        and a.VERSION = '22' 
		and a.OBJECTSTATE != 'Retired'
		and a.ICTOBJECT is NOT NULL
		--and a.CMS_NXT_SYS_SURVEY = 1
    ) t
    CROSS APPLY x.nodes('/XMLRoot/RowData')m(n)











