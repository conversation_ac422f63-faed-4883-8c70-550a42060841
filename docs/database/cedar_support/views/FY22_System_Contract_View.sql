










CREATE VIEW [FY22].[System_Contract_View]
AS


WITH Towers AS
(
	Select c.REFSTR as CONTRACT_REFSTR, a.REFSTR as APPLICATION_REFSTR, c.NAME as CONTRACT_NAME, a.NAME as APPLICATION_NAME, TBM_Tower = ct.NAME
	from AlfabetDB.dbo.CONTRACT c
	JOIN AlfabetDB.dbo.CONTRACTDELIVERABLE cd on cd.CONTRACT = c.REFSTR
	JOIN AlfabetDB.dbo.APPLICATION a on a.REFSTR = cd.ARCHITECTUREELEMENT
	JOIN AlfabetDB.dbo.RELATIONS r on r.FROMREF = cd.REFSTR
	JOIN AlfabetDB.dbo.COSTTYPE ct on ct.REFSTR = r.TOREF
	where a.OBJECTSTATE = 'Active'
	and ct.BELONGSTO = (Select REFSTR from AlfabetDB.dbo.COSTTYPE where NAME = 'TBM Towers')
	--order by 4,3
),
CONTRACTTOWERS AS
(
Select CONTRACT_REFSTR, APPLICATION_REFSTR, CONTRACT_NAME, APPLICATION_NAME, 
TBMTower = STUFF(
                 (SELECT ',' + TBM_Tower FROM Towers t1 WHERE t1.CONTRACT_REFSTR = t2.CONTRACT_REFSTR and t1.APPLICATION_REFSTR = t2.APPLICATION_REFSTR  FOR XML PATH ('')), 1, 1, ''
               )
FROM Towers t2
GROUP BY CONTRACT_REFSTR, APPLICATION_REFSTR, CONTRACT_NAME, APPLICATION_NAME
),
Pools AS
(
	Select c.REFSTR as CONTRACT_REFSTR, a.REFSTR as APPLICATION_REFSTR, c.NAME as CONTRACT_NAME, a.NAME as APPLICATION_NAME, Cost_Pools = ct.NAME  
	from AlfabetDB.dbo.CONTRACT c
	JOIN AlfabetDB.dbo.CONTRACTDELIVERABLE cd on cd.CONTRACT = c.REFSTR
	JOIN AlfabetDB.dbo.APPLICATION a on a.REFSTR = cd.ARCHITECTUREELEMENT
	JOIN AlfabetDB.dbo.RELATIONS r on r.FROMREF = cd.REFSTR
	JOIN AlfabetDB.dbo.COSTTYPE ct on ct.REFSTR = r.TOREF
	where a.OBJECTSTATE = 'Active'
	and ct.BELONGSTO = (Select REFSTR from AlfabetDB.dbo.COSTTYPE where NAME = 'Cost Pools')
	--order by 4,3
),
CONTRACTPOOLS AS
(
Select CONTRACT_REFSTR, APPLICATION_REFSTR, CONTRACT_NAME, APPLICATION_NAME, 
CostPool = STUFF(
                 (SELECT ',' + Cost_Pools FROM Pools t1 WHERE t1.CONTRACT_REFSTR = t2.CONTRACT_REFSTR and t1.APPLICATION_REFSTR = t2.APPLICATION_REFSTR  FOR XML PATH ('')), 1, 1, ''
               )
FROM Pools t2
GROUP BY CONTRACT_REFSTR, APPLICATION_REFSTR, CONTRACT_NAME, APPLICATION_NAME
)
Select a.REFSTR as [System REFSTR], a.NAME as [System Name]
,c.CMS_INTER_AGREE_NUM as [Parent Award Id], c.CONTRACTNUMBER as [Award Id], c.NAME as Description, c.REFSTR as [Contract REFSTR], cd.REFSTR as [CD REFSTR]
,a.ICTOBJECT
,ct.TBMTower, cp.CostPool, c.STATUS as [Contract_Status]
from AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.CONTRACTDELIVERABLE cd ON cd.ARCHITECTUREELEMENT = a.REFSTR
JOIN AlfabetDB.dbo.CONTRACT c ON c.REFSTR = cd.CONTRACT
LEFT JOIN CONTRACTTOWERS ct ON c.REFSTR = ct.CONTRACT_REFSTR and a.REFSTR = ct.APPLICATION_REFSTR
LEFT JOIN CONTRACTPOOLS cp on c.REFSTR = cp.CONTRACT_REFSTR and a.REFSTR = cp.APPLICATION_REFSTR
WHERE 
a.VERSION = '22' 
and a.ICTOBJECT is NOT NULL



/*
Select a.REFSTR as [System REFSTR], a.NAME as [System Name]
,c.CMS_INTER_AGREE_NUM as [Parent Award Id], c.CONTRACTNUMBER as [Award Id], c.NAME as Description, c.REFSTR as [Contract REFSTR], cd.REFSTR as [CD REFSTR]
,a.ICTOBJECT
from AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.CONTRACTDELIVERABLE cd ON cd.ARCHITECTUREELEMENT = a.REFSTR
JOIN AlfabetDB.dbo.CONTRACT c ON c.REFSTR = cd.CONTRACT
WHERE 
a.VERSION = '22' 
and a.ICTOBJECT is NOT NULL
*/












