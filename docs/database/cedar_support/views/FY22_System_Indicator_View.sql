


CREATE VIEW [FY22].[System_Indicator_View]
AS
SELECT a.[REFSTR] as Application_REFSTR
      ,a.[NAME] as Application_Name
	  ,a.ACRONYM as Application_Acronym
	  ,a.ICTOBJECT
      ,it.REFSTR as IndicatorType_REFSTR
	  ,it.NAME as IndicatorType_Name
	  ,i.REFSTR as Indicator_REFSTR
	  ,i.NAME as Indicator_Name
	  ,i.SEMANTICVALUE 
	  ,i.VALUE as Indicator_Value
  FROM [AlfabetDB].[dbo].[APPLICATION] a
  JOIN [AlfabetDB].[dbo].[INDICATOR] i on i.OBJECT = a.REFSTR
  JOIN [AlfabetDB].[dbo].[INDICATORTYPE] it on it.REFSTR = i.INDICATORTYPE
  WHERE a.VERSION = '22'
  and a.OBJECTSTATE != 'Retired'
  and a.ICTOBJECT is NOT NULL
  --and a.[CMS_NXT_SYS_SURVEY] = 1







