






CREATE VIEW [FY22].[Census_System_List_View]
AS
SELECT   a.NAME
		,a.REFSTR as APPLICATION_REFSTR
		,a.ACRONYM
		,bo.<PERSON>AM<PERSON> as Business_Owner
		,bo.REFSTR AS Business_Owner_Org_REFSTR
		,sm.<PERSON>AM<PERSON> as System_Maintainer
		,sm.REFSTR AS System_Maintainer_Org_REFSTR
FROM AlfabetDB.dbo.APPLICATION a
LEFT JOIN 
	(	Select rbo.OBJECT, bo.CMS_COMPONENT as NAME, bo.REFSTR
		FROM AlfabetDB.dbo.ROLE rbo 
		JOIN AlfabetDB.dbo.ROLETYPE rtbo ON rbo.ROLETYPE = rtbo.REFSTR and rtbo.NAME = 'Business Owner'
		JOIN AlfabetDB.dbo.ORGAUNIT bo ON rbo.RESPONSIBLE = bo.REFSTR
	) bo ON a.REFSTR = bo.OBJECT
LEFT JOIN 
	(	Select rsm.OBJECT, sm.CMS_COMPONENT as NAME, sm.REFSTR
		FROM AlfabetDB.dbo.ROLE rsm 
		JOIN AlfabetDB.dbo.ROLETYPE rtsm ON rsm.ROLETYPE = rtsm.REFSTR and rtsm.NAME = 'System Maintainer'
		JOIN AlfabetDB.dbo.ORGAUNIT sm ON rsm.RESPONSIBLE = sm.REFSTR
	) sm ON a.REFSTR = sm.OBJECT
WHERE  a.CMS_NXT_SYS_SURVEY = 1
and a.VERSION = '22' 













