




CREATE view [dbo].[SYSTEM_STATUS_VIEW]
AS 

SELECT 
Title, 
ACRONYM,
Business_Owner,
System_Maintainer, 
PERCENT_COMPLETE = PERCENT_COMPLETE,
STATUS = CASE	WHEN HighLevelStatus = 5 THEN 'Issues Found' 
				WHEN HighLevelStatus = 4 THEN 'Ready to Submit' 
				WHEN HighLevelStatus = 3 THEN 'Started' 
				WHEN HighLevelStatus = 2 AND StartedS = 'Started' THEN 'Started'
				WHEN HighLevelStatus = 2 AND StartedS = '' THEN 'Not Started' 
				WHEN HighLevelStatus = 1 THEN 'Passed' 
				ELSE 'Not Started'
		  END
		  , 
ISNULL(QA_Contact,'') as QA_Contact,
ISNULL(DA_Contact,'') as DA_Contact, 
[Business Owner Basic Information],
[Contacts and Roles] , 
[Budget and Contracts], 
[System Maintainer Basic Information],
[Sub-Systems],
[URLs],
[Data Exchanges],
[Data Center Hosting Environments],
[Software Products]   
FROM   
	(
	select 
		aa.[System Name] as Title
		,aa.ACRONYM
		,pqa.[First Name]+' '+pqa.[Last Name] as QA_Contact
		,pda.[First Name]+' '+pda.[Last Name] as DA_Contact 
		,DISPLAY_PAGE_NAME = 
			CASE a.PAGE_NAME	WHEN 'ContactsAndRoles' THEN 'Contacts and Roles'
								WHEN 'BusinessOwnerBasicInformation' THEN 'Business Owner Basic Information'
								WHEN 'BudgetAndContracts' THEN 'Budget and Contracts'
								WHEN 'SystemMaintainerBasicInfo' THEN 'System Maintainer Basic Information'
								WHEN 'Sub-Systems' THEN 'Sub-Systems'
								WHEN 'Urls' THEN 'URLS'
								WHEN 'DataExchanges' THEN 'Data Exchanges'
								WHEN 'DataCenterHostingEnvironments' THEN 'Data Center Hosting Environments'
								WHEN 'SoftwareProducts' THEN 'Software Products'
			END
		,a.status 
		,bo.[Organization Component] as Business_Owner
		,sm.[Organization Component] as System_Maintainer
		,PERCENT_COMPLETE = CAST(ROUND(SUM(CASE WHEN a.status = 'Passed' THEN 11.11 ELSE 0 END) OVER (PARTITION BY aa.[System Name]),0) AS INT)
		,StartedS = MAX(CASE WHEN a.STATUS IN ('Issues Found','Ready to Submit','Started','Passed') THEN 'Started' ELSE '' END) OVER (PARTITION BY aa.[System Name])
		,HighLevelStatus = MAX(CASE WHEN a.STATUS = 'Issues Found' THEN 5 WHEN a.STATUS = 'Ready to Submit' THEN 4 WHEN a.STATUS = 'Started' THEN 3 WHEN a.STATUS = 'Not Started' THEN 2 ELSE 1 END) OVER (PARTITION BY aa.[System Name])
		from [CEDAR_Support].[System_Census].[SYSTEM_SURVEY_PAGE_STATUS] a  
		join Sparx_Support.dbo.Sparx_System aa ON aa.[Sparx System GUID] = a.System_ID and aa.[Current System Survey] = 'true'
		left join Sparx_Support.dbo.Sparx_System_Contact rqa on rqa.[Sparx System GUID] = a.System_ID and rqa.[Role Name] = 'QA Reviewer'
		left join Sparx_Support.dbo.Sparx_Person pqa on pqa.[Sparx Person GUID] = rqa.[Sparx Person GUID]
		left join Sparx_Support.dbo.Sparx_System_Contact rda on rda.[Sparx System GUID] = a.System_ID and rda.[Role Name] = 'DA Reviewer'
		left join Sparx_Support.dbo.Sparx_Person pda on pda.[Sparx Person GUID] = rda.[Sparx Person GUID]
		left join Sparx_Support.dbo.Sparx_System_BusinessOwner_Organization bo on bo.[Sparx System GUID] = a.System_ID
		left join Sparx_Support.dbo.Sparx_System_Maintainer_Organization sm on sm.[Sparx System GUID] = a.System_ID

	) t 
PIVOT  
	(  
		max(status)   
		FOR display_page_name IN  
		( [Contacts and Roles], 
		[Business Owner Basic Information], 
		[Budget and Contracts], 
		[System Maintainer Basic Information],
		[Sub-Systems],
		[URLs],
		[Data Exchanges],
		[Data Center Hosting Environments],
		[Software Products]
	)  
) AS pvt  
--order by Title; 







