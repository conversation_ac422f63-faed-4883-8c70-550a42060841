


CREATE VIEW [dbo].[System_POC] AS
SELECT DISTINCT
	   [System Name] = a.NAME
      ,[System ACRONYM] = ISNULL(a.ACRONYM,'')
      ,[System REFSTR] = a.REFSTR
      ,[Survey Contact Name] = ISNULL(p.NAME,'')+', '+ISNULL(p.FIRSTNAME,'')
	  ,[Survey Contact EUA] = p.USER_NAME
	  ,[Survey Contact Email] = p.EMAIL
      ,[Role Name] = rt.NAME
FROM AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.ROLE r on r.OBJECT = a.REFSTR --AND rt
JOIN AlfabetDB.dbo.ROLETYPE rt ON rt.REFSTR = r.[ROLETYPE]
JOIN AlfabetDB.dbo.PERSON p on p.REFSTR = r.[RESPONSIBLE]
WHERE rt.NAME = 'Survey Point of Contact'
and a.VERSION = '21'



