






CREATE VIEW [FY22].[Data_Exchange_Census_View]
AS

SELECT [Name] = ae.CMS_EXCHANGE
      ,[Retire Date] = ae.CMS_EXCHANGE_RETIRE_DATE
      ,[IE Agreement] = ae.CMS_IE_AGREEMENT
	  ,ae.OBJECTSTATE
	  ,ae.A_FROM as [Sender REFSTR]
	  ,ae.A_TO as [Receiver REFSTR]
	  ,ae.REFSTR as [Exchange REFSTR]
	  ,fa.NAME as [Sender]
	  ,fa.ACRONYM as [SenderAcronym]
	  ,'System' as SenderType
	  ,ta.NAME as [Receiver]
	  ,ta.ACRONYM as [ReceiverAcronym]
	  ,'System' as ReceiverType
	  ,ae.CMS_DATA_EXCH_BENEF_MAILING
	  ,ae.CMS_DATA_EXCH_BENEFICIARY_ADD
	  ,ae.CMS_DATA_EXCHANGE_BANKING
	  ,ae.CMS_DATA_EXCHANGE_PHI
	  ,ae.CMS_DATA_EXCHANGE_PII
	  ,CMS_EXCHANGE_FREQUENCY = REPLACE(REPLACE(ae.CMS_EXCHANGE_FREQUENCY,CHAR(13),','),CHAR(10),'')
	  ,ae.CMS_DATA_EXCH_API_DATA_SHARE
	  ,ae.CMS_DATA_EXCH_NUM_RECS
	  ,ae.CMS_DATA_EXCH_FORMAT
	  ,ae.CMS_DATA_EXCH_FORMAT_OTHER
	  ,[Primary Data Type] = r.NAME 
      ,[Exchange Description] = ae.DESCRIPTION
	  ,TECH_NAME = ae.NAME
	  ,fa.ICTOBJECT as [SenderICTOBJECT]
	  ,ta.ICTOBJECT as [ReceiverICTOBJECT]
FROM [AlfabetDB].[dbo].[INFORMATIONFLOW] ae
JOIN [AlfabetDB].[dbo].APPLICATION fa 
	ON fa.REFSTR = ae.A_FROM and fa.VERSION = '22'
	and fa.OBJECTSTATE != 'Retired'
	and fa.ICTOBJECT is NOT NULL
JOIN [AlfabetDB].[dbo].APPLICATION ta
	ON ta.REFSTR = ae.A_TO and ta.VERSION = '22'
	and ta.OBJECTSTATE != 'Retired'
	and ta.ICTOBJECT is NOT NULL
LEFT JOIN 
(
	Select r.FROMREF, d.NAME from
	AlfabetDB.dbo.RELATIONS r 
	JOIN AlfabetDB.dbo.DOMAIN d on d.REFSTR = r.TOREF
) r ON r.FROMREF = ae.REFSTR
where ae.VERSION = '22'
and ae.OBJECTSTATE = 'Active'
--and (fa.CMS_NXT_SYS_SURVEY = 1 OR ta.CMS_NXT_SYS_SURVEY = 1)

UNION ALL

SELECT [Name] = ae.CMS_EXCHANGE
      ,[Retire Date] = ae.CMS_EXCHANGE_RETIRE_DATE
      ,[IE Agreement] = ae.CMS_IE_AGREEMENT
	  ,ae.OBJECTSTATE
	  ,ae.A_FROM as [Sender REFSTR]
	  ,ae.A_TO as [Receiver REFSTR]
	  ,ae.REFSTR as [Exchange REFSTR]
	  ,fa.NAME as [Sender]
	  ,fa.ACRONYM as [SenderAcronym]
	  ,'System' as SenderType
	  ,ta.NAME as [Receiver]
	  ,'' as [ReceiverAcronym]
	  ,'Role' as ReceiverType
	  ,ae.CMS_DATA_EXCH_BENEF_MAILING
	  ,ae.CMS_DATA_EXCH_BENEFICIARY_ADD
	  ,ae.CMS_DATA_EXCHANGE_BANKING
	  ,ae.CMS_DATA_EXCHANGE_PHI
	  ,ae.CMS_DATA_EXCHANGE_PII
	  ,CMS_EXCHANGE_FREQUENCY = REPLACE(REPLACE(ae.CMS_EXCHANGE_FREQUENCY,CHAR(13),','),CHAR(10),'')
	  ,ae.CMS_DATA_EXCH_API_DATA_SHARE
	  ,ae.CMS_DATA_EXCH_NUM_RECS
	  ,ae.CMS_DATA_EXCH_FORMAT
	  ,ae.CMS_DATA_EXCH_FORMAT_OTHER
	  ,[Primary Data Type] = r.NAME 
      ,[Exchange Description] = ae.DESCRIPTION
	  ,TECH_NAME = ae.NAME
	  ,fa.ICTOBJECT as [SenderICTOBJECT]
	  ,NULL as [ReceiverICTOBJECT]
FROM [AlfabetDB].[dbo].[INFORMATIONFLOW] ae
JOIN [AlfabetDB].[dbo].APPLICATION fa 
	ON fa.REFSTR = ae.A_FROM and fa.VERSION = '22'
	and fa.OBJECTSTATE != 'Retired'
	and fa.ICTOBJECT is NOT NULL
JOIN [AlfabetDB].[dbo].PERIPHERAL ta
	ON ta.REFSTR = ae.A_TO
LEFT JOIN 
(
	Select r.FROMREF, d.NAME from
	AlfabetDB.dbo.RELATIONS r 
	JOIN AlfabetDB.dbo.DOMAIN d on d.REFSTR = r.TOREF
) r ON r.FROMREF = ae.REFSTR
where ae.VERSION = '22'
and ae.OBJECTSTATE = 'Active'

--and fa.CMS_NXT_SYS_SURVEY = 1

UNION ALL

SELECT [Name] = ae.CMS_EXCHANGE
      ,[Retire Date] = ae.CMS_EXCHANGE_RETIRE_DATE
      ,[IE Agreement] = ae.CMS_IE_AGREEMENT
	  ,ae.OBJECTSTATE
	  ,ae.A_FROM as [Sender REFSTR]
	  ,ae.A_TO as [Receiver REFSTR]
	  ,ae.REFSTR as [Exchange REFSTR]
	  ,fa.NAME as [Sender]
	  ,'' as [SenderAcronym]
	  ,'Role' as SenderType
	  ,ta.NAME as [Receiver]
	  ,ta.ACRONYM as [ReceiverAcronym]
	  ,'System' as ReceiverType
	  ,ae.CMS_DATA_EXCH_BENEF_MAILING
	  ,ae.CMS_DATA_EXCH_BENEFICIARY_ADD
	  ,ae.CMS_DATA_EXCHANGE_BANKING
	  ,ae.CMS_DATA_EXCHANGE_PHI
	  ,ae.CMS_DATA_EXCHANGE_PII
	  ,CMS_EXCHANGE_FREQUENCY = REPLACE(REPLACE(ae.CMS_EXCHANGE_FREQUENCY,CHAR(13),','),CHAR(10),'')
	  ,ae.CMS_DATA_EXCH_API_DATA_SHARE
	  ,ae.CMS_DATA_EXCH_NUM_RECS
	  ,ae.CMS_DATA_EXCH_FORMAT
	  ,ae.CMS_DATA_EXCH_FORMAT_OTHER
	  ,[Primary Data Type] = r.NAME 
      ,[Exchange Description] = ae.DESCRIPTION
	  ,TECH_NAME = ae.NAME
	  ,NULL as [SenderICTOBJECT]
	  ,ta.ICTOBJECT as [ReceiverICTOBJECT]
FROM [AlfabetDB].[dbo].[INFORMATIONFLOW] ae
JOIN [AlfabetDB].[dbo].PERIPHERAL fa 
	ON fa.REFSTR = ae.A_FROM 
JOIN [AlfabetDB].[dbo].APPLICATION ta
	ON ta.REFSTR = ae.A_TO and ta.VERSION = '22'
	and ta.OBJECTSTATE != 'Retired'
	and ta.ICTOBJECT is NOT NULL
LEFT JOIN 
(
	Select r.FROMREF, d.NAME from
	AlfabetDB.dbo.RELATIONS r 
	JOIN AlfabetDB.dbo.DOMAIN d on d.REFSTR = r.TOREF
) r ON r.FROMREF = ae.REFSTR
where ae.VERSION = '22'
and ae.OBJECTSTATE = 'Active'

--and ta.CMS_NXT_SYS_SURVEY = 1





















