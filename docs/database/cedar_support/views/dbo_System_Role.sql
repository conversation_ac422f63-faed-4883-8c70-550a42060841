



CREATE VIEW [dbo].[System_Role] AS
SELECT DISTINCT
	   [System Name] = a.NAME
      ,[System ACRONYM] = ISNULL(a.ACRONYM,'')
      ,[System REFSTR] = a.REFSTR
      ,[Role Name] = rt.NAME
      ,[Person Name] = ISNULL(p.NAME,'')+', '+ISNULL(p.FIRSTNAME,'')
	  ,[Person EUA] = p.USER_NAME
	  ,[Person Email] = ISNULL(p.EMAIL,'')
FROM AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.ROLE r on r.OBJECT = a.REFSTR --AND rt
JOIN AlfabetDB.dbo.ROLETYPE rt ON rt.REFSTR = r.[ROLETYPE]
JOIN AlfabetDB.dbo.PERSON p on p.REFSTR = r.[RESPONSIBLE]
WHERE rt.NAME IN ('Business Owner','System Maintainer')
AND a.VERSION = '21'
and a.CMS_NXT_SYS_SURVEY = 1



