

CREATE VIEW [FY22].[System_Domain_View]
AS
SELECT a.[REFSTR] as Application_REFSTR
	  ,a.ICTOBJECT
      ,a.[NAME] as Application_Name
	  ,a.ACRONYM as Application_Acronym
	  ,d.REFSTR as Domain_REFSTR
	  ,d.NAME as Domain_Name
	  ,d.STEREOTYPE 
  FROM [AlfabetDB].[dbo].[APPLICATION] a
  JOIN AlfabetDB.dbo.RELATIONS rel ON rel.TOREF = a.REFSTR
  JOIN [AlfabetDB].[dbo].[DOMAIN] d on d.REFSTR = rel.FROMREF
  WHERE a.VERSION = '22'
  and a.OBJECTSTATE != 'Retired'
  and a.ICTOBJECT is NOT NULL
--  and a.[CMS_NXT_SYS_SURVEY] = 1






