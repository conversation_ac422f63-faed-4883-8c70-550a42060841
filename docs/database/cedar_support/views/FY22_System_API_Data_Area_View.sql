




CREATE VIEW [FY22].[System_API_Data_Area_View]
AS
SELECT
	 [System Name] = NAME
	,[System REFSTR] = REFSTR
	,ICTOBJECT
    ,LTRIM(RTRIM(m.n.value('.[1]','varchar(8000)'))) AS [CMS_API_DATA_AREA]
    FROM
    (
        SELECT  
                 a.NAME
                ,a.CMS_UUID
				,a.REFSTR 
				,a.ICTOBJECT
                ,CAST('<XMLRoot><RowData>' + REPLACE(REPLACE(REPLACE(REPLACE(a.[CMS_API_DATA_AREA],CHAR(13),','),CHAR(10),''),'|',','),',','</RowData><RowData>') + '</RowData></XMLRoot>' AS XML) AS x
        from AlfabetDB.[dbo].[APPLICATION] a
        WHERE
        a.[CMS_API_DATA_AREA] IS NOT NULL
        and a.VERSION = '22' 
		and a.OBJECTSTATE != 'Retired'
		and a.ICTOBJECT is NOT NULL
		--and a.CMS_NXT_SYS_SURVEY = 1
    ) t
    CROSS APPLY x.nodes('/XMLRoot/RowData')m(n)










