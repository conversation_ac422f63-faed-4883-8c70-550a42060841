














CREATE VIEW [FY22].[System_Maint_Basic_View]
AS
select 
[System Name] = a.NAME
,[System REFSTR] = a.REFSTR
,a.Version as System_Version
,a.[CMS_SYSTEM_CUSTOMIZATION]
,a.[CMS_SYSTEM_SENDSRECEIVES_EMAIL]
,a.[CMS_SYSTEM_SUPPORT_NAME]
,a.[CMS_SYS_SUPPORT_CONTACT_TITLE]
,a.[CMS_SYSTEM_SUPPORT_EMAIL]
,a.[CMS_SYSTEMSUPPORT_URL]
,a.[CMS_SYSTEM_SUPPORT_PHONE]
,a.[CMS_APIS_DEVELOPED]
,a.[CMS_APIS_ACCESSIBILITY]
,a.[CMS_AI_PLAN]
,a.[CMS_AT_REST_DATA_ENCRY]
,a.[CMS_IN_MOTION_DATA_ENCRY]
,a.[CMS_DATA_ENCRYP_MANAG]
,a.[CMS_IS_SUBSYSTEM]
,ap.NAME as <PERSON><PERSON><PERSON><PERSON><PERSON>
,ap.VERSION as <PERSON>rentVers<PERSON>
,ap.REFSTR as Parent_REFSTR
,a.<PERSON>NDDATE
,a.OBJECTSTATE
,a.ICTOBJECT
,a.CMS_FRONT_END_ACCESS_TYPE
,a.CMS_NET_ACCESSIBILITY_TYPE
,a.CMS_IP_ENABLED_ASSET_COUNT
,a.CMS_PERCENT_IPV6
,a.CMS_LONG_TERM_IPV6_PLAN
,a.CMS_HARD_CODED_IP_ADDRESS
,a.CMS_E_CAP_PARTICIPATION
,a.CMS_DEV_COMPLETE_PERCENT
,a.CMS_DEVELOPMENT_WORK
,a.CMS_AGILE_METHODOLOGY_USE
,a.CMS_PLANNING_RETIREMENT_QUART
,a.CMS_RETIREDORREPLACE_DATE
,a.CMS_BUS_ARTIFACTS_ON_DEMAND
,a.CMS_REQ_ON_DEMAND
,a.[CMS_SYS_DESIGN_ON_DEMAND]
,a.[CMS_SOURCE_CODE_ON_DEMAND]
,a.[CMS_TEST_PLAN_ON_DEMAND]
,a.[CMS_TEST_SCRIPT_ON_DEMAND]
,a.[CMS_TEST_REPORTS_ON_DEMAND]
,a.[CMS_OPS_MAINT_ON_DEMAND]
,i.STARTDATE as [System Production Date]
,a.CMS_RECORDS_MANAGEMENT_BUCKET
from AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.ICTOBJECT i on i.REFSTR = a.ICTOBJECT
LEFT JOIN AlfabetDB.dbo.APPLICATION ap ON ap.REFSTR = a.CMS_PARENT_SYSTEM
WHERE a.VERSION = '22'
and a.OBJECTSTATE != 'Retired'
and a.ICTOBJECT is NOT NULL

--and a.CMS_NXT_SYS_SURVEY = 1




















