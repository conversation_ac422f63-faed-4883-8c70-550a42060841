

CREATE VIEW [FY22].[Data_Exchange_Expected_View]
AS
SELECT [Name] = ae.CMS_EXCHANGE
      ,[Retire Date] = ae.CMS_EXCHANGE_RETIRE_DATE
      ,[IE Agreement] = ae.CMS_IE_AGREEMENT
	  ,ae.OBJECTSTATE
	  ,ae.A_FROM as [Sender REFSTR]
	  ,ae.A_TO as [Receiver REFSTR]
	  ,ae.REFSTR as [Exchange REFSTR]
	  ,fa.NAME as [Sender]
	  ,fa.ACRONYM as [SenderAcronym]
	  ,'System' as SenderType
	  ,ta.NAME as [Receiver]
	  ,ta.ACRONYM as [ReceiverAcronym]
	  ,'System' as ReceiverType
	  ,ae.CMS_DATA_EXCH_BENEF_MAILING
	  ,ae.CMS_DATA_EXCH_BENEFICIARY_ADD
	  ,ae.CMS_DATA_EXCHANGE_BANKING
	  ,ae.CMS_DATA_EXCHANGE_PHI
	  ,ae.CMS_DATA_EXCHANGE_PII
	  ,ae.CMS_DATA_EXCH_API_DATA_SHARE
	  ,ae.CMS_DATA_EXCH_FORMAT
	  ,ae.CMS_DATA_EXCH_FORMAT_OTHER
	  ,ae.CMS_DATA_EXCH_NUM_RECS
	  ,CMS_EXCHANGE_FREQUENCY = REPLACE(REPLACE(ae.CMS_EXCHANGE_FREQUENCY,CHAR(13),','),CHAR(10),'')
      ,[Exchange Description] = ae.DESCRIPTION
	  ,TECH_NAME = ae.NAME
	  ,fa.ICTOBJECT as [SenderICTOBJECT]
	  ,ta.ICTOBJECT as [ReceiverICTOBJECT]
FROM [AlfabetDB].[dbo].[INFORMATIONFLOW] ae
JOIN [AlfabetDB].[dbo].APPLICATION fa 
	ON fa.REFSTR = ae.A_FROM --and fa.VERSION = '21'
JOIN [AlfabetDB].[dbo].APPLICATION fa22
	ON fa22.ICTOBJECT = fa.ICTOBJECT and fa22.VERSION = '22'
JOIN [AlfabetDB].[dbo].APPLICATION ta
	ON ta.REFSTR = ae.A_TO --and ta.VERSION = '21'
JOIN [AlfabetDB].[dbo].APPLICATION ta22
	ON ta22.ICTOBJECT = ta.ICTOBJECT and ta22.VERSION = '22'
where ae.VERSION = '21'
and ae.OBJECTSTATE = 'Active'

UNION ALL

SELECT [Name] = ae.CMS_EXCHANGE
      ,[Retire Date] = ae.CMS_EXCHANGE_RETIRE_DATE
      ,[IE Agreement] = ae.CMS_IE_AGREEMENT
	  ,ae.OBJECTSTATE
	  ,ae.A_FROM as [Sender REFSTR]
	  ,ae.A_TO as [Receiver REFSTR]
	  ,ae.REFSTR as [Exchange REFSTR]
	  ,fa.NAME as [Sender]
	  ,fa.ACRONYM as [SenderAcronym]
	  ,'System' as SenderType
	  ,ta.NAME as [Receiver]
	  ,'' as [ReceiverAcronym]
	  ,'Role' as ReceiverType
	  ,ae.CMS_DATA_EXCH_BENEF_MAILING
	  ,ae.CMS_DATA_EXCH_BENEFICIARY_ADD
	  ,ae.CMS_DATA_EXCHANGE_BANKING
	  ,ae.CMS_DATA_EXCHANGE_PHI
	  ,ae.CMS_DATA_EXCHANGE_PII
	  ,ae.CMS_DATA_EXCH_API_DATA_SHARE
	  ,ae.CMS_DATA_EXCH_FORMAT
	  ,ae.CMS_DATA_EXCH_FORMAT_OTHER
	  ,ae.CMS_DATA_EXCH_NUM_RECS
	  ,CMS_EXCHANGE_FREQUENCY = REPLACE(REPLACE(ae.CMS_EXCHANGE_FREQUENCY,CHAR(13),','),CHAR(10),'')
      ,[Exchange Description] = ae.DESCRIPTION
	  ,TECH_NAME = ae.NAME
	  ,fa.ICTOBJECT as [SenderICTOBJECT]
	  ,NULL as [ReceiverICTOBJECT]
FROM [AlfabetDB].[dbo].[INFORMATIONFLOW] ae
JOIN [AlfabetDB].[dbo].APPLICATION fa 
	ON fa.REFSTR = ae.A_FROM --and fa.VERSION = '21'
JOIN [AlfabetDB].[dbo].APPLICATION fa22
	ON fa22.ICTOBJECT = fa.ICTOBJECT and fa22.VERSION = '22'
JOIN [AlfabetDB].[dbo].PERIPHERAL ta
	ON ta.REFSTR = ae.A_TO
where ae.VERSION = '21'
--and fa.CMS_NXT_SYS_SURVEY = 1
and ae.OBJECTSTATE = 'Active'

UNION ALL

SELECT [Name] = ae.CMS_EXCHANGE
      ,[Retire Date] = ae.CMS_EXCHANGE_RETIRE_DATE
      ,[IE Agreement] = ae.CMS_IE_AGREEMENT
	  ,ae.OBJECTSTATE
	  ,ae.A_FROM as [Sender REFSTR]
	  ,ae.A_TO as [Receiver REFSTR]
	  ,ae.REFSTR as [Exchange REFSTR]
	  ,fa.NAME as [Sender]
	  ,'' as [SenderAcronym]
	  ,'Role' as SenderType
	  ,ta.NAME as [Receiver]
	  ,ta.ACRONYM as [ReceiverAcronym]
	  ,'System' as ReceiverType
	  ,ae.CMS_DATA_EXCH_BENEF_MAILING
	  ,ae.CMS_DATA_EXCH_BENEFICIARY_ADD
	  ,ae.CMS_DATA_EXCHANGE_BANKING
	  ,ae.CMS_DATA_EXCHANGE_PHI
	  ,ae.CMS_DATA_EXCHANGE_PII
	  ,ae.CMS_DATA_EXCH_API_DATA_SHARE
	  ,ae.CMS_DATA_EXCH_FORMAT
	  ,ae.CMS_DATA_EXCH_FORMAT_OTHER
	  ,ae.CMS_DATA_EXCH_NUM_RECS
	  ,CMS_EXCHANGE_FREQUENCY = REPLACE(REPLACE(ae.CMS_EXCHANGE_FREQUENCY,CHAR(13),','),CHAR(10),'')
      ,[Exchange Description] = ae.DESCRIPTION
	  ,TECH_NAME = ae.NAME
	  ,NULL as [SenderICTOBJECT]
	  ,ta.ICTOBJECT as [ReceiverICTOBJECT]
FROM [AlfabetDB].[dbo].[INFORMATIONFLOW] ae
JOIN [AlfabetDB].[dbo].PERIPHERAL fa 
	ON fa.REFSTR = ae.A_FROM 
JOIN [AlfabetDB].[dbo].APPLICATION ta
	ON ta.REFSTR = ae.A_TO --and ta.VERSION = '21'
JOIN [AlfabetDB].[dbo].APPLICATION ta22
	ON ta22.ICTOBJECT = ta.ICTOBJECT and ta22.VERSION = '22'
where ae.VERSION = '21'
and ae.OBJECTSTATE = 'Active'





