





CREATE VIEW [FY22].[System_View]
AS
SELECT [REFSTR]
      ,[ID]
      ,[NAME]
      ,[DESCRIPTION]
      ,[SHORT<PERSON><PERSON>]
      ,[ICTOBJECT]
      ,[VERSION]
      ,[NEXTVERSION]
      ,[PREVIOUSVERSION]
      ,[STAR<PERSON><PERSON><PERSON>]
      ,[ENDDATE]
      ,[OBJECTSTATE]
      ,[CMS_ICD9_OR_ICD10]
      ,[CMS_PIA_AVAILABLE]
      ,[CMS_SYSTEM_COMMENT]
      ,[CMS_RECONCILE_COMMENT]
      ,[CMS_IDENTITY_PROOFING]
      ,[CMS_DR_PLAN]
      ,[CMS_BIA_PLAN]
      ,[CMS_BC_PLAN]
      ,[CMS_OPERATIONS_MANUAL]
      ,[CMS_SYSTEM_BACKUP_FREQUENCY]
      ,[CMS_LAST_BIA_UPDATE]
      ,[CMS_LAST_DR_PLAN_UPDATE]
      ,[CMS_DR_EXERCISE]
      ,[CMS_DR_EXERCISE_DATE]
      ,[CMS_DR_EXERCISE_TYPE]
      ,[CMS_DR_EXERCISE_WEAKNESS]
      ,[CMS_INTERFACE_USE]
      ,[CMS_APIS_DEVELOPED]
      ,[CMS_APIS_ACCESSIBILITY]
      ,[CMS_EMERGING_TECHNOLOGIES]
      ,[CMS_UUID]
      ,[CMS_BUSINESS_APPLICATION]
      ,[CMS_SUPPORTING_APPLICATION]
      ,[CMS_CONTRACTOR_OWNED_SYSTEM]
      ,[CMS_PARENT_IT_SYSTEM]
      ,[CMS_PARENT_SYSTEM]
      ,[CMS_SYSTEM_TYPE]
      ,[CMS_OWNED_SYSTEM]
      ,[CMS_SYSTEM_CUSTOMIZATION]
      ,[CMS_PRIMARY_MULTIFACTOR_AUTH]
      ,[CMS_PRIVILEGED_MULTIFACTORAUTH]
      ,[CMS_MAXIMUM_TOLERABLE_DOWNTIME]
      ,[CMS_RECOVERY_POINT_OBJECTIVE]
      ,[CMS_RECOVERY_TIME_OBJECTIVE]
      ,[CMS_508_REVIEW_CONDUCTED]
      ,[CMS_508_REVIEW_SCORE]
      ,[CMS_SENSITIVE_INFO]
      ,[CMS_HIGHERLEVEL_SENSITIVE_INFO]
      ,[CMS_NUM_RECORDS_SENSITIVE_INFO]
      ,[CMS_NUM_INDV_SENSITIVE_INFO]
      ,[CMS_SYSTEM_COST_PER_YEAR]
      ,[CMS_508_REVIEW_EXCEPTION]
      ,[CMS_EXTERNALSYSTEM]
      ,[CMS_LEGACYITSYSTEMWAVENUMBER]
      ,[CMS_BIADETERMINATIONDOCUMENT]
      ,[CMS_APISDEVELOPMENTPLANS]
      ,[CMS_HIGHVALUEASSET]
      ,[CMS_SOFTWARELICENSEAGREEMENT]
      ,[CMS_SYSTEMCLASSIFICATION]
      ,[CMS_CLOUDSERVICEMODEL]
      ,[CMS_MIGRATEDTOCLOUD]
      ,[CMS_VENDOR]
      ,[CMS_CLOUDMIGRATIONPLAN]
      ,[CMS_ARM]
      ,[CMS_BRM]
      ,[CMS_DRM]
      ,[CMS_ALIAS_1]
      ,[CMS_ALIAS_2]
      ,[CMS_ALIAS_3]
      ,[CMS_IE_AGREEMENT]
      ,[CMS_SYSTEM_PII]
      ,[CMS_SYSTEM_SENDSRECEIVES_EMAIL]
      ,[CMS_LAST_XLC_PROJECTID]
      ,[CMS_RETIREDATECONFIRMATION]
      ,[CMS_PRODSTARTDATECONFIRMED]
      ,[CMS_MAJORCHANGES]
      ,[CMS_MAJOR_CHANGES_STARTDATE]
      ,[CMS_MAJOR_CHANGES_ENDDATE]
      ,[CMS_DR_PLAN_LOCATION]
      ,[CMS_BIA_LOCATION]
      ,[CMS_BCP_LOCATION]
      ,[CMS_SYSTEM_BACKUP_LOCATION]
      ,[CMS_DATE_BIA_UPDATED]
      ,[CMS_AGILE_METHODOLOGY_USE]
      ,[CMS_IMPROVEMENT_TYPE]
      ,[CMS_IMPROVEMENT_STARTDATE]
      ,[CMS_IMPROVEMENT_FINISHDATE]
      ,[CMS_SYSTEM_SUPPORT_EMAIL]
      ,[CMS_SYSTEMSUPPORT_URL]
      ,[CMS_SYSTEM_SUPPORT_PHONE]
      ,[CMS_SYSTEM_SUPPORT_OTHER_INFO]
      ,[CMS_OTHER_USER_MFA]
      ,[CMS_PRIVILEGEDUSERMFACOUNT]
      ,[CMS_PRIVILEGEDUSERMFA_ACCESS]
      ,[CMS_ASSET_AGREEMENT]
      ,[CMS_ARTIFICIAL_INTELLIGENCE]
      ,[CMS_AI_OTHER_DESCRIPTION]
      ,[CMS_AI_PLAN]
      ,[CMS_AGREEMENTSOFTWARE]
      ,[CMS_COBOL_LOC]
      ,[CMS_MAJORTECHREFRESHDATE]
      ,[CMS_NEXTMAJORREFRESHDATE]
      ,[CMS_FEA_ARM]
      ,[CMS_FEA_BRM]
      ,[CMS_FEA_DRM]
      ,[CMS_BUSINESSPROGRAMAREA]
      ,[CMS_REPLACING_SYSTEM]
      ,[CMS_API_DATA_AREA]
      ,[CMS_ADDITIONAL_COMMENTS]
      ,[CMS_NAVIGATOR_NAME]
      ,[CMS_TLC_BASIC_INFORMATION_ID]
      ,[CMS_TLC_PROFILE_LAST_UPDT_DATE]
      ,[CMS_NEXT_PLAND_PROD_REL_DTE]
      ,[CMS_RELEASE_DESCRIPTION]
      ,[CMS_CHANGES_2_GPR_4_NXT_REL]
      ,[CMS_508_REVIEW_DATE]
      ,[CMS_DELETE]
      ,[CMS_BUISNESS_ARTIFACT_LOC]
      ,[CMS_BUSINESS_ARTIFACTS_MNGT]
      ,[CMS_REQUIREMENT_LOCATION]
      ,[CMS_REQUIREMENTS_MANAGEMENT]
      ,[CMS_SYSTEMDESIGNLOCATION]
      ,[CMS_SYSTEMDESIGNMANAGEMENT]
      ,[CMS_SOURCECODELOCATION]
      ,[CMS_SOURCECODEMANAGEMENT]
      ,[CMS_TESTPLANLOCATION]
      ,[CMS_TESTPLANMANAGEMENT]
      ,[CMS_TESTSCRIPTSLOCATION]
      ,[CMS_TESTSCRIPTMANAGEMENT]
      ,[CMS_TESTREPORTSLOCATION]
      ,[CMS_TESTREPORTSMANAGEMENT]
      ,[CMS_OPSANDMAINTLOCATION]
      ,[CMS_OPSANDMAINTMANAGEMENT]
      ,[ACRONYM]
      ,[CMS_CONTING_PLAN_COMPLET_DATE]
      ,[CMS_CONTING_PLAN_DOCU]
      ,[CMS_CNT_TOT_NON_PRIV_USER_POP]
      ,[CMS_CNT_TOT_PRIVI_USER_POP]
      ,[CMS_CONT_TOT_USER_POP]
      ,[CMS_E_AUTHENTICATION_LEVEL]
      ,[CMS_INFO_SYST_OR_PROG_NAME]
      ,[CMS_IS_DATA_MAINT_IN_FISMA]
      ,[CMS_PII_SYS_MAINT_USERNAME]
      ,[CMS_IS_SYS_ACCESS_NON_ORG_USE]
      ,[CMS_PIA_COMPLETION_DATE]
      ,[CMS_XLC_PHASE]
      ,[CMS_DEV_COMPLETE]
      ,[CMS_DEV_COMPLETE_PERCENT]
      ,[CMS_SYS_SUPPORT_CONTACT_TITLE]
      ,[CMS_CONFIDENCE_LEVEL]
      ,[CMS_CLOUD_MIGRATED_DATE]
      ,[CMS_NXT_SYS_SURVEY]
      ,[ARIS_GUID]
      ,[CMS_CENSUS_FIS_YEAR]
      ,[CMS_MAJOR_RELEASE]
      ,[CMS_BUS_ARTIFACTS_ON_DEMAND]
      ,[CMS_REQ_ON_DEMAND]
      ,[CMS_SYS_DESIGN_ON_DEMAND]
      ,[CMS_SOURCE_CODE_ON_DEMAND]
      ,[CMS_TEST_PLAN_ON_DEMAND]
      ,[CMS_TEST_SCRIPT_ON_DEMAND]
      ,[CMS_TEST_REPORTS_ON_DEMAND]
      ,[CMS_OPS_MAINT_ON_DEMAND]
      ,[CMS_DEVELOPMENT_WORK]
      ,[CMS_BENEFICIARY_ADDRESS]
      ,[CMS_BENEFICIARY_ADDRESS_PUR]
      ,[CMS_BANKING_DATA]
      ,[CMS_AT_REST_DATA_ENCRY]
      ,[CMS_IN_MOTION_DATA_ENCRY]
      ,[CMS_DATA_ENCRYP_MANAG]
      ,[CMS_RETIREDORREPLACE_DATE]
      ,[CMS_SYSTEM_SUPPORT_NAME]
      ,[CMS_PLANNING_RETIREMENT_QUART]
      ,[CMS_RETIRE_OR_REPLACE]
      ,[CMS_IS_SUBSYSTEM]
      ,[CMS_REST_UPDATED_USER]
      ,[CMS_REST_LAST_UPDATED_DATE]
      ,[CMS_REASON_ATO]
      ,[CMS_ATO_DESC]
      ,[CMS_BENEFICIARY_ADDRESS_SRC]
      ,[CMS_BENEFICIARY_ADDR_SRC_OTHER]
      ,[CMS_FRONT_END_ACCESS_TYPE]
      ,[CMS_NET_ACCESSIBILITY_TYPE]
      ,[CMS_IP_ENABLED_ASSET_COUNT]
      ,[CMS_PERCENT_IPV6]
      ,[CMS_LONG_TERM_IPV6_PLAN]
      ,[CMS_HARD_CODED_IP_ADDRESS]
      ,[CMS_DEPLOYMENT_FREQ]
      ,[CMS_RECORDS_MANAGEMENT_BUCKET]
      ,[CMS_E_CAP_PARTICIPATION]
      ,[CMS_BENEFICIARY_ADDR_PUR_OTHER]
      ,[MANDATEMASK]
      ,[ALFA_INSTID]
	  ,[CMS_API_FHIR_USE]
	  ,[CMS_API_FHIR_USE_OTHER]
	  ,[CMS_SYSTEM_HAS_API_GATEWAY]
  FROM [AlfabetDB].[dbo].[APPLICATION]
  WHERE VERSION = '22'
  and OBJECTSTATE != 'Retired'
  and ICTOBJECT is NOT NULL
  --and [CMS_NXT_SYS_SURVEY] = 1








