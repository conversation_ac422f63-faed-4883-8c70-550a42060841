






CREATE VIEW [FY22].[System_URL_View]
AS

SELECT [System Name] = a.[NAME]
	  ,[System REFSTR] = a.REFSTR
      ,[URL] = u.LINK
      ,[URL API Endpoint] = u.CMS_URL_API_ENDPOINT
      ,[URL API AWF] = u.CMS_URL_API_AWF
	  ,[URL REFSTR] = u.REFSTR
	  ,a.ICTOBJECT
	  , u.CMS_PROVIDES_VER_CODE_ACCESS
	  , u.CMS_URL_HOSTING_ENV
  FROM [AlfabetDB].[dbo].[ALFA_URI] u
  JOIN [AlfabetDB].[dbo].[APPLICATION] a on a.REFSTR = u.OBJECT
  WHERE a.VERSION = '22'
  and a.OBJECTSTATE != 'Retired'
  and a.ICTOBJECT is NOT NULL
and a.CMS_NXT_SYS_SURVEY = 1










