

CREATE VIEW [FY22].[System_SubSystems_View]
AS
select 
 a.<PERSON>AM<PERSON> as [System Name]
,a.REFSTR as [System REFSTR]
,ac.NAME as [Sub-System Name]
,ac.ACRONYM as [Sub-System Acronym]
,ac.CMS_RETIREDORREPLACE_DATE
,ac.CMS_PLANNING_RETIREMENT_QUART
,ac.DESCRIPTION as [Component Description]
, [Sub-System Retirement Date] = IIF((ac.CMS_RETIREDORREPLACE_DATE is not null and ac.CMS_PLANNING_RETIREMENT_QUART is not null), CONCAT(ac.CMS_PLANNING_RETIREMENT_QUART, ' Quarter of Year ', ac.CMS_RETIREDORREPLACE_DATE), '')
,a.ICTOBJECT
--,ar.NAME as [Replacing System]
from AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.APPLICATION ac ON a.REFSTR = ac.CMS_PARENT_SYSTEM
--LEFT JOIN AlfabetDB.dbo.APPLICATION ar ON ar.REFSTR = ac.CMS_REPLACING_SYSTEM
WHERE a.VERSION = '22'
and ac.VERSION = '22'
and ac.OBJECTSTATE != 'Retired'
and a.OBJECTSTATE != 'Retired'
and a.ICTOBJECT is NOT NULL
--and a.CMS_NXT_SYS_SURVEY = 1

--and a.CMS_NXT_SYS_SURVEY = 1























