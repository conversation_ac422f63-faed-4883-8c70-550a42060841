










CREATE VIEW [FY22].[System_Contact_View]
AS

SELECT [System Name] = a.NAME
	  ,[Acronym] = a.ACRONYM
      ,[System REFSTR] = a.REFSTR
      ,[Business Contact Name] = ISNULL(p.NAME,'')+', '+ISNULL(p.FIRSTNAME,'')
	  ,[Business Contact First Name] = ISNULL(p.FIRSTNAME,'')
	  ,[Business Contact Last Name] = ISNULL(p.NAME,'')
      ,[Business Contact Role] = rt.NAME
      ,a.ICTOBJECT
--,[Business Contact Org] = p.[ORGUNITNAME]
FROM AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.ROLE r on r.OBJECT = a.REFSTR --AND a.OBJECTSTATE != 'Plan'
JOIN AlfabetDB.dbo.ROLETYPE rt ON rt.REFSTR = r.[ROLETYPE]
JOIN AlfabetDB.dbo.PERSON p on p.REFSTR = r.[RESPONSIBLE]
WHERE a.VERSION = '22'
and a.OBJECTSTATE != 'Retired'
and a.ICTOBJECT is NOT NULL

and a.CMS_NXT_SYS_SURVEY = 1












