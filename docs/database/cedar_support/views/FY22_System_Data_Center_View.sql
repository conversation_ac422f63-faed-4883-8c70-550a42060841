










CREATE VIEW [FY22].[System_Data_Center_View] 
AS
SELECT 
	   [System Name] = a.NAME -- SUBSTRING(ISNULL(h.[Name],'Undefined'),1,CASE WHEN CHARINDEX('v.',ISNULL(h.[Name],'Undefined'),1)-1 > 0 THEN CHARINDEX('v.',ISNULL(h.[Name],'Undefined'),1)-1 ELSE LEN(ISNULL(h.[Name],'Undefined')) END)
      ,[System REFSTR] = a.REFSTR
	  ,[Data Center type] = h.SAG_DEPLOYMENTTYPE
	  ,[Data Center REFSTR] = dev.REFSTR
      ,[Data Center name] = dev.NAME
	  ,dev.CMS_ADDRESSLINE1
	  ,dev.CMS_ADDRESSLINE2
	  ,dev.CMS_CITY
	  ,dev.CMS_STATE
	  ,dev.CMS_ZIPCODE
      ,[Utilizes VPN] = h.CMS_DATA_CENTER_UTILIZES_VPN	
      ,[Contractor Name] = h.CMS_DC_CONTRACTOR_NAME
      ,[Multi Factor Authentication] = h.CMS_MULTIFACTOR_AUTH
      ,[WAN Type] = REPLACE(REPLACE(h.CMS_DATA_CENTER_WAN_TYPE,CHAR(13),','),CHAR(10),'') --h.CMS_DATA_CENTER_WAN_TYPE
      ,[CCIC Integration Flag] = h.CMS_CCIC_INTEGRATION_FLAG
      ,[Incident Response Contact] = h.CMS_INCIDENT_RESPONSE_CONTACT
	  ,[Hot Site] = h.CMS_HOT_SITE
	  ,[Data Replicated] = h.CMS_DATA_REPLICATED
	  ,[System Server Software Replicated] = h.CMS_SYS_SERVER_SW_REPLICATED
	  ,[Application Software Replicated] = h.CMS_APP_SOFTWARE_REPLICATED
	  ,[Fed Contact Name] = h.CMS_FED_CONTACT_NAME
	  ,[Fed Contact Org] = h.CMS_FED_CONTACT_ORG
	  ,[Production Data Use Flag] = h.CMS_PRODUCTION_DATA_USE_FLAG
	  ,[Cloud Migration Plan] = a.CMS_CLOUDMIGRATIONPLAN
	  ,a.ICTOBJECT
	  ,h.REFSTR as [Deployment REFSTR]
	  ,de.REFSTR as [DeploymentElement REFSTR]
from AlfabetDB.[dbo].[APPLICATION] a
join AlfabetDB.[dbo].[DEPLOYMENT] h on a.REFSTR = h.OBJECT
join AlfabetDB.[dbo].[DEPLOYMENTELEMENT] de on de.DEPLOYMENT = h.REFSTR
join AlfabetDB.[dbo].[DEVICE] dev on dev.REFSTR = de.DEVICE
WHERE a.VERSION = '22'
and a.OBJECTSTATE != 'Retired'
--and a.ICTOBJECT is NOT NULL
--and a.CMS_NXT_SYS_SURVEY = 1
















