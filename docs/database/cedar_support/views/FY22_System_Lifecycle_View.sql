






CREATE VIEW [FY22].[System_Lifecycle_View]
AS
select 
[System Name] = a.NAME
,[System REFSTR] = a.REFSTR
,a.CMS_DEV_COMPLETE
,a.CMS_DEV_COMPLETE_PERCENT
,a.CMS_DEVELOPMENT_WORK
,a.CMS_AGILE_METHODOLOGY_USE
,a.CMS_RETIRE_OR_REPLACE
,a.CMS_RETIREDORREPLACE_DATE
,a.CMS_PLANNING_RETIREMENT_QUART
,a.CMS_BUS_ARTIFACTS_ON_DEMAND
,a.CMS_TEST_PLAN_ON_DEMAND
,a.CMS_TEST_REPORTS_ON_DEMAND
,a.CMS_TEST_SCRIPT_ON_DEMAND
,a.CMS_SOURCE_CODE_ON_DEMAND
,a.CMS_SYS_DESIGN_ON_DEMAND
,a.CMS_REQ_ON_DEMAND
,a.CMS_OPS_MAINT_ON_DEMAND
,a.ICTOBJECT
from AlfabetDB.dbo.APPLICATION a
WHERE a.VERSION = '22'
and a.OBJECTSTATE != 'Retired'
and a.ICTOBJECT is NOT NULL
--and a.CMS_NXT_SYS_SURVEY = 1












