







CREATE VIEW [FY22].[System_Software_View]
AS
Select	 
		 a.NAME AS [System Name]
		,a.REFSTR AS [System REFSTR]
		,c.CMS_TECHNOPEDIA_RELEASE_ID 
		,v.NAM<PERSON> as [Manufacturer Name]
		,c.Name AS [Software Product]
		,c.CMS_TECHNOPEDIA_VERSION
		,c.CMS_TECHNOPEDIA_EDITION
		,ISNULL(ccp.NAME,cc.NAME) as ProductType
		,cc.Name as Category
		,ccp.NAME as ParentCategory
		,a.ICTOBJECT
		,lc.CMS_API_GATEWAY_USE
		,lc.CMS_PROVIDES_AI_CAPABILITY

from AlfabetDB.dbo.LOCALCOMPONENT lc
join AlfabetDB.dbo.COMPONENT c on c.REFSTR = lc.COMPONENT
join AlfabetDB.dbo.APPLICATION a on a.REFSTR = lc.OWNER
left join AlfabetDB.dbo.VENDOR v on v.REFSTR = c.VEND<PERSON>
left join AlfabetDB.dbo.COMPONENTCATEGORY cc ON cc.REFSTR = c.CA<PERSON><PERSON><PERSON><PERSON>
left join AlfabetDB.dbo.COMPONENTCATEGORY ccp ON ccp.REFSTR = cc.BELONGSTO
WHERE 
c.STEREOTYPE = 'Component'
and a.VERSION = '22'
and a.OBJECTSTATE != 'Retired'

and a.ICTOBJECT is NOT NULL
--and a.CMS_NXT_SYS_SURVEY = 1












