






CREATE VIEW [FY22].[System_Budget_View]
AS

Select 
	 a.REFSTR as [System REFSTR]
	,a.NAME as [System Name]
	,a.ACRONYM
	,p.OFM_PROJECT_ID
	,p.NAME as [Budget Project Name]
	,pa.CMS_FUNDING
	,pa.REFSTR as [Project Arch REFSTR]
	,p.REFSTR as [Budget Project REFSTR]
	,p.STARTDATE
	,p.ENDDATE
	,p.CMS_TITLE
from AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.PROJECT_ARCH pa ON pa.OBJECT = a.REFSTR
JOIN AlfabetDB.dbo.PROJECT p ON p.REFSTR = pa.PROJECT 
WHERE p.STEREOTYPE = 'FYBudgetProject'
and DATEPART(year, p.ENDDATE) = '2021' -- Data currently does not exist for 2020 against v21 (which should be done before Census) so 2019 data against v1.0 can be used for testing
--and p.NAME like '%FY 2021%'
and a.VERSION = '22'
and a.OBJECTSTATE != 'Retired'
and a.ICTOBJECT is NOT NULL

--and a.CMS_NXT_SYS_SURVEY = 1








