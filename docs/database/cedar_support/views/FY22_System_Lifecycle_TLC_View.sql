








CREATE VIEW [FY22].[System_Lifecycle_TLC_View]
AS
select 
[System Name] = a.NAME
,[System REFSTR] = a.REFSTR
,CONVERT(DATE,er.STARTDATE) as [Next Planned Release Date]
,er.CMS_ISMAJORRELEASE as [Is this a major release]
,er.[DESCRIPTION] as [Release Description]
,a.ICTOBJECT
,ri.REFSTR as ReleaseItem_REFSTR
,er.REFSTR as EnterpriseRelease_REFSTR
from AlfabetDB.dbo.APPLICATION a
JOIN AlfabetDB.dbo.RELEASEITEM ri ON a.REFSTR = ri.OBJECT
JOIN AlfabetDB.dbo.ENTERPRISERELEASE er ON ri.A_RELEASE = er.REFSTR
WHERE a.VERSION = '22'
and a.OBJECTSTATE != 'Retired'
and a.ICTOBJECT is NOT NULL
--and a.CMS_NXT_SYS_SURVEY = 1














