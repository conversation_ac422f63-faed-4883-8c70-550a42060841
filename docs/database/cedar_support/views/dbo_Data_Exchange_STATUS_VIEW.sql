



CREATE view [dbo].[Data_Exchange_STATUS_VIEW]
AS 
SELECT Title, 
ACRONYM,
REFSTR,
QA_Contact,
DA_Contact, 
[Data Exchanges]
FROM   
	(
	select 
		aa.NAME Title
		,a.APPLICATION_REFSTR REFSTR
		,b.SURVEY_REVIEWER QA_Contact
		,b.SURVEY_DATA_PAGE_REVIEWER DA_Contact 
		,a.DISPLAY_PAGE_NAME
		,a.status 
		,a.PERCENT_COMPLETE
		,aa.ACRONYM
		from [System_Census].[SYSTEM_SURVEY_PAGE_STATUS] a  
		join [System_Census].[SYSTEM_SURVEY_STATUS] b  on a.APPLICATION_REFSTR=b.APPLICATION_REFSTR 
		join AlfabetDB.dbo.APPLICATION aa ON aa.REFSTR = a.[APPLICATION_REFSTR]
		where aa.CMS_NXT_SYS_SURVEY = 1
	) t 
PIVOT  
	(  
		max(status)   
		FOR display_page_name IN  
		( [Contacts and Roles], [Business Owner Basic Information], [Budget and Contracts], 
		[System Lifecycle and Planned Releases], 
		[System Maintainer Basic Information],
		[System Components],
		[URLs],
		[Data Exchanges],
		[Data Center Hosting Environments],
		[Software Products]
	)  
) AS pvt  
--order by Title; 








