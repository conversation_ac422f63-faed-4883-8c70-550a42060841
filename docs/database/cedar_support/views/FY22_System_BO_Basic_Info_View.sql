
CREATE VIEW [FY22].[System_BO_Basic_Info_View]
AS

SELECT   [System Name] = a.NAME
		, [System Refstr] = a.REFSTR
		, ACRONYM = ISNULL(a.ACRONYM,'')
		, a.VERSION
		, [Prod Date] = CONVERT(VARCHAR(10), ict.STARTDATE, 101)
		, [System OwnedBy] = IIF(a.CMS_OWNED_SYSTEM= 'Yes', 'CMS Owned', IIF(a.CMS_CONTRACTOR_OWNED_SYSTEM='Yes', 'Contractor Owned', ''))
		, a.CMS_SYSTEM_COST_PER_YEAR
		, [Number of Federal Support FTEs] = MAX(CASE WHEN it.NAME = 'Number of Federal Support FTEs' THEN i.SEMANTICVALUE ELSE '' END)
		, [Number of Contractor Support FTEs] = MAX(CASE WHEN it.NAME = 'Number of Contractor Support FTEs' THEN i.SEMANTICVALUE ELSE '' END)
		, [Number of Direct System Users] = MAX(CASE WHEN it.NAME = 'Number of Direct System Users' THEN i.SEMANTICVALUE ELSE '' END)
		, [CMS_BENEFICIARY_ADDRESS]
		, CMS_BENEFICIARY_ADDRESS_PUR
		, CMS_BENEFICIARY_ADDR_PUR_OTHER
		, CMS_BENEFICIARY_ADDRESS_SRC
		, CMS_BENEFICIARY_ADDR_SRC_OTHER
		, a.DESCRIPTION

FROM AlfabetDB.dbo.APPLICATION a 
JOIN AlfabetDB.dbo.ICTOBJECT ict on ict.REFSTR = a.ICTOBJECT
LEFT JOIN AlfabetDB.dbo.INDICATOR i ON i.OBJECT = a. REFSTR
LEFT JOIN AlfabetDB.dbo.INDICATORTYPE it on it.REFSTR = i.INDICATORTYPE 
		AND it.NAME IN ('Number of Federal Support FTEs','Number of Contractor Support FTEs','Number of Direct System Users')
WHERE a.VERSION in('22') AND a.CMS_NXT_SYS_SURVEY = 1
GROUP BY a.NAME
		,a.REFSTR
		,a.ACRONYM
		,a.DESCRIPTION
		,a.CMS_OWNED_SYSTEM
		,a.CMS_CONTRACTOR_OWNED_SYSTEM
		,a.CMS_SYSTEM_COST_PER_YEAR	
		,a.[CMS_BENEFICIARY_ADDRESS]
		,a.[CMS_BANKING_DATA]
		, a.version
		, a.STARTDATE
		, CMS_BENEFICIARY_ADDRESS_PUR
		, CMS_BENEFICIARY_ADDRESS_SRC
		, CMS_BENEFICIARY_ADDR_SRC_OTHER
		, CMS_BENEFICIARY_ADDR_PUR_OTHER
		, ict.STARTDATE
		, a.version


