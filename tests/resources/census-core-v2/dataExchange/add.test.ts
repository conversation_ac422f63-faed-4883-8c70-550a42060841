import request from 'supertest';
import { get } from 'lodash';
import dataExchangeAdd from 'src/resources/census-core-v2/dataExchange/add';
import findConfig from 'src/resources/census-core-v2/dataExchange';
import { CMSApp } from 'src/types';
import { createBadApp, testLogger, createUnitTestApp } from 'tests/test-utils';

const mocks = vi.hoisted(() => ({
  getDataExchangeListUtil: vi.fn(),
  dataExchangeStatusFindUtil: vi.fn(),
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('src/utils/dataExchange'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDataExchangeListUtil: mocks.getDataExchangeListUtil,
    dataExchangeStatusFindUtil: mocks.dataExchangeStatusFindUtil,
  };
});

let app: CMSApp;

describe.skip('Data Exchange Add List tests', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(findConfig);
    app.resources.initResources(app);
  });

  const SYSTEM_ID = '{11111111-**************-************}';
  const OTHER_SYSTEM_ID = '{11111111-**************-************}'; // Using same mock UUID as per guideline
  const EXCHANGE_ID = '{11111111-**************-55555555555}5'; // Using same mock UUID as per guideline

  const mockDbExchange = (direction: 'sender' | 'receiver' = 'sender') => ({
    fromOwnerName: direction === 'sender' ? 'System A' : 'System B',
    fromOwnerId: direction === 'sender' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    fromOwnerType: 'Application',
    exchangeName: 'Sample Exchange',
    exchangeId: EXCHANGE_ID,
    toOwnerName: direction === 'receiver' ? 'System A' : 'System B',
    toOwnerId: direction === 'receiver' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    toOwnerType: 'Application',
    exchangeDescription: 'Description of sample exchange',
    dataFormat: 'JSON',
    dataFormatOther: null,
    containsPhi: 'false',
    containsPii: 'true',
    connectionFrequency: 'Daily',
    containsBankingData: 'false',
    containsBeneficiaryAddress: 'false',
    isBeneficiaryMailingFile: 'false',
    dataExchangeAgreement: 'MOU',
    numOfRecords: '1000',
    sharedViaApi: 'true',
    exchangeState: 'Active',
    exchangeRetiredDate: null,
    isAddressEditable: 'false',
    containsHealthDisparityData: 'false',
    apiOwnership: 'Internal',
    typeOfDataName: 'Patient Demographics',
    typeOfDataId: 'TD1',
    exchangeStartDate: '2023-01-01',
    exchangeEndDate: '2023-12-31',
    exchangeVersion: '1',
    businessPurposeOfAddress: 'Enrollment',
    exchangeConnectionAuthenticated: 'true',
    exchangeContainsCUI: 'false',
    exchangeCUIDescription: null,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
    exchangeCUIType: 'Controlled',
  });

  const mockDbStatus = (direction: 'sender' | 'receiver' = 'receiver', isNew = false) => ({
    SYSTEM_SURVEY_EXCHANGE_STATUS_ID: 1,
    exchangeId: EXCHANGE_ID,
    NEW_EXCHANGE_FLAG: isNew,
    direction,
    systemId: direction === 'receiver' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    partnerId: direction === 'sender' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    deleted: false,
    systemStatus: 'Completed',
    partnerStatus: 'Reviewed',
    reviewerStatus: 'Reviewed',
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 200 with data exchanges and statuses when found for systemId as receiver', async () => {
    mocks.getDataExchangeListUtil
      .mockResolvedValueOnce({
        count: 1,
        Exchanges: [mockDbExchange('receiver')],
      }); // Mock getExchangeList
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce({
        count: 1,
        ExchangeStatus: [mockDbStatus('receiver')],
      }); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.systemId).toEqual(SYSTEM_ID);
    expect(response.body.pageName).toEqual('DataExchanges');
    expect(response.body.count).toEqual(1);
    expect(response.body.DataExchanges).toHaveLength(1);
    expect(response.body.DataExchanges[0].direction).toEqual('receiver');
    expect(response.body.DataExchanges[0].Exchange.exchangeId).toEqual(EXCHANGE_ID);
    expect(response.body.DataExchanges[0].Status.exchangeId).toEqual(EXCHANGE_ID);
  });

  it('Should return a 200 with data exchanges and statuses when found for systemId as sender', async () => {
    mocks.getDataExchangeListUtil
      .mockResolvedValueOnce({
        count: 1,
        Exchanges: [mockDbExchange('sender')],
      }); // Mock getExchangeList
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce({
        count: 1,
        ExchangeStatus: [mockDbStatus('sender')],
      }); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.DataExchanges[0].direction).toEqual('sender');
    expect(response.body.DataExchanges[0].Exchange.fromOwnerId).toEqual(SYSTEM_ID);
    expect(response.body.DataExchanges[0].Status.systemId).toEqual(SYSTEM_ID);
  });

  it('Should return a 200 with multiple data exchanges and statuses', async () => {
    const mockDbExchange2 = {
      ...mockDbExchange('receiver'),
      exchangeId: '{11111111-**************-************}', // Updated mock UUID
      exchangeName: 'Another Exchange',
      fromOwnerId: OTHER_SYSTEM_ID,
      toOwnerId: SYSTEM_ID,
    };
    const mockDbStatus2 = {
      ...mockDbStatus('receiver', true), // Set isNew to true
      exchangeId: '{11111111-**************-************}', // Updated mock UUID
    };

    mocks.getDataExchangeListUtil
      .mockResolvedValueOnce({
        count: 2,
        Exchanges: [mockDbExchange('sender'), mockDbExchange2],
      }); // Mock getExchangeList
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce({
        count: 2,
        ExchangeStatus: [mockDbStatus('sender'), mockDbStatus2],
      }); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.count).toEqual(2);
    expect(response.body.DataExchanges).toHaveLength(2);
    expect(response.body.DataExchanges[0].direction).toEqual('sender');
    expect(response.body.DataExchanges[1].direction).toEqual('receiver');
  });

  it('Should return an empty list when no data exchanges are found', async () => {
    mocks.getDataExchangeListUtil
      .mockResolvedValueOnce({
        count: 0,
        Exchanges: [],
      }); // Mock getExchangeList
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce({
        count: 0,
        ExchangeStatus: [],
      }); // Mock getPageDataExchangeStatusList
    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.count).toEqual(0);
    expect(response.body.DataExchanges).toEqual([]);
  });

  it('Should return an empty list when exchanges are found but no matching statuses', async () => {
    const unmatchedMockDbStatus = {
      SYSTEM_SURVEY_EXCHANGE_STATUS_ID: 1,
      exchangeId: null,
      direction: 'sender',
      systemId: OTHER_SYSTEM_ID,
      partnerId: SYSTEM_ID,
      deleted: false,
      systemStatus: 'Completed',
      partnerStatus: 'Reviewed',
      reviewerStatus: 'Reviewed',
    };
    mocks.getDataExchangeListUtil
      .mockResolvedValueOnce({
        count: 1,
        Exchanges: [mockDbExchange('sender')],
      }); // Mock getExchangeList
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce({
        count: 1,
        ExchangeStatus: [unmatchedMockDbStatus],
      }); // Mock getPageDataExchangeStatusList
    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.count).toEqual(1);
    expect(response.body.DataExchanges).toHaveLength(1);
    // No matching status, so direction is null
    expect(response.body.DataExchanges[0].direction).toBeNull();
    // Exchange ID still present
    expect(response.body.DataExchanges[0].Status.exchangeId).toEqual(EXCHANGE_ID);
    // System ID copied over
    expect(response.body.DataExchanges[0].Status.systemId).toEqual(SYSTEM_ID);
  });

  it('should always pass in version as a string', async () => {
    const db = mocks.getDb();
    mocks.getDataExchangeListUtil
      .mockResolvedValueOnce({
        count: 1,
        Exchanges: [mockDbExchange('sender')],
      }); // Mock getExchangeList
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce({
        count: 1,
        ExchangeStatus: [mockDbStatus('sender')],
      }); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
        version: 123,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.getDataExchangeListUtil).toHaveBeenCalledWith(app, db, SYSTEM_ID, 'both', '123');
  });

  it('should always pass in version as a string, even when empty', async () => {
    const db = mocks.getDb();
    mocks.getDataExchangeListUtil
      .mockResolvedValueOnce({
        count: 1,
        Exchanges: [mockDbExchange('sender')],
      }); // Mock getExchangeList
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce({
        count: 1,
        ExchangeStatus: [mockDbStatus('sender')],
      }); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
        version: null,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.getDataExchangeListUtil).toHaveBeenCalledWith(app, db, SYSTEM_ID, 'both', '');
  });

  it('Should return a 400 when systemId is missing', async () => {
    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['systemId must be provided'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('systemId must be provided');
  });

  it('Should return a 500 when app is not in the request', async () => {
    const badApp = createBadApp('/', 'get', dataExchangeAdd);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Unable to get the application from request'],
    });
  });

  it('Should return a 500 when getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('DB connection failed'));

    const response = await request(app).get('/')
      .query({ systemId: SYSTEM_ID })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Database Unavailable'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('DB connection failed');
  });

  it('Should return an empty list when attempting to fetch exchanges returns an error', async () => {
    mocks.getDataExchangeListUtil.mockResolvedValueOnce(new Error('Checking exchange logger error here'));

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Error fetching data exchanges'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('Checking exchange logger error here');
  });

  it('Should return an empty list when attempting to fetch exchanges returns an error', async () => {
    mocks.getDataExchangeListUtil.mockResolvedValueOnce({
      count: 1,
      Exchanges: [mockDbExchange('sender')],
    });
    mocks.dataExchangeStatusFindUtil.mockResolvedValueOnce(new Error('Checking status logger error here')); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Error fetching data exchange statuses'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('Checking status logger error here');
  });
});
