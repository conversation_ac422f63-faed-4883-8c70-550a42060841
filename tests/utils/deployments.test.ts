import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import {
  getDeploymentWhere,
  deploymentListUtil,
} from '../../src/utils/deployments';
import {
  rawDeploymentAllQuery,
  formattedDeploymentAllQuery,
  rawDeploymentSelectedQuery,
  formattedDeploymentSelectedQuery,
} from './deploymentListTestData';
import { createApp, testLogger } from '../test-utils';
import { CMSApp } from '../../src/types';
import MssqlData from '../../src/data-sources/mssql';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  db: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

const app = await createApp();
const db = mocks.db();

const systemId = '{11111111-1111-1111-1111-111111111111}';

describe('Deployment List tests', () => {
  describe('getDeploymentWhere', () => {
    it('Should have the secondary param deploymentType when included in the where object', async () => {
      const where = getDeploymentWhere(systemId, '', 'dev', '');
      const WhereObj = {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: systemId,
          },
        },
        and: [{
          operation: {
            column: 'Environment',
            operator: '=',
            value: 'dev',
          },
        }],
      };
      expect(where).toEqual(WhereObj);
    });

    it('Should have the secondary param status when included in the where object', async () => {
      const where = getDeploymentWhere(systemId, 'active', '', '');
      const WhereObj = {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: systemId,
          },
        },
        and: [{
          operation: {
            column: 'Relationship Status',
            operator: '=',
            value: 'active',
          },
        }],
      };
      expect(where).toEqual(WhereObj);
    });

    it('Should have the secondary param state when included in the where object', async () => {
      const where = getDeploymentWhere(systemId, '', '', 'active');
      const WhereObj = {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: systemId,
          },
        },
        and: [{
          operation: {
            column: 'State',
            operator: '=',
            value: 'active',
          },
        }],
      };
      expect(where).toEqual(WhereObj);
    });

    it('Should return a 500 and an error if secondary params are provided and systemId is not', async () => {
      const where = getDeploymentWhere('', '', '', 'active');
      expect(where).toEqual(new Error('System ID is required if other parameters are provided'));
    });
  });
  describe('deploymentListUtil', () => {
    it('Should return an error if app is not in the params', async () => {
      const test = await deploymentListUtil(null as unknown as CMSApp, db, {}) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is an error', async () => {
      const test = await deploymentListUtil(app, new Error('There was an issue getting the database') as unknown as MssqlData, {}) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(new Error('There was an issue getting the database'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an issue getting the database');
    });

    it('Should return an error if db is empty', async () => {
      const test = await deploymentListUtil(app, null as unknown as MssqlData, {}) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(new Error('Database Unavailable'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return a list of all data centers with no systemId', async () => {
      mocks.queryView.mockResolvedValueOnce([rawDeploymentAllQuery]);
      const test = await deploymentListUtil(app, db, {});

      expect(test).toEqual({
        count: formattedDeploymentAllQuery.length,
        Deployments: formattedDeploymentAllQuery,
      });
    });

    it('Should return a list of all systems with a systemId', async () => {
      mocks.queryView.mockResolvedValueOnce([rawDeploymentSelectedQuery]);

      const test = await deploymentListUtil(app, db, { systemId });
      expect(test).toEqual({
        count: formattedDeploymentSelectedQuery.length,
        Deployments: formattedDeploymentSelectedQuery,
      });
    });

    it('Should return an error if getDeploymentWhere returns an error', async () => {
      const test = await deploymentListUtil(app, db, { state: 'active' });

      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(new Error('System ID is required if other parameters are provided'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('System ID is required if other parameters are provided');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await deploymentListUtil(app, db, { systemId });

      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(new Error('There was an error retrieving the deployment list from the database'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
      const test = await deploymentListUtil(app, db, { systemId });
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(new Error('There was an error retrieving the deployment list from the database'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
    });
  });
});
