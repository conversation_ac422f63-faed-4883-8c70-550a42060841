import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
  afterEach,
} from 'vitest';
import { get } from 'lodash';
import {
  dataExchangeStatusFindUtil,
  getDataExchangeListUtil,
  getDataExchangeByIdUtil,
  convertDbToApiStatus,
  mapExchangeToSPJson,
  createRelation,
  wmStatusInsert,
  insertReceiver,
  insertSender,
  wmStatusUpdate,
  updateReceiver,
  updateSender,
  mapStatusToSPSenderJson,
  mapStatusToSPReceiverJson,
} from '../../src/utils/dataExchange';
import { createApp, testLogger } from '../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  db: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
    db: {
      query: vi.fn(),
    },
    query: vi.fn(),
  })),
  tryAsync: vi.fn(),
}));

const app = await createApp();
const db = mocks.db();

vi.mock('../../src/utils/db-helpers', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

describe('Data Exchange Utilities tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('convertDbToApiStatus', () => {
    const mockDbStatus: DbPageDataExchangeStatus = {
      SYSTEM_SURVEY_EXCHANGE_STATUS_ID: 1,
      EXCHANGE_ID: 'test-exchange-id',
      NEW_EXCHANGE_FLAG: true,
      ORIGINAL_RECEIVER_ID: 'receiver-123',
      ORIGINAL_SENDER_ID: 'sender-456',
      RECEIVER_REPORTED_DELETE_FLAG: false,
      SENDER_REPORTED_DELETE_FLAG: null,
      RECEIVER_STATUS: 'active',
      RECEIVER_LAST_SUBMIT_DATETIME: '2023-01-01T00:00:00Z',
      SENDER_STATUS: 'pending',
      SENDER_LAST_SUBMIT_DATETIME: '2023-01-02T00:00:00Z',
      RECEIVER_QA_STATUS: 'approved',
      SENDER_QA_STATUS: 'review',
    };

    it('should convert DB status to API format when system is receiver', () => {
      const result = convertDbToApiStatus(mockDbStatus, 'receiver-123');

      expect(result).toEqual({
        exchangeId: 'test-exchange-id',
        systemId: 'receiver-123',
        systemStatus: 'active',
        partnerId: 'sender-456',
        partnerStatus: 'pending',
        reviewerStatus: 'approved',
        direction: 'receiver',
      });
    });

    it('should convert DB status to API format when system is sender', () => {
      const result = convertDbToApiStatus(mockDbStatus, 'sender-456');

      expect(result).toEqual({
        exchangeId: 'test-exchange-id',
        systemId: 'sender-456',
        systemStatus: 'pending',
        partnerId: 'receiver-123',
        partnerStatus: 'active',
        reviewerStatus: 'review',
        direction: 'sender',
      });
    });

    it('should return default values when system ID does not match', () => {
      const result = convertDbToApiStatus(mockDbStatus, 'unknown-system');

      expect(result).toEqual({
        exchangeId: 'test-exchange-id',
        systemId: null,
        systemStatus: null,
        partnerId: null,
        partnerStatus: null,
        reviewerStatus: null,
        direction: null,
      });
    });
  });

  describe('dataExchangeStatusFindUtil', () => {
    const mockDbResults: DbPageDataExchangeStatus[] = [
      {
        SYSTEM_SURVEY_EXCHANGE_STATUS_ID: 1,
        EXCHANGE_ID: 'exchange-1',
        NEW_EXCHANGE_FLAG: true,
        ORIGINAL_RECEIVER_ID: 'system-123',
        ORIGINAL_SENDER_ID: 'system-456',
        RECEIVER_REPORTED_DELETE_FLAG: false,
        SENDER_REPORTED_DELETE_FLAG: null,
        RECEIVER_STATUS: 'active',
        RECEIVER_LAST_SUBMIT_DATETIME: '2023-01-01T00:00:00Z',
        SENDER_STATUS: 'pending',
        SENDER_LAST_SUBMIT_DATETIME: '2023-01-02T00:00:00Z',
        RECEIVER_QA_STATUS: 'approved',
        SENDER_QA_STATUS: 'review',
      },
    ];

    it('should return error when app is not provided', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await dataExchangeStatusFindUtil(null as any, db, 'system-123', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Unable to get the application from request');
    });

    it('should return error when db is an error', async () => {
      const errorDb = new Error('Database connection failed');
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await dataExchangeStatusFindUtil(app, errorDb as any, 'system-123', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Database Unavailable');
      expect(app.logger.error).toHaveBeenCalledWith({ error: errorDb });
    });

    it('should return error when systemId is not provided', async () => {
      const result = await dataExchangeStatusFindUtil(app, db, '', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('The systemId parameter is required and must be a string');
    });

    it('should return error when systemId is not a string', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await dataExchangeStatusFindUtil(app, db, 123 as any, 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('The systemId parameter is required and must be a string');
    });

    it('should successfully fetch exchange status for receiver direction', async () => {
      mocks.queryView.mockResolvedValue([mockDbResults]);
      const result = await dataExchangeStatusFindUtil(app, db, 'system-123', 'receiver');

      expect(result).toEqual({
        count: 1,
        ExchangeStatus: [
          {
            exchangeId: 'exchange-1',
            systemId: 'system-123',
            systemStatus: 'active',
            partnerId: 'system-456',
            partnerStatus: 'pending',
            reviewerStatus: 'approved',
            direction: 'receiver',
          },
        ],
      });

      expect(mocks.queryView).toHaveBeenCalledWith(
        'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_STATUS',
        expect.any(Array),
        expect.objectContaining({
          where: {
            operation: {
              column: 'ORIGINAL_RECEIVER_ID',
              operator: '=',
              value: 'system-123',
            },
          },
        }),
      );
    });

    it('should successfully fetch exchange status for sender direction', async () => {
      mocks.queryView.mockResolvedValue([mockDbResults]);

      const result = await dataExchangeStatusFindUtil(app, db, 'system-456', 'sender');

      expect(result).toEqual({
        count: 1,
        ExchangeStatus: [
          {
            exchangeId: 'exchange-1',
            systemId: 'system-456',
            systemStatus: 'pending',
            partnerId: 'system-123',
            partnerStatus: 'active',
            reviewerStatus: 'review',
            direction: 'sender',
          },
        ],
      });

      expect(mocks.queryView).toHaveBeenCalledWith(
        'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_STATUS',
        expect.any(Array),
        expect.objectContaining({
          where: {
            operation: {
              column: 'ORIGINAL_SENDER_ID',
              operator: '=',
              value: 'system-456',
            },
          },
        }),
      );
    });

    it('should successfully fetch exchange status for both direction', async () => {
      mocks.queryView.mockResolvedValue([mockDbResults]);
      const result = await dataExchangeStatusFindUtil(app, db, 'system-123', 'both');

      expect(result).toEqual({
        count: 1,
        ExchangeStatus: [
          {
            exchangeId: 'exchange-1',
            systemId: 'system-123',
            systemStatus: 'active',
            partnerId: 'system-456',
            partnerStatus: 'pending',
            reviewerStatus: 'approved',
            direction: 'receiver',
          },
        ],
      });

      expect(mocks.queryView).toHaveBeenCalledWith(
        'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_STATUS',
        expect.any(Array),
        expect.objectContaining({
          where: {
            operation: {
              column: 'ORIGINAL_RECEIVER_ID',
              operator: '=',
              value: 'system-123',
            },
          },
          or: [
            {
              operation: {
                column: 'ORIGINAL_SENDER_ID',
                operator: '=',
                value: 'system-123',
              },
            },
          ],
        }),
      );
    });

    it('should return error when query returns an error', async () => {
      mocks.queryView.mockResolvedValue(new Error('Query View Error'));
      const result = await dataExchangeStatusFindUtil(app, db, 'system-123', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('There was an error fetching the query');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('should return error when query returns a critical error', async () => {
      mocks.queryView.mockRejectedValue(new Error('Query View Critical Error'));
      const result = await dataExchangeStatusFindUtil(app, db, 'system-123', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('There was an error fetching the query');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Critical Error');
    });

    it('should return error when query result is not an array', async () => {
      mocks.queryView.mockResolvedValue(null);
      mocks.tryAsync.mockResolvedValue([null, null]);

      const result = await dataExchangeStatusFindUtil(app, db, 'system-123', 'both');
      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Unable to get result from query');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to get result from query');
    });
  });

  describe('getDataExchangeListUtil', () => {
    it('should return error when app is not provided', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await getDataExchangeListUtil(null as any, db, '{********-1111-1111-1111-************}', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Unable to get the application from request');
    });

    it('should return error when db is an error', async () => {
      const errorDb = new Error('Database connection failed');
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await getDataExchangeListUtil(app, errorDb as any, '{********-1111-1111-1111-************}', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Database Unavailable');
    });

    it('should return error when systemId is empty', async () => {
      const result = await getDataExchangeListUtil(app, db, '', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Please provide required parameters \'systemId\' and \'direction\'');
    });

    it('should return error when direction is empty', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await getDataExchangeListUtil(app, db, '{********-1111-1111-1111-************}', '' as any);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Please provide required parameters \'systemId\' and \'direction\'');
    });

    it('should return error when direction is invalid', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await getDataExchangeListUtil(app, db, '{********-1111-1111-1111-************}', 'invalid' as any);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('The provided direction is not valid.');
    });

    it('should return error when systemId is not a valid UUID', async () => {
      const result = await getDataExchangeListUtil(app, db, '{invalid-uuid}', 'both');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('The system ID is not valid');
    });

    it('should successfully fetch exchange list for sender direction', async () => {
      const mockRawResults = [
        {
          exchangeId: '{********-1111-1111-1111-************}',
          exchangeName: 'Test Exchange',
          exchangeDescription: 'Test Description',
          exchangeVersion: '1.0',
          exchangeState: 'Active',
          exchangeStartDate: '2023-01-01',
          exchangeEndDate: '2023-12-31',
          exchangeRetiredDate: null,
          fromOwnerId: '{*************-2222-2222-************}',
          fromOwnerName: 'Sender System',
          fromOwnerType: 'System',
          toOwnerId: '{*************-3333-3333-************}',
          toOwnerName: 'Receiver System',
          toOwnerType: 'System',
          connectionFrequency: 'Daily|Weekly',
          dataExchangeAgreement: 'Yes',
          containsBeneficiaryAddress: 'Yes',
          businessPurposeOfAddress: 'Business|Personal',
          isAddressEditable: 'No',
          containsPii: 'Yes',
          containsPhi: 'No',
          containsHealthDisparityData: 'Yes',
          containsBankingData: 'No',
          sharedViaApi: 'Yes',
          apiOwnership: 'Internal',
          typeOfDataName: 'Patient Data',
          typeOfDataId: '1',
          numOfRecords: '1000',
          dataFormat: 'JSON',
          dataFormatOther: '',
          exchangeContainsCUI: 'No',
          exchangeCUIDescription: null,
          exchangeCUIType: 'ItemOne|ItemTwo',
          exchangeConnectionAuthenticated: 'Yes',
          exchangeNetworkProtocol: 'HTTPS|SFTP',
          exchangeNetworkProtocolOther: '',
        },
      ];

      mocks.queryView.mockResolvedValue([mockRawResults]);
      mocks.tryAsync.mockResolvedValue([null, [mockRawResults]]);

      const result = await getDataExchangeListUtil(app, db, '{********-1111-1111-1111-************}', 'sender');

      expect(result).toEqual({
        count: 1,
        Exchanges: [
          expect.objectContaining({
            exchangeId: '{********-1111-1111-1111-************}',
            exchangeName: 'Test Exchange',
            connectionFrequency: ['Daily', 'Weekly'],
            containsBeneficiaryAddress: true,
            businessPurposeOfAddress: ['Business', 'Personal'],
            isAddressEditable: false,
            containsPii: true,
            containsPhi: false,
            containsHealthDisparityData: true,
            containsBankingData: false,
            sharedViaApi: true,
            typeOfData: [{ id: '1', name: 'Patient Data' }],
            exchangeContainsCUI: false,
            exchangeCUIType: ['ItemOne', 'ItemTwo'],
            exchangeConnectionAuthenticated: true,
            exchangeNetworkProtocol: ['HTTPS', 'SFTP'],
          }),
        ],
      });

      expect(mocks.queryView).toHaveBeenCalledWith(
        expect.any(String), // SPARX_SYSTEM_DATAEXCHANGE constant
        expect.any(Array),
        expect.objectContaining({
          where: {
            operation: {
              column: 'Sparx Sendor GUID',
              operator: '=',
              value: '{********-1111-1111-1111-************}',
            },
          },
        }),
      );
    });

    it('should successfully fetch exchange list for receiver direction', async () => {
      const mockRawResults = [
        {
          exchangeId: '{********-1111-1111-1111-************}',
          exchangeName: 'Test Exchange',
          exchangeDescription: 'Test Description',
          exchangeVersion: '1.0',
          exchangeState: 'Active',
          exchangeStartDate: '2023-01-01',
          exchangeEndDate: '2023-12-31',
          exchangeRetiredDate: null,
          fromOwnerId: '{*************-2222-2222-************}',
          fromOwnerName: 'Sender System',
          fromOwnerType: 'System',
          toOwnerId: '{*************-3333-3333-************}',
          toOwnerName: 'Receiver System',
          toOwnerType: 'System',
          connectionFrequency: 'Daily|Weekly',
          dataExchangeAgreement: 'Yes',
          containsBeneficiaryAddress: 'Yes',
          businessPurposeOfAddress: 'Business|Personal',
          isAddressEditable: 'No',
          containsPii: 'Yes',
          containsPhi: 'No',
          containsHealthDisparityData: 'Yes',
          containsBankingData: 'No',
          sharedViaApi: 'Yes',
          apiOwnership: 'Internal',
          typeOfDataName: 'Patient Data',
          typeOfDataId: '1',
          numOfRecords: '1000',
          dataFormat: 'JSON',
          dataFormatOther: '',
          exchangeContainsCUI: 'No',
          exchangeCUIDescription: null,
          exchangeCUIType: null,
          exchangeConnectionAuthenticated: 'Yes',
          exchangeNetworkProtocol: 'HTTPS|SFTP',
          exchangeNetworkProtocolOther: '',
        },
      ];

      mocks.queryView.mockResolvedValue([mockRawResults]);
      mocks.tryAsync.mockResolvedValue([null, [mockRawResults]]);

      const result = await getDataExchangeListUtil(app, db, '{********-1111-1111-1111-************}', 'receiver');

      expect(result).toEqual({
        count: 1,
        Exchanges: [
          expect.objectContaining({
            exchangeId: '{********-1111-1111-1111-************}',
            exchangeName: 'Test Exchange',
            connectionFrequency: ['Daily', 'Weekly'],
            containsBeneficiaryAddress: true,
            businessPurposeOfAddress: ['Business', 'Personal'],
            isAddressEditable: false,
            containsPii: true,
            containsPhi: false,
            containsHealthDisparityData: true,
            containsBankingData: false,
            sharedViaApi: true,
            typeOfData: [{ id: '1', name: 'Patient Data' }],
            exchangeContainsCUI: false,
            exchangeCUIType: [],
            exchangeConnectionAuthenticated: true,
            exchangeNetworkProtocol: ['HTTPS', 'SFTP'],
          }),
        ],
      });

      expect(mocks.queryView).toHaveBeenCalledWith(
        expect.any(String), // SPARX_SYSTEM_DATAEXCHANGE constant
        expect.any(Array),
        expect.objectContaining({
          where: {
            operation: {
              column: 'Sparx Receiver GUID',
              operator: '=',
              value: '{********-1111-1111-1111-************}',
            },
          },
        }),
      );
    });

    it('should fetch all ecxhanges with a particular system ID when direction is both', async () => {
      const mockRawResults = [
        {
          exchangeId: '{********-1111-1111-1111-************}',
          exchangeName: 'Test Exchange',
          exchangeDescription: 'Test Description',
          exchangeVersion: '1.0',
          exchangeState: 'Active',
          exchangeStartDate: '2023-01-01',
          exchangeEndDate: '2023-12-31',
          exchangeRetiredDate: null,
          fromOwnerId: '{*************-2222-2222-************}',
          fromOwnerName: 'Sender System',
          fromOwnerType: 'System',
          toOwnerId: '{*************-3333-3333-************}',
          toOwnerName: 'Receiver System',
          toOwnerType: 'System',
          connectionFrequency: 'Daily|Weekly',
          dataExchangeAgreement: 'Yes',
          containsBeneficiaryAddress: 'Yes',
          businessPurposeOfAddress: 'Business|Personal',
          isAddressEditable: 'No',
          containsPii: 'Yes',
          containsPhi: 'No',
          containsHealthDisparityData: 'Yes',
          containsBankingData: 'No',
          sharedViaApi: 'Yes',
          apiOwnership: 'Internal',
          typeOfDataName: 'Patient Data',
          typeOfDataId: '1',
          numOfRecords: '1000',
          dataFormat: 'JSON',
          dataFormatOther: '',
          exchangeContainsCUI: 'No',
          exchangeCUIDescription: null,
          exchangeCUIType: null,
          exchangeConnectionAuthenticated: 'Yes',
          exchangeNetworkProtocol: 'HTTPS|SFTP',
          exchangeNetworkProtocolOther: '',
        },
      ];

      mocks.queryView.mockResolvedValue([mockRawResults]);
      mocks.tryAsync.mockResolvedValue([null, [mockRawResults]]);

      const result = await getDataExchangeListUtil(app, db, '{********-1111-1111-1111-************}', 'both');

      expect(result).toEqual({
        count: 1,
        Exchanges: [
          expect.objectContaining({
            exchangeId: '{********-1111-1111-1111-************}',
            exchangeName: 'Test Exchange',
            connectionFrequency: ['Daily', 'Weekly'],
            containsBeneficiaryAddress: true,
            businessPurposeOfAddress: ['Business', 'Personal'],
            isAddressEditable: false,
            containsPii: true,
            containsPhi: false,
            containsHealthDisparityData: true,
            containsBankingData: false,
            sharedViaApi: true,
            typeOfData: [{ id: '1', name: 'Patient Data' }],
            exchangeContainsCUI: false,
            exchangeCUIType: [],
            exchangeConnectionAuthenticated: true,
            exchangeNetworkProtocol: ['HTTPS', 'SFTP'],
          }),
        ],
      });

      expect(mocks.queryView).toHaveBeenCalledWith(
        expect.any(String), // SPARX_SYSTEM_DATAEXCHANGE constant
        expect.any(Array),
        expect.objectContaining({
          where: {
            operation: {
              column: 'Sparx Sendor GUID',
              operator: '=',
              value: '{********-1111-1111-1111-************}',
            },
          },
          or: [
            {
              operation: {
                column: 'Sparx Receiver GUID',
                operator: '=',
                value: '{********-1111-1111-1111-************}',
              },
            },
          ],
        }),
      );
    });

    it('Should return an error when the queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await getDataExchangeListUtil(app, db, '{********-1111-1111-1111-************}', 'both');

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toBe('Unable to retrieve exchange by system ID');
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error when the queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Query View Critical Error'));
      const test = await getDataExchangeListUtil(app, db, '{********-1111-1111-1111-************}', 'both');

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toBe('Unable to retrieve exchange by system ID');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Critical Error');
    });
  });

  describe('getDataExchangeByIdUtil', () => {
    it('should return error when id is not a valid UUID', async () => {
      const result = await getDataExchangeByIdUtil(app, db, '{invalid-uuid}');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Invalid object id');
    });

    it('should successfully fetch exchange by id', async () => {
      const mockRawResult = [
        {
          exchangeId: '{********-1111-1111-1111-************}',
          exchangeName: 'Test Exchange',
          exchangeVersion: '1.0',
          exchangeState: 'Active',
          exchangeStartDate: '2023-01-01',
          exchangeEndDate: '2023-12-31',
          exchangeRetiredDate: null,
          fromOwnerId: '{*************-2222-2222-************}',
          fromOwnerName: 'Sender System',
          fromOwnerType: 'System',
          toOwnerId: '{*************-3333-3333-************}',
          toOwnerName: 'Receiver System',
          toOwnerType: 'System',
          connectionFrequency: 'Daily|Weekly',
          businessPurposeOfAddress: 'Business|Personal',
          containsHealthDisparityData: 'Yes',
          typeOfData: 'Patient Data|Claims Data',
          exchangeContainsCUI: 'No',
          exchangeCUIDescription: null,
          exchangeNetworkProtocol: 'HTTPS|SFTP',
          exchangeNetworkProtocolOther: '',
        },
      ];

      mocks.queryView.mockResolvedValue([mockRawResult]);
      mocks.tryAsync.mockResolvedValue([null, [mockRawResult]]);

      const result = await getDataExchangeByIdUtil(app, db, '{********-1111-1111-1111-************}');

      expect(result).toEqual({
        exchangeId: '{********-1111-1111-1111-************}',
        exchangeName: 'Test Exchange',
        exchangeVersion: '1.0',
        exchangeState: 'Active',
        exchangeStartDate: '2023-01-01',
        exchangeEndDate: '2023-12-31',
        exchangeRetiredDate: null,
        fromOwnerId: '{*************-2222-2222-************}',
        fromOwnerName: 'Sender System',
        fromOwnerType: 'System',
        toOwnerId: '{*************-3333-3333-************}',
        toOwnerName: 'Receiver System',
        toOwnerType: 'System',
        connectionFrequency: ['Daily', 'Weekly'],
        businessPurposeOfAddress: ['Business', 'Personal'],
        containsHealthDisparityData: true,
        typeOfData: ['Patient Data', 'Claims Data'],
        exchangeContainsCUI: false,
        exchangeCUIDescription: null,
        exchangeNetworkProtocol: ['HTTPS', 'SFTP'],
        exchangeNetworkProtocolOther: '',
      });

      expect(mocks.queryView).toHaveBeenCalledWith(
        expect.any(String), // SPARX_SYSTEM_DATAEXCHANGE constant
        expect.any(Array),
        expect.objectContaining({
          where: {
            operation: {
              column: 'Connection GUID',
              operator: '=',
              value: '{********-1111-1111-1111-************}',
            },
          },
        }),
      );
    });

    it('should return error when queryView returns an error', async () => {
      mocks.queryView.mockResolvedValue(new Error('Query View Error'));

      const result = await getDataExchangeByIdUtil(app, db, '{********-1111-1111-1111-************}');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Unable to retrieve exchange by system ID');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('should return error when queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValue(new Error('Query View Critical Error'));

      const result = await getDataExchangeByIdUtil(app, db, '{********-1111-1111-1111-************}');

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Unable to retrieve exchange by system ID');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Critical Error');
    });

    it('should handle empty results gracefully', async () => {
      mocks.queryView.mockResolvedValue([[]]);
      mocks.tryAsync.mockResolvedValue([null, [[]]]);

      const result = await getDataExchangeByIdUtil(app, db, '{********-1111-1111-1111-************}');

      expect(result).toBeUndefined();
    });
  });

  describe('mapExchangeToSPJson', () => {
    it('should map exchange payload to SP JSON correctly', () => {
      const exchangePayload: ExchangePayload = {
        exchangeId: '{********-1111-1111-1111-************}',
        exchangeName: 'Test Exchange',
        exchangeDescription: 'Test Description',
        exchangeVersion: '1.0',
        exchangeState: 'Active',
        exchangeStartDate: '2023-01-01',
        exchangeEndDate: '2023-12-31',
        exchangeRetiredDate: null,      
        fromOwnerId: '{*************-2222-2222-************}',
        fromOwnerName: 'Sender System',
        fromOwnerType: 'System',
        toOwnerId: '{*************-3333-3333-************}',
        toOwnerName: 'Receiver System',
        toOwnerType: 'System',
        connectionFrequency: ['Daily', 'Weekly'],
        dataExchangeAgreement: 'MOU',
        containsBeneficiaryAddress: true,
        businessPurposeOfAddress: ['Business', 'Personal'],
        isAddressEditable: false,
        containsPii: true,
        containsPhi: false,
        containsHealthDisparityData: true,
        containsBankingData: false,
        isBeneficiaryMailingFile: false,
        sharedViaApi: true,
        apiOwnership: 'Internal',
        typeOfData: [{ id: '{********-**************-************}', name: 'Patient Data' }],
        numOfRecords: '1000',
        dataFormat: 'JSON',
        dataFormatOther: '',
        exchangeContainsCUI: false,
        exchangeCUIDescription: null,
        exchangeCUIType: [],
        exchangeConnectionAuthenticated: true,
        exchangeNetworkProtocol: ['HTTPS', 'SFTP'],
        exchangeNetworkProtocolOther: '',
      };

      const result = mapExchangeToSPJson(exchangePayload);
      const expectedResult = {
        exchangeId: '{********-1111-1111-1111-************}',
        cms_exchange: 'Test Exchange',
        description: 'Test Description',
        name: 'Sender System1.0>>Receiver System1.0',
        objectstate: 'Active',
        startdate: '2023-01-01',
        enddate: '2023-12-31',
        fromowner: '{*************-2222-2222-************}',
        toowner: '{*************-3333-3333-************}',
        cms_connection_frequency: 'Daily|Weekly',
        cms_ie_agreement: 'MOU',
        cms_data_exch_beneficiary_add: 'true',
        cms_beneficiary_address_pur: 'Business|Personal',
        cms_adress_data_edits: 'false',
        cms_data_exchange_pii: 'true',
        cms_data_exchange_phi: 'false',
        cms_health_disparity_data: 'true',
        cms_data_exch_benef_mailing: 'false',
        cms_data_exch_api_data_share: 'true',
        cms_api_owner: 'Internal',
        typeOfData: '{********-**************-************}',
        cms_data_exch_num_recs: '1000',
        cms_data_exch_format: 'JSON',
        cms_data_exch_format_other: '',
        cms_exchange_contains_cui: 'false',
        cms_exchange_connection_authenticated: 'true',
        cms_exchange_cui_type: '',
        cms_exchange_network_protocol: 'HTTPS|SFTP',
        cms_exchange_network_protocol_other: '',
        cms_data_exchange_banking: 'false',
      };

      expect(result).toEqual(expectedResult);
    });
  });

  describe('createRelation', () => {
    it('should create a relation correctly', () => {
      const result = createRelation('1', 'From', '2');
      expect(result).toEqual({ FromId: '1', Property: 'From', ToRef: '2' });
    });
  });

  describe('wmStatusInsert', () => {
    it('should insert status correctly', async () => {
      mocks.db.db.query.mockResolvedValueOnce({ insertedIds: ['1'], affectedRows: 1 });

      const result = await wmStatusInsert(
        db,
        'SYSTEM_SURVEY_EXCHANGE_STATUS',
        [
          'EXCHANGE_ID',
          'ORIGINAL_RECEIVER_ID',
          'RECEIVER_STATUS',
          'ORIGINAL_SENDER_ID',
          'SENDER_STATUS',
          'RECEIVER_QA_STATUS',
          'RECEIVER_REPORTED_DELETE_FLAG',
        ],
        [[
          'test-exchange-id',
          'receiver-id',
          'receiver-status',
          'sender-id',
          'sender-status',
          'receiver-qa-status',
          'false',
        ]],
        'EXCHANGE_ID',
      );

      expect(result).toEqual({ insertedIds: ['1'], affectedRows: 1 });
    });
  });
});
